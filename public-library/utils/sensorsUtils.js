export function getSensorsCurrentEntInfo(headInfo) {
    const commonHeaderInfo = headInfo || {};
    const entList = commonHeaderInfo?.enterprises || [];
    const curEntId = commonHeaderInfo?.currentEntId || '';
    let entName = '';
    if (JSON.stringify(commonHeaderInfo) !== '{}' && curEntId !== '0') {
        entList.forEach((ent) => {
            if (ent?.entId === curEntId) {
                entName = ent?.entName || '';
            }
        });
    }
    return entName;
}
export function getSensorsCurrentRoleName(headInfo) {
    const commonHeaderInfo = headInfo || {};
    const curEntId = commonHeaderInfo?.currentEntId || '';
    const currentRoleName = [];
    if (JSON.stringify(commonHeaderInfo) !== '{}' && curEntId !== '0') {
        (commonHeaderInfo?.roleDetails || []).forEach((item) => {
            if (item?.entId === curEntId && !currentRoleName.includes(item?.name)) {
                currentRoleName.push(item?.name);
            }
        });
    }
    return currentRoleName;
}
