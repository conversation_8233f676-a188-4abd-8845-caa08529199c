import authPath from 'pub-utils/business/authPath.js';
export function goToAddr(item) { // 消息点击跳转详情页
    if (item.gotoType === 100) {
        return;
    } // 100则不跳转
    let url = window.location.origin;
    // 1、合同详情页面---contractid 2、用户中心页面 3、APP首页 4、普通h5页面  http链接 5、外部页面链接  http链接 6、站内h5页面，需要app监控页面事件 7、个人实名认证状态-数字证书页面 8、个人实名认证页面 9、企业实名认证页面 10、充值页面（企业）11.充值页面（个人）
    switch (item.gotoType) {
        case 1:
            url += `/doc/detail/${item.gotoAddr}`;
            break;
        case 2: url += '/usercenter/account'; break;
        case 4:
        case 5: url = item.gotoAddr; break;
        case 7:
        case 8: url += authPath(); break;
        case 9: url += '/enterprise/authentication'; break;
        case 10: url += '/console/enterprise/account/recharge'; break;
        case 11: url += '/usercenter/recharge'; break;
        case 3:
        case 6: break;
        case 12: url += '/console/enterprise/seal/manage'; break;
        case 13: url += '/console/enterprise/account/members'; break;
        case 14: url += `/reception/login?token=${item.attachment.token}&isSealRequire=true&noLogin=true`; break; // 申请印章分配
        case 15: url += `/account-center/approve-login?token=${item.attachment.token}&type=2&noLogin=true`; break; // 申请合同权限
    }
    window.open(url);
}
