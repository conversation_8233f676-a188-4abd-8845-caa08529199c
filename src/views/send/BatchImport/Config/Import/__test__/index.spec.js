import Import from '../index.vue';
import flushPromises from 'flush-promises';
import { initWrapper } from 'src/testUtils';
jest.mock('src/lang/index.js');
jest.mock('@/api/send/localFields', () => ({
    postBatchImportExcel: () => Promise.resolve({ data: {} }),
}));
jest.mock('src/utils/hybridBusiness.js', () => {
    return {
        prepareUploadRequest: jest.fn().mockResolvedValue({ data: { data: 'success' } }),
    };
});
jest.mock('@/utils/fileLimit', () => {
    return {
        checkZipUploadLimit: jest.fn(),
    };
});

import { prepareUploadRequest } from 'src/utils/hybridBusiness.js';
import { checkZipUploadLimit } from '@/utils/fileLimit';

const checkExcelHasSenderInfoFn = jest.fn();
const isGammaFn = jest.fn();
const postFn = jest.fn().mockResolvedValue({ data: 1 });
const makeHeaderFn = jest.fn().mockResolvedValue({ data: { params: '1' } });
const MessageToastSuccessFn = jest.fn();
const  MessageBoxFn = jest.fn().mockResolvedValue();
function createWrapper() {
    return initWrapper(Import, {
        modules: {
            template: {
                namespaced: true,
                module: true,
                state: {},
                getters: {
                    needImportZip() {
                        return true;
                    },
                },
                actions: {
                    checkExcelHasSenderInfo: checkExcelHasSenderInfoFn,
                },
            },
        },
        state: {
            commonHeaderInfo: {
                hybridServer: 'hybridServer',
            },
        },
    }, {
        mocks: {
            $messageBox: MessageBoxFn,
            $router: {
                push: jest.fn(),
            },
            $route: {
                params: {
                    draftId: '10000',
                },
                query: {
                    isBatchReplace: undefined,
                },
            },
            $cookie: {
                get() {
                    return 'abc';
                },
            },
            $MessageToast: {
                success: MessageToastSuccessFn,
            },
            $hybrid: {
                offlineTip: jest.fn().mockReturnValue(true),
                makeHeader: makeHeaderFn,
                isGamma: isGammaFn,
                isGammaLocalField: jest.fn().mockImplementation(t => t === '3.0'),
            },
            $http: {
                post: postFn,
            },
        },
    });
}

describe('Excel批量导入【Import.vue】', () => {
    test('批量导入Excel后，主动查看是否全部有发件方', () => {
        const wrapper = createWrapper();
        wrapper.vm.onUploadSuccess({ existError: false, missingHeads: null, inputTable: null, recordCount: 1 });
        expect(checkExcelHasSenderInfoFn.mock.calls[0][1]).toEqual('10000');
    });
    test('压缩包上传前，校验网络，文件格式大小与类型，通过后，如果是混合云，获取混合云接口请求头', async() => {
        const wrapper = createWrapper();
        checkZipUploadLimit.mockReturnValue(true);
        const file = {

        };
        wrapper.vm.beforeUploadZip(file, '/template-api/v2/draft/10000/upload-content-files', '/templates/draft/upload-content-files');
        await flushPromises();
        expect(makeHeaderFn).toHaveBeenCalledWith({
            url: '/template-api/v2/draft/10000/upload-content-files',
            method: 'POST',
            requestData: {
                draftId: '10000',
            },
            hybridTarget: '/templates/draft/upload-content-files',
            isFormType: 1,
        });
        expect(wrapper.vm.uploadHeaders).toEqual({
            Authorization: 'bearer abc',
            params: '1',
        });
    });
    test('压缩包上传，混合云场景走混合云接口上传', async() => {
        isGammaFn.mockReturnValue(true);
        const wrapper = createWrapper();
        expect(wrapper.vm.excelUrl).toEqual(`/template-api/v2/draft/10000/generate-excel?localSupport=false&language=abc&access_token=abc`);
        const opts = {
            onSuccess: wrapper.vm.onUploadZipSuccess,
        };
        wrapper.vm.uploadRequestZip(opts, '/template-api/v2/draft/10000/upload-content-files', '/templates/draft/upload-content-files', 'zip');
        expect(prepareUploadRequest).toHaveBeenCalledWith({
            hybridServer: 'hybridServer',
            hybridTarget: '/templates/draft/upload-content-files',
            data: {
                draftId: '10000',
            },
            headers: {
                Authorization: 'bearer abc',
            },
            opts,
            noToast: 0,
        });
        await flushPromises();
        expect(wrapper.emitted('update:status')[0]).toEqual([{ documents: 'undo', excel: 'undo', zip: 'done' }]);
        expect(MessageToastSuccessFn).toHaveBeenCalledWith('batchImport.msg.success');
    });
    test('文档上传，公有云场景走共有云接口上传', () => {
        isGammaFn.mockReturnValue(false);
        const wrapper = createWrapper();
        wrapper.vm.uploadRequestZip({
            file: '1',
            data: {
                key: 'value',
            },
            onSuccess: () => {
                console.log();
            },
        }, '/template-api/v2/draft/10000/upload-content-files', '/templates/draft/upload-content-files', 'zip');
        expect(postFn).toHaveBeenCalled();
    });
    test('点击批量导入', () => {
        const wrapper = createWrapper();
        wrapper.vm.clickImport('batch');
    });
    test('上传压缩包成功', () => {
        const wrapper = createWrapper();
        wrapper.vm.onUploadZipSuccess({ data: 'success' }, 'zip');
        expect(MessageToastSuccessFn).toHaveBeenCalledWith('batchImport.msg.success');
    });
    test('上传压缩包失败', () => {
        const wrapper = createWrapper();
        wrapper.vm.onUploadZipError({ config: { url: '' }, message: '160007' }, 'zip');
        expect(MessageBoxFn).toHaveBeenCalled();
    });
    test('上传压缩包失败', () => {
        const wrapper = createWrapper();
        wrapper.vm.onUploadZipError({ config: { url: '' }, message: '160007' }, 'zip');
        expect(MessageBoxFn).toHaveBeenCalled();
    });
});
