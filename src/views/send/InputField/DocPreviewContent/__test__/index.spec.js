import { initWrapper } from '@/testUtils';
import DocPreviewContent from '../index';
import flushPromises from 'flush-promises';

jest.mock('src/lang');
jest.mock('../../FieldInputSlider', () => ({
    template: '<div></div>',
}));
jest.mock('@/api/send', () => {
    return {
        postSenderLabelInfo: () => Promise.resolve({}),
    };
});
jest.mock('src/api/send/localFields', () => ({
    postDocLabelsWithLocalFiled: () => Promise.resolve({}),
}));

describe('发件人预览填写字段界面', () => {
    const mockInfoToastFn = jest.fn();
    const mockGoFn = jest.fn();
    const dataBaseOptions = {
        directives: {
            lazy: {},
        },
        propsData: {
            docInfo: {
                documentPageMaxWidth: 1000,
                documentPages: [{
                    width: 1000,
                    height: 1500,
                }],
                labels: [{
                    pageNumber: 1,
                    labelId: '111',
                    labelType: 'TEXT',
                    labelName: 'a',
                    valueStr: '',
                    valueList: [],
                    labelPosition: {
                        width: 0.016,
                    },
                    labelExtends: {
                    },
                }, {
                    pageNumber: 1,
                    labelId: '222',
                    labelType: 'TEXT_NUMERIC',
                    labelName: '数值',
                    valueStr: '',
                    valueList: [],
                }, {
                    pageNumber: 2,
                    labelId: '333',
                    labelType: 'TEXT_NUMERIC',
                    labelName: '数值2',
                    valueStr: '',
                    valueList: [],
                }, {
                    pageNumber: 1,
                    labelId: '444',
                    labelType: 'TEXT',
                    labelName: 'a',
                    valueStr: '',
                    valueList: [],
                }, {
                    pageNumber: 1,
                    labelId: '555',
                    labelType: 'MULTIPLE_BOX',
                    labelName: '复选框',
                    valueStr: '',
                    valueList: [],
                }, {
                    pageNumber: 1,
                    labelId: '666',
                    labelType: 'BIZ_DATE',
                    labelName: '日期',
                    valueStr: '22 January,2023',
                    valueList: ['22 January', '2023'],
                    labelExtends: {
                        dateFieldFormat: 'dd MMMM yyyy',
                    },
                }],
            },
            draftId: '11111',
        },
        mocks: {
            $MessageToast: {
                info: mockInfoToastFn,
            },
            $router: {
                go: mockGoFn,
            },
            $route: {
                query: {
                    sendType: 'template',
                },
            },
            $localStorage: {
                get() {
                    return [];
                },
            },
        },
    };
    const wrapper = initWrapper(DocPreviewContent, {
        getters: {
            checkFeat: () => ({
                sendContractFromCache: true,
            }),
        },
    }, dataBaseOptions);
    test('wrapperStyleObj, zoom, labelList', () => {
        wrapper.vm.docWidth = 1200;
        expect(wrapper.vm.zoom).toEqual(1.2);
        expect(wrapper.vm.wrapperStyleObj).toEqual({
            transform: `scale(1.2)`,
            'transform-origin': 'left top',
            width: `1000px`,
        });
        expect(wrapper.vm.labelList(1).length).toEqual(5);
    });
    test('字段值更新', async() => {
        wrapper.vm.handleValueUpdate(['1', '2'], 4);
        expect(wrapper.vm.hasChange).toBeTruthy();
        expect(wrapper.vm.docInfo.labels[4].valueStr).toEqual('1,2');
        wrapper.vm.handleValueUpdate('2023-01-23', 5);
        expect(wrapper.vm.docInfo.labels[5].valueStr).toEqual('2023-01-23');
    });
    test('保存界面字段值', async() => {
        wrapper.vm.hasChange = false;
        await flushPromises();
        wrapper.vm.handleFieldsSave();
        // 无值改变时直接返回
        expect(mockGoFn).toBeCalledWith(-1);
        wrapper.vm.hasChange = true;
        const focusFieldLabelFn = jest.fn();
        wrapper.vm.$refs.fieldInputSlider.focusFieldLabel = focusFieldLabelFn;
        wrapper.vm.$refs.fieldInputSlider.validateOnSave = () => Promise.resolve({
            validateResult: false,
            firstErrorLabelId: '444',
        });
        wrapper.vm.handleFieldsSave();
        await flushPromises();
        expect(mockInfoToastFn).toBeCalledWith('configTemplate.configSenderField.fieldValidateTip');
        expect(wrapper.vm.focusLabelId).toEqual('444');
        expect(focusFieldLabelFn).toBeCalledWith('444');

        wrapper.vm.$refs.fieldInputSlider.validateOnSave = () => Promise.resolve({
            validateResult: true,
            firstErrorLabelId: '',
        });
        wrapper.vm.handleFieldsSave();
        await flushPromises();
        expect(mockGoFn).toBeCalledWith(-1);
    });
    test('滚动到指定标签位置', async() => {
        wrapper.vm.$refs.docPreviewBody = {
            offsetTop: 20,
            clientHeight: 500,
        };
        wrapper.vm.scrollToLabel({
            labelPosition: {
                y: 0.2,
                height: 0.018,
            },
            pageNumber: 1,
            labelId: '444',
        });
        expect(wrapper.vm.focusLabelId).toEqual('444');
    });
    test('是否根据进行暂存', () => {
        expect(wrapper.vm.isWebSend).toBeTruthy();
        expect(wrapper.vm.isResendDraft).toBeFalsy();
        expect(wrapper.vm.showSavePartlyBtn).toBeTruthy();
    });
});
