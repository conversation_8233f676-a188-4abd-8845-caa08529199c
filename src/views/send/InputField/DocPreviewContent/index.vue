<template>
    <div class="doc-preview-module">
        <div class="doc-preview-body" ref="docPreviewBody">
            <div class="doc-context" ref="docContext">
                <div class="doc-context__doc-pages doc-pages-wrapper" :style="wrapperStyleObj">
                    <div v-for="(page, pageIndex) in docInfo.documentPages"
                        :key="pageIndex"
                    >
                        <div class="doc-pages-wrapper__page" :style="{width: `${page.width}px`, height: `${page.height}px`}">
                            <!-- 预览图片 -->
                            <img class="image"
                                :width="page.width"
                                :height="page.height"
                                v-lazy.previewUrl="{
                                    src: `${page.highQualityPreviewUrl}${page.showHighDpiImg ? '&highQuality=true' : ''}`,
                                    split: 2,
                                    index: pageIndex,
                                    total: docInfo.documentPages.length
                                }"
                                :data-previewUrl="`${page.highQualityPreviewUrl}${page.showHighDpiImg ? '&highQuality=true' : ''}`"
                                alt=""
                            />
                            <PreviewLabels
                                v-for="(label, labelIndex) in labelList(page.pageNumber, page.attachmentId)"
                                :key="label.labelId"
                                :label-index="label.indexInDoc + 1"
                                :pageHeight="page.height"
                                :pageWidth="page.width"
                                :zoom="zoom"
                                :label="label"
                                :focusLabelId="focusLabelId"
                                :is-focus="focusLabelId === label.labelId"
                                @onClick="handleClickLabel(label, labelIndex)"
                                @updataValue="handleValueUpdate($event, label.indexInDoc)"
                            >
                            </PreviewLabels>
                        </div>
                        <!-- 页脚 -->
                        <p class="doc-pages-wrapper__page-footer"
                            :key="pageIndex+'size'"
                            :style="{width:
                                `${page.width}px`}"
                        >{{ page.documentName }}
                            <span class="fr">{{ $t('pointPositionDoc.pageTip', { pageNum: pageIndex + 1, pageSize: docInfo.documentPages.length}) }}</span>
                            <CommonTip class="item"
                                effect="dark"
                                placement="top"
                                :content="$tc('pointPositionDoc.viewHighDpiImg', page.showHighDpiImg ? 0
                                    : 1)"
                            >
                                <i slot="reference"
                                    :class="page.showHighDpiImg ? 'switch-icon el-icon-zoom-out' :
                                        'switch-icon el-icon-zoom-in'"
                                    @click="switchToHighDpi(page)"
                                ></i>
                            </CommonTip>
                        </p>
                    </div>
                </div>

            </div>
        </div>
        <FieldInputSlider ref="fieldInputSlider"
            :labels="docInfo.labels"
            :focusLabelId="focusLabelId"
            :datafld="draftId"
            @updataValue="handleValueUpdate"
            @jumpToLabel="scrollToLabel"
        ></FieldInputSlider>
    </div>
</template>

<script>
import PreviewLabels from 'components/labels/PreviewLabels';
import FieldInputSlider from '../FieldInputSlider';
import { scrollToYSmooth } from 'pub-utils/dom';
import { postSenderLabelInfo } from '@/api/send';
import { dateConvertToStandard } from 'utils/docDataResolve';
import { postDocLabelsWithLocalFiled } from 'src/api/send/localFields';
import { mapGetters } from 'vuex';
export default {
    name: 'DocPreviewContent',
    components: {
        PreviewLabels,
        FieldInputSlider,
    },
    props: {
        docInfo: {
            type: Object,
            default: () => ({
                documentPageMaxWidth: 0,
                documentPages: [],
            }),
        },
        draftId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            docWidth: 0,
            focusLabelId: '', // 高亮标记
            hasChange: false,
            isCancel: this.$route.query.isCancel === 'true', // 是否是作废合同
            isDynamic: this.$route.query.isDynamic === 'true',
            isOfdTemplate: this.$route.query.isOfdTemplate === 'true',
            isDataBoxPreview: this.$route.query.isDataBoxPreview === 'true', // 是否是档案+合同预览
            isWebSend: this.$route.query.sendType === 'template', // 是否web使用模板发送
        };
    },
    computed: {
        ...mapGetters(['checkFeat']),
        wrapperStyleObj() {
            return {
                transform: `scale(${this.zoom})`,
                'transform-origin': this.$i18n.locale === 'ar' ? 'right top' : 'left top',
                width: `${this.docInfo.documentPageMaxWidth}px` };
        },
        zoom() { // 合适的缩放
            return this.docWidth / (this.docInfo.documentPageMaxWidth || 1);
        },
        // 某一页的标签
        labelList() {
            return (pageNumber, attachmentId) => {
                return this.docInfo.labels.filter(label => {
                    const isPageNumber = label.pageNumber === pageNumber;
                    if (attachmentId || label.attachmentId) { // 在附页上或者附件的标签
                        return (label.attachmentId === attachmentId) && isPageNumber;
                    }
                    return isPageNumber;
                });
            };
        },
        isResendDraft() {
            const resendDraftIdList = this.$localStorage.get('resendDraftIdList');
            return resendDraftIdList && resendDraftIdList.includes(this.draftId);
        },
        showSavePartlyBtn() {
            // 乐高城开启开关，ofd、作废、重新发起、本地发起不支持，sso发送不支持
            return this.checkFeat.sendContractFromCache && !this.isCancel && !this.isOfdTemplate && !this.isResendDraft &&
                !this.isNew && this.isWebSend;
        },
    },
    methods: {
        // 查看高清图片
        switchToHighDpi(page) {
            page.showHighDpiImg = !page.showHighDpiImg;
        },
        // 外层调用，保存界面字段值
        async handleFieldsSave(isLocalFieldScene) {
            if (!this.hasChange) {
                this.$router.go(-1);
                return;
            }
            const { validateResult, firstErrorLabelId } = await this.$refs.fieldInputSlider.validateOnSave();
            // 存在校验不通过的项
            if (!validateResult) {
                this.$MessageToast.info(this.$t('configTemplate.configSenderField.fieldValidateTip'));
                this.handleClickLabel({ labelId: firstErrorLabelId }); // 右侧边栏滚动到信息异常处
                return;
            }
            this.submitFieldsInfo({ isLocalFieldScene });
        },
        submitFieldsInfo({ isLocalFieldScene, savePartly }) {
            const params = this.docInfo.labels.map(label => ({
                labelId: label.labelId,
                // 图片字段保存时，只存id
                value: this.formatValue(label),
                name: label.labelName,
                saveLocation: label.saveLocation || 'LOCAL',
            }));

            (isLocalFieldScene ? postDocLabelsWithLocalFiled(this.draftId, params, savePartly)
                : postSenderLabelInfo(this.draftId, params, savePartly)).then(() => {
                this.hasChange = false; // 保存后移除change标记
                if (!savePartly) {
                    this.$router.go(-1);
                }
            });
        },
        formatValue(label) {
            if (!label.valueStr) {
                return label.valueStr;
            }
            switch (label.labelType) {
                case 'PICTURE':
                    return label.valueStr.split('/').pop();
                case 'BIZ_DATE':
                    return dateConvertToStandard(label.valueStr, label.labelExtends.dateFieldFormat, '-');
                default:
                    return label.valueStr;
            }
        },
        handleValueUpdate(val, index) {
            this.hasChange = true;
            const label = this.docInfo.labels[index];
            let updateValue = val;
            switch (label.labelType) {
                case 'MULTIPLE_BOX':
                    updateValue = val.join(',');
                    break;
                // case 'TEXT':
                // case 'TEXT_NUMERIC':
                // case 'NUMERIC_VALUE':
                // case 'BIZ_DATE':
                default:
                    break;
            }
            this.updateCommonField(label, updateValue);
        },
        updateCommonField(label, val) {
            this.docInfo.labels.forEach(oldLabel => {
                if (oldLabel.labelName === label.labelName) {
                    oldLabel.valueStr = val;
                }
            });
        },
        handleClickLabel({ labelId }) {
            this.focusLabelId = labelId;
            this.$refs.fieldInputSlider.focusFieldLabel(labelId);
        },
        scrollToLabel(label) {
            const { labelPosition, pageNumber, labelId } = label;
            this.focusLabelId = labelId;
            const documentsContentDom = this.$refs.docPreviewBody;
            const pageHeight = this.sumAryHeight(this.docInfo.documentPages, pageNumber - 1);
            const labelY = (1 - labelPosition.y - labelPosition.height) * this.docInfo.documentPages[pageNumber - 1].height;
            scrollToYSmooth(documentsContentDom, (pageHeight + labelY) * this.zoom);
        },
        // 计算在这份文档内，在index对应的页之前的所有页数高度之和
        sumAryHeight(ary, index) {
            const sliceAry = ary.slice(0, index);
            sliceAry.push({ height: 0 });
            const res = parseFloat(this.accHeight(sliceAry));
            sliceAry.splice(sliceAry.length - 1, 1);
            return res;
        },
        /**
         * @param  {Array} ary 数组
         * @return {Number} 数组项height之和
         */
        accHeight(ary) {
            return ary.length ? ary.reduce((a, b) => {
                return {
                    height: parseFloat(a.height) + parseFloat(b.height) + 20, // 20为文档页之间的spaceheight
                };
            }).height : 0;
        },
    },
    mounted() {
        this.docWidth = this.$refs.docContext.getBoundingClientRect().width;
        const _this = this;
        window.onresize = () => {
            _this.docWidth = _this.$refs.docContext.getBoundingClientRect().width;
        };
    },
    beforeDestroy() {
        window.onresize = null;
    },
};
</script>

<style lang="scss">
.doc-preview-module {
    height: 100%;
    position: relative;
    overflow: hidden;
    .doc-preview-body {
        height: 100%;
        margin: 0 210px;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 0 50px;
        .doc-context {
            position: relative;
            display: flex;
            flex: 1;
            flex-direction: column;
            margin-top: 10px;
            &__doc-title {
                height: 40px;
                line-height: 40px;
                font-size: 14px;
                text-align: center;
            }
            &__doc-pages {
                .doc-pages-wrapper {
                    &__page {
                        position: relative;
                    }
                    &__page-footer {
                        font-size: 12px;
                        line-height: 20px;
                        color: $--color-text-secondary;

                        .switch-icon {
                            float: right;
                            line-height: 20px;
                            padding-right: 5px;
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }
}
</style>
