<template>
    <!-- 文件管理侧边导航栏 -->
    <div class="doc-slider__common" ref="docSlider">
        <!-- 如果存在这个 dom，则 doc-slider-inner 高度是 100% -->
        <div class="doc-slider-inner">

            <div
                class="doc-start-sign-box"
                v-if="!ssoDoc.doc_2_crt || ssoDoc.doc_2_crt.visible"
            >
                <el-button
                    v-if="entFolderInfo.entFolderSimpleInfoVOS && entFolderInfo.entFolderSimpleInfoVOS.length"
                    class="doc-start-sign-btn"
                    @click="goEntFolder"
                >
                    {{ $t("docSlider.performanceManage") }}

                </el-button>
                <el-button
                    v-else
                    class="doc-start-sign-btn"
                    @click="handleStartSign"
                    :disabled="startSignDisable"
                >
                    {{ $t("docSlider.initSign") }}
                </el-button>
            </div>
            <el-menu
                :default-active="defaultActive"
                class="items-container"
                :default-openeds="['1', '2', '3']"
            >
                <el-menu-item-group>
                    <el-menu-item
                        v-for="item in folderObject.presetFolders"
                        :key="item.folderId"
                        :index="item.folderId"
                        @click="handleFolderClick(item, 'preset')"
                        :disabled="tableLoading"
                    >
                        {{ item.folderName }}
                    </el-menu-item>
                    <el-menu-item
                        v-if="checkFeat.contractConfidentiality"
                        @click="handleSafeBoxClick"
                    >
                        {{ $t('safeBox.safeBox') }}
                    </el-menu-item>
                </el-menu-item-group>

                <div class="divider"></div>
                <el-submenu index="1">
                    <template slot="title">
                        <!-- 快捷入口 -->
                        {{ $t("docSlider.fastOperate") }}
                        <i
                            class="el-icon-ssq-shezhi"
                            @click.prevent.stop="handleShortcut"
                        ></i>
                    </template>
                    <el-menu-item-group>
                        <el-menu-item
                            v-for="item in shortcutList"
                            :key="item.shortcutId"
                            :index="item.shortcutId"
                            @click="handleFolderClick(item, 'quick')"
                            :disabled="tableLoading"
                        >
                            <span class="doc-slider-shortcut-name" :title="item.name">{{ item.name }}</span>
                            <em v-if="!hasShortcutsNum"><i class="el-icon-loading"></i></em>
                            <em v-else>{{
                                item.contractsCount > 1000000 ? "100w+" : item.contractsCount
                            }}</em>
                        </el-menu-item>
                    </el-menu-item-group>
                </el-submenu>

                <div class="divider"></div>
                <el-submenu index="2">
                    <template slot="title">
                        <div class="doc-slider-folder-manager">
                            {{ $t("docSlider.folder") }}
                            <span class="doc-slider-folder-manager-block">
                                <i v-if="checkTrialInfoStatus('81',2)" class="el-icon-ssq-biaoqiannew right-new">
                                </i>
                                <i class="el-icon-ssq-jia"
                                    @mouseover="isToolTipShow = true"
                                    @mouseenter="isToolTipShow = true"
                                    @mousemove="isToolTipShow = true"
                                    @mouseout="handleHideTrialTip"
                                    @mouseleave="handleHideTrialTip"
                                ></i>
                                <ul class="folderManagerOperate">
                                    <AdvancedTooltip
                                        featureId="81"
                                        :showToolTip="isToolTipShow"
                                        :showNew="false"
                                    >
                                        <template slot="tipContent">
                                            <li
                                                @mouseover="isToolTipShow = true"
                                                @mouseenter="isToolTipShow = true"
                                                @mousemove="isToolTipShow = true"
                                                @mouseout="handleHideTrialTip"
                                                @mouseleave="handleHideTrialTip"
                                                @click.prevent.stop="handleEditFolder"
                                            >
                                                {{ $t("docSlider.addFolder") }}
                                            </li>
                                        </template>
                                    </AdvancedTooltip>
                                    <li @click.prevent.stop="$emit('showDialog', 'ArchiveRule')" v-if="checkFeat.autoArchive">
                                        {{ $t("docSlider.autoArchive") }}
                                    </li>
                                </ul>
                            </span>
                        </div>
                    </template>
                    <el-menu-item-group>
                        <el-menu-item
                            v-for="(item, index) in folderObject.myFolders"
                            :key="item.folderId"
                            :index="item.folderId"
                            class="folder-item"
                            @click="handleFolderClick(item, 'custom')"
                            :disabled="tableLoading"
                        >
                            <span class="doc-slider-folder-name">{{ item.folderName }}</span>
                            <span class="folderOperateBlock" @click.prevent.stop>
                                <i :class="`folderOperateIcon  ${item.syncEntFolder?'el-icon-ssq-tongbuzhiqiyewenjianjia' : 'el-icon-ssq-gengduo'}`"></i>

                                <ul class="folderHiddenOperate">
                                    <li @click.prevent.stop="handleEditFolder(item)">
                                        {{ $t("docSlider.rename") }}
                                    </li>
                                    <li @click.prevent.stop="handleDeleteFolder(item, index)">
                                        {{ $t("docSlider.delete") }}
                                    </li>

                                    <li v-if="entFolderInfo.syncPermission" @click.prevent.stop="handleSyncEntFolder(item)">{{
                                        item.syncEntFolder? $t('docSlider.stopSyncEntFolder'):$t('docSlider.syncEntFolder') }}</li>
                                    <li
                                        v-if="index"
                                        @click.prevent.stop="handleStick(item, index)"
                                    >
                                        {{ $t("docSlider.stick") }}
                                    </li>
                                </ul>
                            </span>
                        </el-menu-item>
                    </el-menu-item-group>
                </el-submenu>

                <div class="divider"></div>
                <el-submenu index="3" v-if="hasShareFolder">
                    <template slot="title">
                        <!-- 共享文件夹 -->{{ $t('docSlider.sharedFolder') }}
                        <CommonTip
                            class="item"
                            effect="dark"
                            :content="$t('docSlider.shareToMeFolder')"
                            placement="right"
                            :visible-arrow="false"
                        />
                    </template>
                    <el-menu-item-group>
                        <el-menu-item
                            v-for="item in folderObject.sharedFolders"
                            :key="item.folderId"
                            :index="item.folderId"
                            class="folder-item"
                            @click="handleFolderClick(item, 'share')"
                            :disabled="tableLoading"
                        >
                            <span class="doc-slider-folder-name">{{ item.folderName }}</span>
                        </el-menu-item>
                    </el-menu-item-group>
                </el-submenu>
            </el-menu>
        </div>
        <ChooseEntFolder v-if="showChooseEntFolder" @close="showChooseEntFolder=false" @confirm="doGoEntFolder"></ChooseEntFolder>
        <SyncEntFolder v-if="syncEntFolderDialog.show" @close="syncEntFolderDialog.show=false" :folderId="syncEntFolderDialog.folderId" @create="showAddEntFolder = true"></SyncEntFolder>
        <AddEntFolder
            v-if="showAddEntFolder"
            @close="showAddEntFolder=false"
            :entList="addFolderEntList"
            @confirm="handleAddEntFolder"
        ></AddEntFolder>
        <!-- sso 提示弹窗 -->
        <SsoNotBelongToEntDialog
            :params="ssoParams"
            @closeDialog="handleCloseSSoDialog"
        />
        <SafeBoxListDialog :visible.sync="showSafeBoxListDialog" :safeBoxData="safeBoxData" @updateSafeBoxList="updateSafeBoxList" />
    </div>
</template>

<script>
import SsoNotBelongToEntDialog from 'pub-businessComponents/ssoNotBelongToEntDialog';
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
import openPageAfterAjax from 'pub-utils/linkOpener.js';
import ChooseEntFolder from '../Dialogs/ChooseEntFolder';
import SyncEntFolder from '../Dialogs/SyncEntFolder';
import AddEntFolder from 'components/addEntFolder';
import SafeBoxListDialog from 'components/safeBoxListDialog';
import { stopSyncToEntFolder, selectShortcut } from 'src/api/docManage/detail.js';
import { advancedFeatureMixin } from 'pub-mixins/advancedFeature.js';
import { jumpToSendMixin } from 'mixins/jumpToSend';
import AdvancedTooltip from 'pub-businessComponents/advanceTooltip/index.vue';
export default {
    components: {
        ChooseEntFolder,
        SyncEntFolder,
        AddEntFolder,
        SsoNotBelongToEntDialog,
        SafeBoxListDialog,
        AdvancedTooltip,
    },
    mixins: [advancedFeatureMixin, jumpToSendMixin],
    props: {
        isGroupProxyAuthStatus: {
            type: Boolean,
        },
    },
    data() {
        return {
            startSignDisable: false,
            ssoDoc: {}, // sso配置合同管理页面
            showChooseEntFolder: false,
            syncEntFolderDialog: {
                show: false,
                folderId: '',
            },
            showAddEntFolder: false,
            ssoParams: {
                visible: false,
                developerName: '',
            },
            showSafeBoxListDialog: false,
            safeBoxData: {},
            isToolTipShow: false, // 高级功能试用tooltip是否显示
        };
    },
    computed: {
        ...mapState(['commonHeaderInfo']),
        ...mapGetters([
            'getUserType',
            'getSsoConfig',
            'checkFeat',
            'getAuthStatus',
            'getUserPermissons',
            'getCurrentEntInfo',
            'isPerson',
            'getIsForeignVersion',
        ]),
        ...mapGetters('doc', ['slideMenuName']),
        ...mapState('doc', {
            searchEntryParams: (state) => state.searchParams.searchEntryParams,
            folderObject: (state) => state.folderObject,
            shortcuts: (state) => state.shortcuts,
            entFolderInfo: (state) => state.entFolderInfo,
            hasShortcutsNum: (state) => state.hasShortcutsNum,
            tableLoading: state => state.tableLoading,
        }),
        addFolderEntList() {
            return this.entFolderInfo.entFolderSimpleInfoVOS.filter(item => item.createPermission).map(item =>  {
                return {
                    entId: item.entId,
                    entName: item.entOrGroupName,
                };
            });
        },
        defaultActive() {
            return (
                this.searchEntryParams.folderId || this.searchEntryParams.shortcutId
            );
        },
        shortcutList() {
            return this.shortcuts
                .filter((item) => item.display)
                .sort((a, b) => a.order - b.order);
        },
        hasShareFolder() {
            return this.folderObject.sharedFolders?.length;
        },
    },
    methods: {
        ...mapMutations('doc', ['switchFolder']),
        ...mapMutations(['setHeaderSelectCompanyVisible']),
        ...mapActions('doc', [
            'queryFolderList',
            'updateShareFolder',
            'queryShortCuts',
        ]),
        sendDocManageSlidePoint(iconName) {
            this.$sensors.track({
                eventName: 'Ent_ContractManageList_BtnClick',
                eventProperty: {
                    page_name: '合同管理列表页',
                    first_category: '目录栏',
                    index_name: this.slideMenuName,
                    folder_name: this.slideMenuName,
                    icon_name: iconName,
                },
            });
        },
        // tooltip消失
        handleHideTrialTip() {
            setTimeout(() => {
                this.isToolTipShow = false;
            }, 3000);
        },
        async handleAddEntFolder(entId) {
            // 新建的企业文件夹对应主体非当前主体需要切换主体后跳转
            if (this.getCurrentEntInfo.entId !== entId) {
                await this.$http.switchEntId(entId);
            }
            this.$router.push('/ent-doc-manage/ent-folder');
        },
        // 点击进入企业文件
        async goEntFolder() {
            this.sendDocManageSlidePoint('进入企业履约管理');
            if (this.entFolderInfo.entFolderSimpleInfoVOS.length > 1) {
                return this.showChooseEntFolder = true;
            }
            this.doGoEntFolder(this.entFolderInfo.entFolderSimpleInfoVOS[0].entId);
        },
        async doGoEntFolder(entOrGroupId) {
            const loading = this.$loading();
            try {
                await this.$http.switchEntId(entOrGroupId);
                loading.close();
                this.$router.push(`/ent-doc-manage/ent-folder`);
            } catch (err) {
                loading.close();
            }
        },
        // 去发起本地文件页面
        handleToUpload() {
            this.startSignDisable = true;
            this.$loading();
            this.$http
                .post('/contract-api/contracts')
                .then((res) => {
                    openPageAfterAjax(`${this.GLOBAL.rootPathName}/sign/prepare?contractId=${res.data.contractId}&from=doc`);
                })
                .finally(() => {
                    this.$loading().close();
                    this.startSignDisable = false;
                });
        },
        // 企业账号判断发起权限
        judgeSendPermission() {
            // 本地发起权限
            const localPermis = this.getUserPermissons.sendLocal;
            // 是否有模板权限
            const templatePermis = this.checkFeat.template;

            // 无模板和本地发起权限，弹窗提示既没有上传本地文档权限也没有模板权限
            if (!localPermis && !templatePermis) {
                return this.$MessageToast.info(this.$t('docSlider.assignAuthority'));
            }
            // 同时具有本地和模板发起权限，弹窗选择本地/模板
            if (localPermis && templatePermis) {
                return this.$emit('showDialog', 'SelectSignType', {
                    visible: true,
                });
            }
            // 如果有本地发起权限，但无模板权限，直接发起本地文件
            if (localPermis && !templatePermis) {
                return this.handleToUpload();
            }
            // 无本地发起权限，但有模板权限，直接去模板管理页
            if (!localPermis && templatePermis) {
                location.href = `${this.GLOBAL.rootPathName}/template/list`;
            }
        },
        // SAAS-28647 有企业主体的账号sso登录，切换至个人的时候二次提醒
        ssoSendConfirm() {
            return new Promise((resolve, reject) => {
                this.$confirm(`
                <h6>${this.$t('ssoSendDialog.main')}</h6>
                <p>${this.$t('ssoSendDialog.tip')}</p>
                `, this.$t('ssoSendDialog.title'), {
                    confirmButtonText: this.$t('ssoSendDialog.confirm'),
                    cancelButtonText: this.$t('ssoSendDialog.cancel'),
                    dangerouslyUseHTMLString: true,
                    type: 'warning',
                    customClass: 'sso-switch-person-dialog',
                    distinguishCancelAndClose: true,
                    beforeClose: (action, instance, done) => done(action),
                }).then(() => {
                    this.setHeaderSelectCompanyVisible(true);
                    reject();
                }).catch(action => {
                    if (action !==  'close') {
                        resolve();
                    }
                });
            });
        },
        handleCloseSSoDialog() {
            this.ssoParams.visible = false;
        },
        // 发起签约
        async handleStartSign() {
            // 如果是单点登录
            if (this.commonHeaderInfo.extendFields.isLoginFromDeveloper) {
                // 判断当前用户是否属于开发者企业/集团子公司
                this.$http.get('/ents/belong-to-developer').then((res) => {
                    this.ssoParams.developerName = res.data.developerName || '';
                    // 用户不属于开发者 企业/集团子公司
                    if (!res.data.ifBelong) {
                        this.ssoParams.visible = true;
                    } else {
                        this.startSign();
                    }
                }).catch(() => {
                    this.startSign();
                });
            } else {
                await this.startSign();
            }
        },
        async startSign() {
            if (this.getAuthStatus !== 2 || this.isGroupProxyAuthStatus) {
                // 身份未认证，发起签约时提示
                this.$emit('showDialog', 'UnverifyConfirmDialog', {
                    visible: true,
                });
                return;
            }
            if (this.commonHeaderInfo.enterprises.length > 1 && this.isPerson) {
                await this.ssoSendConfirm();
            }
            this.handleJudgeUserType();
        },
        async jumpToNewTemplate() {
            try {
                await this.handleToSendPage();
            } catch (e) {
                console.log(e);
            }
        },
        // 发起签约判断用户类型
        handleJudgeUserType() {
            // 开启新合同后，直接走新合同跳转逻辑
            if (this.$store.state.commonHeaderInfo.openNewContractTemplate) {
                this.jumpToNewTemplate();
            } else {
                // 企业是否有权限，个人直接发起
                this.getUserType === 'Enterprise'
                    ? this.judgeSendPermission()
                    : this.handleToUpload();
            }
        },
        async updateSafeBoxList(param) {
            const { data: { data: safeBoxData } } = await this.getSafeBoxList({ currentPage: param.currentPage, pageSize: param.pageSize });
            this.safeBoxData = safeBoxData?.records.length ? safeBoxData : { records: [] };
        },
        getSafeBoxList({ currentPage, pageSize }) {
            return this.$http.post(`contract-search/safe-box/search?currentPage=${currentPage}&pageSize=${pageSize}`);
        },
        async handleSafeBoxClick() {
            this.updateSafeBoxList({ currentPage: 1, pageSize: 10 });
            this.showSafeBoxListDialog = true;
        },
        // 点击切换当前文件夹
        handleFolderClick(folder, type) {
            const folderMap = {
                '10000': '发件箱',
                '20000': '收件箱',
                '40000': '回收站',
            };
            const { folderId, shortcutId, folderName, name } = folder;
            let iconName = '';
            if (type === 'preset') {
                iconName = folderMap[folderId];
            } else {
                iconName = folderName || name;
            }
            this.sendDocManageSlidePoint(iconName);
            const {
                folderId: activeFolderId,
                shortcutId: activeShortcutId,
            } = this.searchEntryParams;
            if (
                (folderId && folderId === activeFolderId) ||
                (shortcutId && shortcutId === activeShortcutId)
            ) {
                return;
            }
            // 记录选中的快捷入口
            shortcutId && selectShortcut(shortcutId);
            this.switchFolder(folder);
            this.$emit('switchFolder', folder);
        },
        // 编辑（新建重命名）文件夹
        handleEditFolder(folder) {
            let params = {
                title: this.$t('docSlider.addFolder'),
            };
            if (folder && folder.folderId) {
                params = {
                    ...folder,
                    title: this.$t('docSlider.rename'),
                };
            } else {
                // 新建文件夹 => 高级功能未开启，提示
                if (!this.checkFeatureCanUseInfo('81')) {
                    return;
                }
            }
            this.$emit('showDialog', 'EditFolder', params);
        },
        // 停止同步至企业文件夹
        handleStopSyncToEntFolder(folder) {
            const loading = this.$loading();
            stopSyncToEntFolder(folder.folderId).then(() => {
                this.$MessageToast.info('已停止同步');
                this.queryFolderList();
            }).finally(() => {
                loading.close();
            });
        },
        handleSyncEntFolder(folder) {
            if (folder.syncEntFolder) {
                this.handleStopSyncToEntFolder(folder);
            } else {
                this.syncEntFolderDialog = {
                    show: true,
                    folderId: folder.folderId,
                };
            }
        },
        // 编辑快捷入口
        handleShortcut() {
            this.$emit('showDialog', 'Shortcut', {});
        },
        // 删除文件夹二次确认
        handleDeleteFolder({ folderId }, index) {
            this.$confirm(
                this.$t('docSlider.deleteFolderTips.title'), // 删除后，文件夹中的合同会移到"所有合同"中，确认删除吗？
                this.$t('docSlider.deleteFolderTips.delete'), // 删除
                {
                    confirmButtonText: this.$t('docSlider.deleteFolderTips.confirm'), // 确定
                    cancelButtonText: this.$t('docSlider.deleteFolderTips.cancel'), // 取消
                    type: 'warning',
                    customClass: 'message-box-confirm-custom',
                },
            )
                .then(() => {
                    const { myFolders, presetFolders } = this.folderObject;
                    this.$http
                        .delete(`/contract-center-bearing/folder/${folderId}`)
                        .then(() => {
                            myFolders.splice(index, 1);
                            // 如果删除当前文件夹则跳转到默认的第一个文件夹，如果没有文件夹了就默认切换到发件箱
                            if (this.searchEntryParams.folderId === folderId) {
                                this.switchFolder(
                                    myFolders.length ? myFolders[0] : presetFolders[0],
                                );
                            }
                            this.queryFolderList();
                        });
                })
                .catch(() => {});
        },
        // 共享文件夹
        // async handleShareFolder(folder) {
        //     if (folder.shareable) {
        //         return this.updateShareFolder(folder);
        //     }

        //     // 查询用户的所有企业信息
        //     const enterpriseInfo = await this.$http.get(
        //         '/contract-center/user/enterprise-info',
        //     );
        //     const {
        //         data: { data: enterpriseList },
        //     } = enterpriseInfo;
        //     if (!enterpriseList || !enterpriseList.length) {
        //         return this.updateShareFolder(folder);
        //     }
        //     const name = enterpriseList
        //         .filter((item) => item.enterpriseName !== '')
        //         .map((item) => item.enterpriseName)
        //         .join('、');
        //     // 您将分享给：${name}的所有成员
        //     this.$confirm(this.$t('docSlider.shareToMemberTips.title', { name }), this.$t('docSlider.shareToMemberTips.share'), {
        //         confirmButtonText: this.$t('docSlider.shareToMemberTips.confirm'), // 确定
        //         cancelButtonText: this.$t('docSlider.shareToMemberTips.cancel'), // 取消
        //         type: 'warning',
        //         customClass: 'message-box-confirm-custom',
        //     })
        //         .then(() => {
        //             this.updateShareFolder(folder);
        //         })
        //         .catch(() => {});
        // },
        // 置顶操作
        handleStick(folder, index) {
            if (!index) {
                return;
            }
            const oldValue = this.folderObject.myFolders.slice();
            const newValue = oldValue.slice();
            newValue.splice(index, 1);
            newValue.unshift(folder);
            this.folderObject.myFolders = newValue;
            this.$http
                .put('/contract-center-bearing/folder/order', newValue)
                .then(() => {
                    this.queryFolderList();
                })
                .catch(() => {
                    this.folderObject.myFolders = oldValue;
                });
        },
    },
    beforeMount() {
        this.ssoDoc = this.getSsoConfig.doc || {};
    },
};
</script>

<style lang="scss">
.doc-slider__common {
    float: left;
    width: 226px;
    height: 100%;
    background: $--color-white;
    border-right: 1px solid $--border-color-light;
    overflow-y: auto;
    .doc-start-sign-box {
        padding: 10px;
        .doc-start-sign-btn {
            width: 100%;
            color: $--color-white;
            border: none;
            background: $theme-color;
            border-radius: 2px;
            cursor: pointer;
            span {
                font-size: 14px;
            }
            &:hover {
                background: $theme-color-light;
            }
        }
    }
    .items-container {
        padding-bottom: 50px;
        color: $--color-text-primary;
        background: $--color-white;
        border-right: 0;
        .el-menu-item {
            position: relative;
            color: $--color-text-primary;
            height: 40px;
            line-height: 40px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 20px !important;
            padding-left: 40px !important;
            font-size: 12px;
            [dir='rtl'] & {
                padding-right: 40px !important;
                padding-left: 20px !important;
            }
            &.is-disabled{
                opacity: 1;
            }
            &:hover,
            &.is-active {
                background: $--background-color-regular;
            }

            &:hover {
                .folderOperateBlock {
                    display: inline-block;
                }
            }

            i {
                margin-right: 0;
                color: $--color-text-secondary;
                font-size: 18px;
                vertical-align: middle;
                text-align: left;
            }
            .doc-slider-shortcut-name {
                display: inline-block;
                width: 100px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: unset !important;
            }
            em {
                float: right;
                white-space: nowrap;
                overflow: hidden;
                font-weight: normal;
                font-size: 12px;
                max-width: 60px;
                color: $--color-text-secondary;
                text-overflow: ellipsis;
              i{
                text-align: center;
              }
            }
            .doc-slider-folder-name {
                display: inline-block;
                width: 108px;
                text-overflow: ellipsis;
                overflow: hidden;
            }
            .el-icon-ssq-fenxiang {
                color: $theme-color;
                font-size: 12px;
                width: 12px;
            }
            .folderOperateBlock {
                display: none;
                position: absolute;
                right: 20px;

                .folderHiddenOperate {
                    display: none;
                    position: absolute;
                    right: -14px;
                    top: 29px;
                    background: $--color-white;
                    text-align: center;
                    z-index: 1;
                    min-width: 84px;
                    padding: 0 10px;
                    box-shadow: 0 2px 4px $--border-color-base;

                    li {
                        text-align: center;
                        height: 30px;
                        line-height: 30px;
                        color: $--color-text-primary;

                        &:hover {
                            background: $--background-color-base;
                        }
                    }
                }
                &:hover {
                    .folderHiddenOperate {
                        display: block;
                    }
                    .folderOperateIcon {
                        color: $theme-color-light;
                    }
                }
                [dir='rtl'] & {
                    right: unset;
                    left: 20px;
                    .folderHiddenOperate {
                        right: auto;
                        left: 0;
                    }
                }
            }
        }
        .el-menu-item-group__title {
            display: none;
        }
        .el-submenu {
            .el-menu {
                background: $--color-white;
            }
            .el-submenu__title {
                padding-left: 40px !important;
                height: 36px;
                line-height: 36px;
                color: $--color-text-primary;
                font-size: 12px;
                [dir='rtl'] & {
                    padding-right: 40px !important;
                    padding-left: 20px !important;
                }

                &:hover {
                    background: none;
                }

                .el-submenu__icon-arrow {
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    left: 20px;
                    right: unset;
                    transform: rotate(-90deg);

                    &:before {
                        font-family: "iconfont",sans-serif !important;
                        content: "\e647";
                    }
                    [dir='rtl'] & {
                        left: unset;
                        right: 20px;
                        transform: rotate(90deg);
                    }
                }

                .doc-slider-folder-manager {
                    position: relative;
                    .el-icon-ssq-jia {
                        right: 0;
                    }
                }
                .doc-slider-folder-manager-block {
                    display: inline-block;
                    position: absolute;
                    top: 0;
                    right: 0px;
                    bottom: 0;
                    [dir='rtl'] & {
                        right: unset;
                        left: 0px;
                    }

                    &:hover {
                        .el-icon-ssq-jia {
                            color: $theme-color-light;
                        }
                        .folderManagerOperate {
                            display: block;
                        }
                    }
                    .right-new{
                        margin-left: -30px;
                    }
                }
                .folderManagerOperate {
                    display: none;
                    position: absolute;
                    left: -75px;
                    top: 29px;
                    background: $--color-white;
                    z-index: 1;
                    width: 105px;
                    box-shadow: 0 2px 4px $--border-color-base;
                    [dir='rtl'] & {
                        left: unset;
                        right: -75px;
                    }
                    li {
                        text-align: center;
                        height: 30px;
                        line-height: 30px;
                        color: $--color-text-primary;
                        padding: 0 5px;
                        &:hover {
                            background: $--background-color-base;
                        }
                    }
                }

                .el-icon-ssq-shezhi {
                    position: absolute;
                    top: 50%;
                    right: 20px;
                    margin-top: -5px;
                    margin-right: -5px;
                    font-weight: bold;
                    font-size: 14px;
                    [dir='rtl'] & {
                        right: unset;
                        left: 20px;
                    }

                    &:hover {
                        color: $theme-color-light;

                    }
                }
                .el-icon-ssq-jia {
                    margin-right: 0;
                    color: #999999;
                    vertical-align: middle;
                    text-align: left;
                    width: auto;
                    font-size: 14px;
                    padding-left: 5px;
                }
                .el-icon-ssq-biaoqiannew{
                    font-size: 13px;
                    margin-top: 3px;
                }

                i {
                    color: $--color-text-secondary;
                }
            }

            &.is-opened {
                .el-submenu__title .el-submenu__icon-arrow {
                    transform: rotate(0deg);
                }
            }
        }
    }
    .divider {
        margin: 10px;
        border-bottom: 1px solid $--border-color-light;
    }
}

.el-icon-ssq-biaoqiannew{
    color: $--color-danger !important;
    font-size: 15px  !important;
    margin-bottom: 3px;
}

</style>
