<template>
    <div class="doc-detail-detailInfo">
        <h3>
            <span class="detailInfo__title">{{ title }}{{ $t("docDetail.contractDetailInfo") }} <!-- 合同详细信息 -->
                <CommonTip class="help-tip-icon">
                    <div slot="content">
                        <p>{{ $t('docDetail.contractDetailInfoTip') }}</p>
                    </div>
                </CommonTip>
            </span>
            <span class="detailInfo__title--right">
                <el-button type="text" v-if="canViewDetail && isSignerOfContract" class="doc-detail-subDetail" @click="goSubContract">{{
                    $t("docDetail.viewDetail") }}</el-button>
                <el-button type="text" v-if="!$hybrid.isGammaLocalField(localFieldSupportType) && !contractBaseInfo.ifMultiPlatformContract" class="doc-detail-field" @click="handleViewContentInfo">{{ $t("docDetail.viewContentInfo") }}</el-button>
            </span>
        </h3>
        <div class="contractInfo-container">
            <div class="contractInfo-reciviers-list">
                <Participants
                    :participants="subContractItem.participants"
                    :contractBaseInfo="contractBaseInfo"
                    :sensorsTrackContractInfo="sensorsTrackContractInfo"
                    @popShowChange="popShowChange"
                >
                </Participants>
            </div>
            <div class="contractInfo">
                <!-- 自定义描述字段 -->
                <template
                    v-if="subContractItem.canManage"
                >
                    <template v-for="(item, index) in cacheDescFields">
                        <!-- 合同到期日 -->
                        <p class="detail-info-line" :key="index" v-if="item.fieldName === 'contractContentExpireDays' && subContractItem.describeFieldDisplay.contractContentExpireDays">
                            <span class="detail-info-label">{{ $t("docDetail.contractExpireDate") }}：</span>
                            <span class="detail-info-text">
                                <span>
                                    {{ subContractItem.contractLifeEnd | formatedTime }}
                                </span>
                                <span class="detail-info-expireDate"
                                    v-if="canEdit"
                                    @click.stop="handleEditLimit(item, $t('docDetail.contractExpireDate'))"
                                >
                                    <a href="javascript:void(0)"
                                        class="common-font-color"
                                    >
                                        {{
                                            subContractItem.contractLifeEnd
                                                ? $t("docDetail.edit")
                                                : $t("docDetail.settings")
                                        }}
                                    </a>
                                    <el-date-picker
                                        type="date"
                                        v-if="item.canContractEdit !== false"
                                        v-model="fileMaturityDate"
                                        :editable="false"
                                        size="small"
                                        :picker-options="pickMaturityDate"
                                        @change="handleExpireChange"
                                    ></el-date-picker>
                                </span>
                                <a href="javascript:void(0)" v-if="subContractItem.contractLifeEnd" class="common-font-color" @click="handleExpireChange({isClear:true})">
                                    {{ $t("docDetail.clear") }}
                                </a>
                            </span>
                        </p>
                        <!-- 合同类型 -->
                        <p class="detail-info-line" :key="index" v-else-if="item.fieldName === 'contractTypeId'">
                            <span class="detail-info-label">{{ $t("docDetail.contractType") }}：</span>
                            <span v-if="!editingContractTypeVisible" class="detail-info-text">
                                {{ subContractItem.contractTypeName }}
                                <span class="editInfo-btn"
                                    v-if="canEdit"
                                    @click="showSelectContractType('contractType', undefined, item)"
                                ><!-- 修改 -->{{ $t("docDetail.modify") }}</span>
                            </span>
                            <span class="detail-info-text" v-else>
                                <el-select
                                    v-model="cacheContractTypeId"
                                    :placeholder="$t('docDetail.plsSelect')"
                                >
                                    <el-option
                                        v-for="option in contractsTypes"
                                        :key="option.contractTypeId"
                                        :label="option.contractTypeName"
                                        :value="option.contractTypeId"
                                    >
                                    </el-option>
                                </el-select>
                                <span
                                    class="detail-info__opt detail-info__opt_confirm"
                                    @click="submitCustomIdAndContractType('contractType')"
                                >
                                    <i class="el-icon-ssq-xuanzhong"></i>
                                </span>
                                <span
                                    class="detail-info__opt detail-info__opt_cancel"
                                    @click="handleResetContractType"
                                >
                                    <i class="el-icon-ssq-delete"></i>
                                </span>
                            </span>
                        </p>
                        <!-- 合同类型（备用） -->
                        <p class="detail-info-line" :key="index" v-else-if="item.fieldName === 'contractTypeIdsForApprove'">
                            <span class="detail-info-label">{{ $t("docDetail.contractTypeBackup") }}：</span>
                            <span v-if="!item.editing" class="detail-info-text">
                                {{ formatFieldValue(item.fieldName) }}
                                <!-- 这里必须用全等去比较，后端未初始化默认值，存在值null但可以编辑的情况   -->
                                <span class="editInfo-btn"
                                    v-if="canEdit"
                                    @click="showSelectContractType('contractTypeBackup', index, item)"
                                >{{ $t("docDetail.modify") }}</span>
                            </span>
                            <span class="detail-info-text" v-else>
                                <el-select
                                    v-model="item.cacheValue"
                                    :placeholder="$t('docDetail.plsSelect')"
                                >
                                    <el-option
                                        v-for="option in contractsTypes"
                                        :key="option.contractTypeId"
                                        :label="option.contractTypeName"
                                        :value="option.contractTypeName"
                                    >
                                    </el-option>
                                </el-select>
                                <span
                                    class="detail-info__opt detail-info__opt_confirm"
                                    @click="submitDescField(item, index)"
                                >
                                    <i class="el-icon-ssq-xuanzhong"></i>
                                </span>
                                <span
                                    class="detail-info__opt detail-info__opt_cancel"
                                    @click="handleResetDescField(index)"
                                >
                                    <i class="el-icon-ssq-delete"></i>
                                </span>
                            </span>
                        </p>
                        <p class="detail-info-line" :key="index" v-else-if="item.fieldName === 'customNumber' && subContractItem.describeFieldDisplay.customNumber">
                            <span v-if="subContractItem.customContractId" class="search-by-code" @click="handleSearchRelativeContract">
                                <i class="el-icon-search"></i>
                            </span>
                            <span class="detail-info-label"><!-- 公司内部编号 -->{{
                                $t("docDetail.companyInternalNum")
                            }}：</span>
                            <span v-if="!editContractCustomIdVisible" class="detail-info-text">
                                {{
                                    cacheCustomContractId || $t('docDetail.none')
                                }}
                                <!-- subContractItem.customContractId || $t("docDetail.none") -->
                                <span @click="handleResetCustomId(false, item)"
                                    v-if="canEdit"
                                    class="editInfo-btn"
                                ><!-- 修改 -->{{ $t("docDetail.modify") }}</span>
                            </span>
                            <span class="detail-info-text" v-else>
                                <el-input
                                    v-model="cacheCustomContractId"
                                    :placeholder="$t('docDetail.plsInputCompanyInternalNum')"
                                ></el-input>
                                <span
                                    class="detail-info__opt detail-info__opt_confirm"
                                    @click="submitCustomIdAndContractType('customId')"
                                >
                                    <i class="el-icon-ssq-xuanzhong"></i>
                                </span>
                                <span
                                    class="detail-info__opt detail-info__opt_cancel"
                                    @click="handleResetCustomId"
                                >
                                    <i class="el-icon-ssq-delete"></i>
                                </span>
                            </span>
                        </p>
                        <p
                            v-else-if="!['contractContentExpireDays', 'contractTitle', 'contractTypeId', 'customNumber', 'signExpireDays'].includes(item.fieldName)"
                            :key="index"
                            class="detail-info-line"
                        >
                            <span class="detail-info-label"><span class="must" v-if="item.necessary">*</span>{{ item.fieldName }}：</span>
                            <span v-if="!item.editing" class="detail-info-text">
                                <!-- {{ item.fieldValue || $t("docDetail.none") }} -->
                                {{
                                    formatFieldValue(item.fieldName)
                                }}
                                <span @click="editDescField(index, item)"
                                    v-if="canEdit"
                                    class="editInfo-btn"
                                ><!-- 修改 -->{{ $t("docDetail.modify") }}</span>
                            </span>
                            <span class="detail-info-text" v-else>
                                <el-select
                                    v-if="item.bizFieldType === 'SINGLE_BOX'"
                                    v-model="item.cacheValue"
                                    popper-class="sign-el-select"
                                >
                                    <el-option
                                        v-for="(option, indx) in item.buttons"
                                        :key="indx"
                                        :label="option"
                                        :value="option"
                                    >
                                    </el-option>
                                </el-select>
                                <el-date-picker
                                    v-else-if="item.bizFieldType === 'BIZ_DATE'"
                                    type="date"
                                    :value="item.cacheValue"
                                    @input="handleDescFieldFormatDate(item, $event)"
                                    :editable="false"
                                    size="mini"
                                ></el-date-picker>
                                <el-input
                                    v-else
                                    v-model="item.cacheValue"
                                    :placeholder="$t('docDetail.plsInput')"
                                ></el-input>
                                <span
                                    class="detail-info__opt detail-info__opt_confirm"
                                    @click="submitDescField(item, index)"
                                >
                                    <i class="el-icon-ssq-xuanzhong"></i>
                                </span>
                                <span
                                    class="detail-info__opt detail-info__opt_cancel"
                                    @click="handleResetDescField(index)"
                                >
                                    <i class="el-icon-ssq-delete"></i>
                                </span>
                            </span>
                        </p>
                    </template>
                </template>
                <p class="detail-info-line" v-if="showTagManageBtn">
                    <span class="detail-info-label"><!-- 合同标签 -->{{ $t("docDetail.contractTag") }}：</span>
                    <span class="detail-info-text">
                        <template v-if="contractTagsInfo.length > 0">
                            <ContractTag
                                v-for="tag in contractTagsInfo"
                                :key="tag.tagId"
                                :tag="tag"
                                :isCursorDef="true"
                            ></ContractTag>
                        </template>
                        <span v-else><!-- 无 -->{{ $t("docDetail.none") }}</span>
                        <a
                            v-if="canEdit"
                            href="javascript:void(0)"
                            class="editInfo-btn"
                            @click="handleTagManage"
                        ><!-- 设置标签 -->{{ $t("docDetail.setTag") }}</a>
                    </span>
                </p>
                <!-- 询证章结果 -->
                <p class="detail-info-line" v-if="subContractItem.confirmationResult">
                    <span class="detail-info-label">{{ $t("docDetail.requestSeal") }}：</span>
                    <span class="detail-info-text">{{ subContractItem.confirmationResult === 'AGREE' ? $t("docDetail.requestSealAgree") : $t("docDetail.requestSealRefuse") }}
                    </span>
                </p>
                <!-- 询证章不符备注 -->
                <p class="detail-info-line" v-if="subContractItem.confirmationRemark">
                    <span class="detail-info-label">{{ $t("docDetail.requestSealRemark") }}：</span>
                    <span class="detail-info-text">{{ subContractItem.confirmationRemark }}</span>
                </p>
            </div>
        </div>
        <SlidePop :outsideShow.sync="contentFieldPopVisible" :needReset="1">
            <ContentField
                slot="slide-pop-content"
                :docId="subContractItem.docId"
            >
            </ContentField>
        </SlidePop>
    </div>
</template>
<script>
import { tagManageMixin } from 'src/mixins/tagManage.js';
import { stringLangTransformMixin } from 'pub-mixins/stringLangTransform';
import { formatDateToString } from 'pub-utils/date.js';

import ContractTag from 'components/contractTag';
import Participants from './Participants';
import ContentField from '../ContentField/index.vue';
import SlidePop from 'pub-components/slidePop/index.vue';

import cloneDeep from 'lodash/cloneDeep';
import i18n from 'src/lang/';
import { mapState } from 'vuex';
import dayjs from 'dayjs';
import { handleSensorsHttpEventTrack } from 'src/utils/handleSensorsHttpEventTrack.js';

export default {
    components: {
        ContractTag,
        Participants,
        ContentField,
        SlidePop,
    },
    mixins: [stringLangTransformMixin, tagManageMixin],
    filters: {
        formatedTime: (val) => {
            if (val) {
                return formatDateToString({
                    date: val,
                    format: 'YYYY-MM-DD',
                });
            }
            return i18n.t('docDetail.none'); // 无
        },
    },
    props: {
        contractBaseInfo: {
            type: Object,
            default: () => {},
        },
        subContractItem: {
            type: Object,
            default: () => {},
        },
        title: {
            type: String,
            default: '',
        },
        // 自定义描述字段以及其他字段默认可修改，子合同暂时不支持
        canEdit: {
            type: Boolean,
            default: true,
        },
        // 查看详情，单文档合同、子合同不能再查看详情了
        canViewDetail: {
            type: Boolean,
            default: true,
        },
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {},
        },
    },
    inject: ['handleShowDialog'],
    data() {
        return {
            fileMaturityDate: '',
            pickMaturityDate: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7;
                },
            },
            editingContractTypeVisible: false,
            editContractCustomIdVisible: false,
            cacheCustomContractId: '',
            cacheContractTypeId: '',
            contractsTypes: [],
            cacheDescFields: [],
            contentFieldPopVisible: false,
        };
    },
    computed: {
        isSignerOfContract() {
            // CFD-21478: 和右上角预览图权限保持一致
            return this.contractBaseInfo.documents.find(a => a.documentId === this.subContractItem.docId);
        },
        sendUserType() {
            return this.contractBaseInfo.sender.enterpriseId !== '0' ? 'ENTERPRISE' : 'PERSON';
        },
        ...mapState({
            localFieldSupportType: state => state.localFieldSupportType,
        }),
    },
    watch: {
        'subContractItem.contractTypeId': {
            handler(v) {
                this.cacheContractTypeId = v;
            },
            immediate: true,
        },
        'subContractItem.customContractId': {
            handler(v) {
                this.cacheCustomContractId = v;
            },
            immediate: true,
        },
        'subContractItem.describeFields': {
            handler(v) {
                if (!v) {
                    return false;
                }
                if (v.length !== this.cacheDescFields.length) {
                    v.map((item) => {
                        item.cacheValue = '';
                        return (item.editing = false);
                    });
                }
                this.cacheDescFields = cloneDeep(v);
            },
            immediate: true,
        },
    },
    methods: {
        handleClickEventTrack(iconName = '', secondCategory = null) {
            this.$sensors.track({
                eventName: 'Ent_ContractManageDetail_BtnClick',
                eventProperty: {
                    page_name: '合同管理详情页',
                    first_category: '合同详细信息',
                    second_category: secondCategory,
                    icon_name: iconName,
                    ...this.sensorsTrackContractInfo,
                },
            });
        },
        handleViewContentInfo() {
            this.handleClickEventTrack('查看合同内容字段');
            this.contentFieldPopVisible = true;
        },
        // 根据 fieldName 从 describeFields 中找到对应的字段对象
        findFieldByName(fieldName) {
            const findFieldItem = this.subContractItem.describeFields.filter((item) => {
                return item.fieldName === fieldName;
            });

            return findFieldItem.length ? findFieldItem[0] : {};
        },
        // 描述字段取值，目前取 fieldValue 并且处理为空的场景
        // todo 将时间、合同类型放进来一起处理
        formatFieldValue(fieldName) {
            return this.findFieldByName(fieldName).fieldValue || this.$t('docDetail.none');
        },
        // 企业控制台打开限制字段修改时，提示不能修改
        handleEditLimit(item, name) {
            if (item.canContractEdit === false) {
                this.$MessageToast({
                    type: 'info',
                    message: this.$t('docDetail.labelLimitEditTip', { name }),
                    duration: 5000,
                });
                return false;
            }
            return true;
        },
        // 修改到期时间
        handleExpireChange({ isClear = false }) {
            const iconName = isClear ? '清除' : (this.subContractItem.contractLifeEnd ? '修改' : '设置');
            this.handleClickEventTrack(iconName, '合同到期时间');
            // 清除过期时间
            if (isClear) {
                this.fileMaturityDate = '';
            }
            const options = {
                documentId: this.subContractItem.docId,
                ...(this.fileMaturityDate && { contractLifeEnd: Date.parse(this.fileMaturityDate) }),
            };
            this.$http
                .post(
                    `/contract-api/contracts/${this.$route.params.contractId}/contract-life`,
                    options,
                    {
                        headers: { 'ent-id': this.contractBaseInfo.sender.enterpriseId },
                    },
                )
                .then(() => {
                    handleSensorsHttpEventTrack({
                        moduleName: 'ContractManage',
                        pageName: '合同管理详情页',
                        ifSuccess: true,
                        iconName: `${iconName}`,
                        requestUrl: `/contract-api/contracts/${this.$route.params.contractId}/contract-life`,
                        contractBaseInfo: { first_category: '合同到期时间', ...this.sensorsTrackContractInfo },
                    });
                    this.$emit('updateContractDetailForSender', {
                        updateParam: 'contractLifeEnd',
                        newValue: Date.parse(this.fileMaturityDate),
                    });
                })
                .catch((err) => {
                    handleSensorsHttpEventTrack({
                        moduleName: 'ContractManage',
                        pageName: '合同管理详情页',
                        ifSuccess: false,
                        err,
                        iconName: `${iconName}`,
                        requestUrl: `/contract-api/contracts/${this.$route.params.contractId}/contract-life`,
                        contractBaseInfo: { first_category: '合同到期时间', ...this.sensorsTrackContractInfo },
                    });
                });
        },
        // 修改合同类型/合同类型（备用）
        showSelectContractType(callType = 'contractType', index, item = {}) {
            if (!this.handleEditLimit(item, this.$t(callType === 'contractType' ? 'docDetail.contractType'
                : 'docDetail.contractTypeBackup'))) {
                return;
            }
            this.handleClickEventTrack('修改', callType === 'contractType' ? '合同类型' : '合同类型（备用）');
            const status = this.contractBaseInfo.contractStatus;
            // 签署中的合同不允许修改合同类型
            if (['SENT', 'IN_SEND_APPROVAL'].includes(status)) {
                // 中的合同不允许修改合同类型
                const type = status === 'SENT' ? this.$t('docDetail.sign') : this.$t('docDetail.approval');
                const contractTypeTip = this.$t('docDetail.notAllowModifyContractType', { type: type });
                const contractTypeBackupTip = this.$t('docDetail.notAllowModifyContractTypeBackup', { type: type });
                const msg = callType === 'contractType' ? contractTypeTip : contractTypeBackupTip;
                return this.$MessageToast.info(msg);
            }
            const loading = this.$loading();
            this.getContractsTypes().then(() => {
                if (callType === 'contractType') {
                    this.cacheContractTypeId = this.subContractItem.contractTypeId;
                    this.editingContractTypeVisible = true;
                } else {
                    this.editDescField(index);
                }
            }).finally(() => {
                loading.close();
            });
        },
        // 根据公司内部编号查询相关合同，并在侧边栏展示
        handleSearchRelativeContract() {
            this.handleClickEventTrack('搜索', '公司内部编号');
            const params = {
                show: true,
                params: {
                    customNumber: this.subContractItem.customContractId,
                    contractId: this.$route.params.contractId,
                    senderEntId: this.contractBaseInfo.sender.enterpriseId,
                },
                type: 'customNumber',
            };
            this.popShowChange(params);
        },
        // 取消修改合同类型
        handleResetContractType() {
            this.handleClickEventTrack('取消', '合同类型');
            this.editingContractTypeVisible = !this.editingContractTypeVisible;
            this.cacheContractTypeId = '';
        },
        // 获取合同类型数据
        getContractsTypes() {
            return this.$http
                .get(`${signPath}/contract-type-new`, {
                    headers: { 'ent-id': this.contractBaseInfo.sender.enterpriseId },
                })
                .then((res) => {
                    this.contractsTypes = this.handleRoleNameTransform(
                        res.data,
                        [this.$t('docDetail.uncategorized')],
                        [this.$t('docDetail.unSort')],
                    );
                });
        },
        // 修改数据类型与公司内部编号
        submitCustomIdAndContractType(type) {
            const iconName = type === 'contractType' ? '选中' : '请输入公司内部编号';
            const secondCategory = type === 'contractType' ? '合同类型' : '公司内部编号';
            this.handleClickEventTrack(iconName, secondCategory);
            const editContractType = type === 'contractType';
            this.$http
                .post(
                    '/contract-api/contracts/modifyContractDetail',
                    {
                        contractId: this.contractBaseInfo.contractId,
                        contractTypeId: editContractType
                            ? this.cacheContractTypeId
                            : this.subContractItem.contractTypeId,
                        customContractId: !editContractType
                            ? this.cacheCustomContractId
                            : this.subContractItem.customContractId,
                        documentId: this.subContractItem.docId,
                    },
                    {
                        headers: { 'ent-id': this.contractBaseInfo.sender.enterpriseId },
                    },
                )
                .then(() => {
                    handleSensorsHttpEventTrack({
                        moduleName: 'ContractManage',
                        pageName: '合同管理详情页',
                        ifSuccess: true,
                        iconName: `${iconName}`,
                        requestUrl: `/contract-api/contracts/modifyContractDetail`,
                        contractBaseInfo: { first_category: '合同详细信息', second_category: secondCategory, ...this.sensorsTrackContractInfo },
                    });
                    this.editCustomIdAndContractTypeDone(editContractType);
                })
                .catch((err) => {
                    handleSensorsHttpEventTrack({
                        moduleName: 'ContractManage',
                        pageName: '合同管理详情页',
                        ifSuccess: false,
                        err,
                        iconName: `${iconName}`,
                        requestUrl: `/contract-api/contracts/modifyContractDetail`,
                        contractBaseInfo: { first_category: '合同详细信息', second_category: secondCategory, ...this.sensorsTrackContractInfo },
                    });
                });
        },
        // 修改数据类型与公司内部编号成功后的处理，更新显示
        editCustomIdAndContractTypeDone(isEditContractType) {
            this.$MessageToast.success(this.$t('docDetail.modifySuccess')); //
            if (isEditContractType) {
                const contractTypeObj = this.contractsTypes.filter((item) => {
                    return item.contractTypeId === this.cacheContractTypeId;
                })[0];
                this.$emit('updateContractDetailForSender', {
                    updateParam: 'contractTypeId',
                    newValue: contractTypeObj.contractTypeId,
                });
                this.$emit('updateContractDetailForSender', {
                    updateParam: 'contractTypeName',
                    newValue: contractTypeObj.contractTypeName,
                });
                this.handleResetContractType();
            } else {
                this.$emit('updateContractDetailForSender', {
                    updateParam: 'customContractId',
                    newValue: this.cacheCustomContractId,
                });
                this.handleResetCustomId();
            }
        },
        // 取消修改公司内部编号
        handleResetCustomId(noValid = true, item) {
            if (!noValid && !this.handleEditLimit(item, this.$t('docDetail.companyInternalNum'))) {
                return;
            }
            this.handleClickEventTrack('取消', '合同内部编号');
            this.editContractCustomIdVisible = !this.editContractCustomIdVisible;
            this.cacheCustomContractId = '';
        },
        // 展示修改自定义字段
        editDescField(index, item) {
            if (item && !this.handleEditLimit(item, item.fieldName)) {
                return;
            }
            this.$set(this.cacheDescFields, index, {
                ...this.cacheDescFields[index],
                ...{ editing: true },
            });
        },
        getIdByName(name) {
            // 合同类型（备用）字段值传给后端的是id，前端存储的是id对应的值，此处根据值查id
            return this.contractsTypes.find(item => item.contractTypeName === name).contractTypeId;
        },
        // 修改自定义字段
        submitDescField(obj, index) {
            this.handleClickEventTrack('修改', obj.fieldName);
            if (obj.necessary && !obj.cacheValue) {
                return this.$MessageToast(
                    this.$t('docDetail.requireFieldNotAllowEmpty'),
                ); // 必填项不能为空
            }
            this.$http
                .post(
                    `/contract-api/contracts/${this.contractBaseInfo.contractId}/modify-describe-field`,
                    {
                        fieldName: obj.fieldName,
                        filedValue: obj.fieldName === 'contractTypeIdsForApprove' ? this.getIdByName(obj.cacheValue) : obj.cacheValue,
                        documentId: this.subContractItem.docId,
                    },
                    {
                        headers: { 'ent-id': this.contractBaseInfo.sender.enterpriseId },
                    },
                )
                .then(() => {
                    handleSensorsHttpEventTrack({
                        moduleName: 'ContractManage',
                        pageName: '合同管理详情页',
                        ifSuccess: true,
                        iconName: '修改',
                        requestUrl: `/contract-api/contracts/${this.contractBaseInfo.contractId}/modify-describe-field`,
                        contractBaseInfo: { first_category: obj.fieldName, ...this.sensorsTrackContractInfo },
                    });
                    this.$set(this.cacheDescFields, index, {
                        ...this.cacheDescFields[index],
                        ...{ fieldValue: obj.cacheValue },
                    });
                    this.handleResetDescField(index);
                    this.$emit('updateContractDetailForSender', {
                        updateParam: 'describeFields',
                        newValue: this.cacheDescFields,
                    });
                })
                .catch((err) => {
                    handleSensorsHttpEventTrack({
                        moduleName: 'ContractManage',
                        pageName: '合同管理详情页',
                        ifSuccess: false,
                        err,
                        iconName: '修改',
                        requestUrl: `/contract-api/contracts/${this.contractBaseInfo.contractId}/modify-describe-field`,
                        contractBaseInfo: { first_category: obj.fieldName, ...this.sensorsTrackContractInfo },
                    });
                });
        },
        // 取消修改自定义字段
        handleResetDescField(index) {
            this.$set(this.cacheDescFields, index, {
                ...this.cacheDescFields[index],
                ...{ editing: false, cacheValue: '' },
            });
        },
        // 设置自定义标签时，日期格式需要转换
        handleDescFieldFormatDate(item, event) {
            item.cacheValue = dayjs(event).format('YYYY-MM-DD');
        },
        // 设置标签
        handleTagManage() {
            // 判断有没有合同标签，没有的话弹出提示
            if (this.selectedTagParams.tagOptions.length < 1) {
                return this.$MessageToast.info(this.$t('docDetail.noTagToAddHint')); // 还没有标签，请前往企业控制台添加
            }
            this.selectedTagParams.addedTagIds = this.contractTagsInfo.map(
                (a) => a.tagId,
            );
            this.handleShowDialog('TagManage', {
                ...this.selectedTagParams,
            });
        },
        // 右侧弹框
        popShowChange(popShow) {
            this.$emit('popShowChange', popShow);
        },
        // 查看详情
        goSubContract() {
            this.handleClickEventTrack('查看详情');
            this.$router.push(`/doc-manage/detail/${this.$route.params.contractId}/${this.subContractItem.docId}`);
        },
    },
    created() {
        this.initTagInfo(this.$route.params.contractId);
    },
};
</script>
<style lang="scss">
.doc-detail-detailInfo{
    h3 {
        height: 50px;
        line-height: 50px;
        color: $--color-text-primary;
        font-size: 16px;
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;

        .detailInfo__title{
            padding-right: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .detailInfo__title--right{
            flex-shrink: 0;
        }
        .doc-detail-subDetail{
            padding-right: 10px;
        }
    }
    .contractInfo-container{
        display: flex;
        flex-direction: row;
        border:1px solid $--border-color-lighter;
        color: $--color-text-primary;
        font-size: 12px;
        // max-height: 400px;
        .contractInfo-reciviers-list{
            padding: 20px;
            width: 335px;
            border-right: 1px solid $--border-color-lighter;
            overflow: auto;
        }
        .contractInfo{
            flex:1;
            padding: 20px 33px;
            overflow: auto;
            .detail-info-line{
                display: flex;
                flex-direction: row;
                gap: 5px;
                line-height: 26px;
                .search-by-code {
                    padding-right: 6px;
                    color: $--color-primary;
                    cursor: pointer;
                    i {
                        font-weight: bold;
                    }
                }
                .detail-info-label{
                    color: $--color-text-secondary;
                    .must{
                        color: $--color-danger;
                    }
                }
                .detail-info-text{
                    [dir=rtl] & {
                        display: flex;
                        flex:1;
                        align-items: center;
                    }
                    .detail-info-expireDate{
                        position: relative;
                        margin-left: 10px;
                        display: inline-block;

                        .el-date-editor{
                            top:0;
                            position: absolute;
                            left: 0px;
                            opacity: 0;
                            width: 100%;
                            height: 100%;

                            .el-input__icon{
                                display: none;
                            }

                            .el-input__inner{
                                width: inherit;
                                line-height: initial;
                                height: inherit;
                                padding: 0;
                                cursor: pointer;
                            }
                        }
                    }
                    .el-date-editor{
                        .el-input__inner {
                            //width: inherit;
                            line-height: 15px;
                            height: inherit;
                            padding: 0;
                            cursor: pointer;
                        }
                        .el-icon-date {
                            line-height: 15px;
                            vertical-align: top;
                        }
                    }
                    .el-input{
                        display: inline;
                        [dir=rtl] & {
                            width: 140px;
                        }
                    }

                    .el-input__inner{
                        width: 135px;
                        height: 30px;
                        line-height: 30px;
                        font-size: 12px;
                    }

                    .editInfo-btn{
                        margin-left: 10px;
                        cursor: pointer;
                        color: $theme-color;
                        font-size: 12px;
                        [dir=rtl] & {
                            margin-left: 0;
                            margin-right: 10px;
                        }
                    }

                    .detail-info__opt{
                        margin-left: 5px;
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        background: $background-color-dark;
                        border-radius: 2px;
                        font-size: 12px;
                        text-align: center;
                        line-height: 16px;
                        cursor: pointer;
                        color: $--color-text-secondary;
                        [dir=rtl] & {
                            margin-left: 0;
                            margin-right: 5px;
                        }
                    }
                    .detail-info__opt_cancel i{
                        font-weight: bold;
                    }
                    .tag-item{
                        vertical-align: middle;
                    }
                    .edit-remind{
                        cursor: pointer;
                        padding-left: 10px;
                    }
                    .el-select{
                        .el-select__caret{
                                transform: translateY(50%) rotate(180deg);
                        }
                    }
                }
            }
        }
    }
}
</style>
