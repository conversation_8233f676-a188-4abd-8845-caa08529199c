import { createWrapper } from 'src/testUtils';
import DetailInfo from '../index.vue';
import flushPromises from 'flush-promises';
import cloneDeep from 'lodash/cloneDeep';
const contractTypeList = [{
    contractTypeId: '10000',
    contractTypeName: '未分类',
}, {
    contractTypeId: '10001',
    contractTypeName: '分类1',
}];

const subContractItem = {
    canManage: true,
    contractLifeEnd: '1619711999000',
    docId: '333',
    contractTypeName: '合同类型1',
    customContractId: '222',
    contractTypeId: '1000',
    describeFieldDisplay: {
        contractContentExpireDays: true,
        contractTitle: true,
        contractTypeId: true,
        customNumber: true,
        signExpireDays: true,
    },
    describeFields: [{
        bizFieldType: 'TEXT',
        buttons: null,
        enterpriseId: '2607221667608400902',
        fieldId: '2781903697511187458',
        fieldName: '描述字段',
        fieldType: null,
        fieldValue: '呃呃呃呃',
        necessary: false,
    }, {
        bizFieldType: 'TEXT',
        buttons: null,
        enterpriseId: '2607221667608400901',
        fieldId: '2781903697511187459',
        fieldName: '描述字段1',
        fieldType: null,
        fieldValue: '你好',
        necessary: false,
    }],
};

const contractBaseInfo =  {
    sender: {
        enterpriseId: '11',
    },
    contractId: '1',
    contractStatus: 'COMPLETE',
    documents: [],

};

const emitFn = jest.fn();
const mockRouterPush = jest.fn();

const tagManageMixin = {
    data() {
        return {
            showTagManageBtn: true, // 是否显示设置标签按钮
            contractTagsInfo: [{
                color: '#439B4C',
                name: '已审查',
                tagId: '2481886533599100930',
            }],
            selectedTagParams: {
                dialogTitle: this.$t('mixin.setLabel'),
                tagOptions: [{
                    color: '#439B4C',
                    name: '已审查',
                    tagId: '2481886533599100930',
                }, {
                    color: '#DB5B6C',
                    name: '发送后',
                    tagId: '2481886977608122377',
                }],
                addedTagIds: [],
            },
        };
    },
    methods: {
        initTagInfo: jest.fn(),
    },
};

const stringLangTransformMixin = {
    methods: {
        handleRoleNameTransform: jest.fn().mockImplementation(t => t),
    },
};

jest.mock('src/lang/',  () => {
    return {
        t: jest.fn().mockImplementation((t) => t),
    };
});
jest.mock('pub-utils/date.js', () => {
    return {
        formatDateToString: jest.fn().mockImplementation(t => t),
    };
});
jest.mock('../Participants', () => ({
    template: '<div></div>',
}));
jest.mock('../../ContentField/index.vue', () => ({
    template: '<div></div>',
}));
jest.mock('pub-components/slidePop/index.vue', () => ({
    template: '<div></div>',
}));

describe('DetailInfo.vue', () => {
    test('点击修改合同到期日后，接口调用修改，抛出emit修改详情页数据',  async() => {
        const httpFn = jest.fn().mockImplementation(() => Promise.resolve());
        const wrapper = createWrapper(DetailInfo, undefined, {
            propsData: {
                subContractItem: cloneDeep(subContractItem),
                contractBaseInfo: cloneDeep(contractBaseInfo),
            },
            data() {
                return {
                    fileMaturityDate: '01 Jan 2022 00:00:00 GMT',
                };
            },
            provide: {
                handleShowDialog: jest.fn(),
            },
            mocks: {
                $emit: emitFn,
                $http: {
                    post: httpFn,
                },
                $route: {
                    params: {
                        contractId: '111',
                    },
                },
                $hybrid: {
                    isGammaLocalField: jest.fn(),
                },
            },
            mixins: [tagManageMixin],
        });
        // 修改日期
        wrapper.vm.handleExpireChange({ isClear: false });
        await flushPromises();
        expect(httpFn).toHaveBeenCalled();
        const params = {
            updateParam: 'contractLifeEnd',
            newValue: Date.parse(wrapper.vm.fileMaturityDate),
        };
        expect(emitFn).toHaveBeenCalledWith('updateContractDetailForSender', params);
    });
    test('点击修改合同类型，签署中的合同不能修改合同类型，会提示', async() => {
        const infoFn = jest.fn().mockImplementation(t => t);
        const theContractBaseInfo = cloneDeep(contractBaseInfo);
        theContractBaseInfo.contractStatus = 'SENT';
        const wrapper = createWrapper(DetailInfo, undefined, {
            propsData: {
                subContractItem: cloneDeep(subContractItem),
                contractBaseInfo: theContractBaseInfo,
            },
            provide: {
                handleShowDialog: jest.fn(),
            },
            mocks: {
                $MessageToast: {
                    info: infoFn,
                },
                $route: {
                    params: {
                        contractId: '111',
                    },
                },
                $hybrid: {
                    isGammaLocalField: jest.fn(),
                },
            },
            mixins: [tagManageMixin],
        });
        wrapper.vm.showSelectContractType();
        expect(infoFn).toHaveBeenCalledWith('docDetail.notAllowModifyContractType');

        const newContractBaseInfo = {
            sender: {
                enterpriseId: '11',
            },
            contractStatus: 'IN_SEND_APPROVAL',
        };
        await wrapper.setProps({ contractBaseInfo: newContractBaseInfo });
        wrapper.vm.showSelectContractType();
        expect(infoFn).toHaveBeenCalledWith('docDetail.notAllowModifyContractType');

        wrapper.vm.showSelectContractType('contractTypeBackup', 1);
        expect(infoFn).toHaveBeenCalledWith('docDetail.notAllowModifyContractTypeBackup');
    });
    test('点击修改合同类型，展示可选择的合同类型', async() => {
        const getFn = jest.fn().mockImplementation(() => Promise.resolve({ data: contractTypeList }));
        const wrapper = createWrapper(DetailInfo, undefined, {
            propsData: {
                subContractItem: cloneDeep(subContractItem),
                contractBaseInfo: cloneDeep(contractBaseInfo),
            },
            provide: {
                handleShowDialog: jest.fn(),
            },
            mocks: {
                $http: {
                    get: getFn,
                },
                $route: {
                    params: {
                        contractId: '111',
                    },
                },
                $hybrid: {
                    isGammaLocalField: jest.fn(),
                },
            },
            mixins: [tagManageMixin, stringLangTransformMixin],
        });
        wrapper.vm.showSelectContractType();
        await flushPromises();
        expect(getFn).toHaveBeenCalledWith('true/contract-type-new', { 'headers': { 'ent-id': '11' } });
        expect(wrapper.vm.contractsTypes).toEqual(contractTypeList);
    });
    test('点击确认修改合同类型后，调用修改接口，更新对应合同类型和id的emit被抛出,修改状态为false',  async() => {
        const postFn = jest.fn().mockImplementation(() => Promise.resolve());
        const wrapper = createWrapper(DetailInfo, undefined, {
            propsData: {
                subContractItem: cloneDeep(subContractItem),
                contractBaseInfo: cloneDeep(contractBaseInfo),
            },
            provide: {
                handleShowDialog: jest.fn(),
            },
            data() {
                return {
                    fileMaturityDate: '01 Jan 2022 00:00:00 GMT',
                    contractsTypes: contractTypeList,
                    editingContractTypeVisible: true,
                };
            },
            mocks: {
                $emit: emitFn,
                $http: {
                    post: postFn,
                },
                $route: {
                    params: {
                        contractId: '111',
                    },
                },
                $MessageToast: {
                    success: jest.fn(),
                },
                $hybrid: {
                    isGammaLocalField: jest.fn(),
                },
            },
            mixins: [tagManageMixin, stringLangTransformMixin],
        });
        wrapper.vm.cacheContractTypeId = '10001';
        wrapper.vm.submitCustomIdAndContractType('contractType');

        await flushPromises();
        // 修改接口调用
        expect(postFn).toHaveBeenCalledWith('/contract-api/contracts/modifyContractDetail', { 'contractId': '1', 'contractTypeId': '10001', 'customContractId': '222', 'documentId': '333' }, { 'headers': { 'ent-id': '11' } });
        expect(emitFn).toHaveBeenCalledWith('updateContractDetailForSender', {
            updateParam: 'contractTypeId',
            newValue: '10001',
        });
        expect(emitFn).toHaveBeenCalledWith('updateContractDetailForSender', {
            updateParam: 'contractTypeName',
            newValue: '分类1',
        });
        expect(wrapper.vm.editingContractTypeVisible).toEqual(false);
        expect(wrapper.vm.cacheContractTypeId).toEqual('');
    });
    test('点击确认修改公司内部编号，调用修改接口，更新抛出值，修改状态为false',  async() => {
        const postFn = jest.fn().mockImplementation(() => Promise.resolve());
        const wrapper = createWrapper(DetailInfo, undefined, {
            propsData: {
                subContractItem: cloneDeep(subContractItem),
                contractBaseInfo: cloneDeep(contractBaseInfo),
            },
            provide: {
                handleShowDialog: jest.fn(),
            },
            data() {
                return {
                    editContractCustomIdVisible: true,
                };
            },
            mocks: {
                $emit: emitFn,
                $http: {
                    post: postFn,
                },
                $route: {
                    params: {
                        contractId: '111',
                    },
                },
                $MessageToast: {
                    success: jest.fn(),
                },
                $hybrid: {
                    isGammaLocalField: jest.fn(),
                },
            },
            mixins: [tagManageMixin, stringLangTransformMixin],
        });
        wrapper.vm.cacheCustomContractId = '1222';
        wrapper.vm.submitCustomIdAndContractType('customerId');
        await flushPromises();
        // 修改接口调用
        expect(postFn).toHaveBeenCalledWith('/contract-api/contracts/modifyContractDetail', { 'contractId': '1',  'contractTypeId': '1000', 'customContractId': '1222', 'documentId': '333' }, { 'headers': { 'ent-id': '11' } });
        expect(emitFn).toHaveBeenCalledWith('updateContractDetailForSender', {
            updateParam: 'customContractId',
            newValue: '1222',
        });
        expect(wrapper.vm.editContractCustomIdVisible).toEqual(false);
        expect(wrapper.vm.cacheCustomContractId).toEqual('');
    });
    test('点击修改自定义描述字段，输入后确认，修改接口被调用，对应值更新，编辑状态为false',  async() => {
        const postFn = jest.fn().mockImplementation(() => Promise.resolve());
        const wrapper = createWrapper(DetailInfo, undefined, {
            propsData: {
                subContractItem: cloneDeep(subContractItem),
                contractBaseInfo: cloneDeep(contractBaseInfo),
            },
            provide: {
                handleShowDialog: jest.fn(),
            },
            data() {
                return {
                    editContractCustomIdVisible: true,
                };
            },
            mocks: {
                $emit: emitFn,
                $http: {
                    post: postFn,
                },
                $route: {
                    params: {
                        contractId: '111',
                    },
                },
                $MessageToast: {
                    success: jest.fn(),
                },
                $hybrid: {
                    isGammaLocalField: jest.fn(),
                },
            },
            mixins: [tagManageMixin, stringLangTransformMixin],
        });
        wrapper.vm.editDescField(0);
        await flushPromises();
        let item = wrapper.vm.cacheDescFields[0];
        expect(item.editing).toEqual(true);
        item.cacheValue = '修改后描述';
        // 确认修改
        wrapper.vm.submitDescField(item, 0);

        await flushPromises();
        expect(postFn).toHaveBeenCalledWith('/contract-api/contracts/1/modify-describe-field', { 'documentId': '333', 'fieldName': '描述字段', 'filedValue': '修改后描述' }, { 'headers': { 'ent-id': '11' } });
        item = wrapper.vm.cacheDescFields[0];
        expect(item.fieldValue).toEqual('修改后描述');
        expect(item.editing).toEqual(false);
        expect(item.cacheValue).toEqual('');
        expect(emitFn).toHaveBeenCalledWith('updateContractDetailForSender', {
            updateParam: 'describeFields',
            newValue: wrapper.vm.cacheDescFields,
        });

        // 确认修改合同类型（备用）
        wrapper.vm.contractsTypes = [{ contractTypeName: 'testName', contractTypeId: 'testId' }];
        wrapper.vm.submitDescField({ fieldName: 'contractTypeIdsForApprove', necessary: false, cacheValue: 'testName' }, 0);
        expect(postFn).toHaveBeenCalledWith('/contract-api/contracts/1/modify-describe-field', { 'documentId': '333', 'fieldName': 'contractTypeIdsForApprove', 'filedValue': 'testId' }, { 'headers': { 'ent-id': '11' } });
    });
    test('点击设置标签，默认选中的之前设置的标签且弹框显示可选择标签', () => {
        const wrapper = createWrapper(DetailInfo, undefined, {
            propsData: {
                subContractItem: cloneDeep(subContractItem),
                contractBaseInfo: cloneDeep(contractBaseInfo),
            },
            provide: {
                handleShowDialog: jest.fn(),
            },
            data() {
                return {
                };
            },
            mocks: {
                $route: {
                    params: {
                        contractId: '111',
                    },
                },
                $hybrid: {
                    isGammaLocalField: jest.fn(),
                },
            },
            mixins: [tagManageMixin, stringLangTransformMixin],
        });
        wrapper.vm.handleTagManage();
        expect(wrapper.vm.selectedTagParams.addedTagIds).toEqual(['2481886533599100930']);
        expect(wrapper.vm.handleShowDialog).toHaveBeenCalledWith('TagManage', wrapper.vm.selectedTagParams);
    });
    test('点击查看详情', () => {
        const httpFn = jest.fn().mockImplementation(() => Promise.resolve());
        const wrapper = createWrapper(DetailInfo, undefined, {
            propsData: {
                subContractItem: cloneDeep(subContractItem),
                contractBaseInfo: cloneDeep(contractBaseInfo),
            },
            data() {
                return {
                    // fileMaturityDate: '01 Jan 2022 00:00:00 GMT',
                };
            },
            provide: {
                handleShowDialog: jest.fn(),
            },
            mocks: {
                $emit: emitFn,
                $http: {
                    post: httpFn,
                },
                $route: {
                    params: {
                        contractId: '111',
                    },
                },
                $hybrid: {
                    isGammaLocalField: jest.fn(),
                },
                $router: {
                    push: mockRouterPush,
                },
            },
            mixins: [tagManageMixin],
        });
        wrapper.vm.goSubContract();
        expect(mockRouterPush).toHaveBeenCalledWith('/doc-manage/detail/111/333');
    });
    test('点击按公司内部编号查询', () => {
        const wrapper = createWrapper(DetailInfo, undefined, {
            propsData: {
                subContractItem: cloneDeep(subContractItem),
                contractBaseInfo: cloneDeep(contractBaseInfo),
            },
            provide: {
                handleShowDialog: jest.fn(),
            },
            mocks: {
                $emit: emitFn,
                $route: {
                    params: {
                        contractId: '111',
                    },
                },
                $http: {
                    post: jest.fn().mockResolvedValue({}),
                },
                $hybrid: {
                    isGammaLocalField: jest.fn(),
                },
                $router: {
                    push: mockRouterPush,
                },
            },
        });
        wrapper.vm.handleSearchRelativeContract();
        expect(emitFn).toBeCalledWith('popShowChange', { 'params': { 'contractId': '111', 'customNumber': '222', 'senderEntId': '11' }, 'show': true, 'type': 'customNumber' });
    });
});
