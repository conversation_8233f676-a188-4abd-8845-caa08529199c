import Operates from '../index.vue';
import { initWrapper } from 'src/testUtils';
import cloneDeep from 'lodash/cloneDeep';
import flushPromises from 'flush-promises';

jest.mock('src/components/downloadHook', () => ({
    template: '<div></div>',
}));
jest.mock('pub-businessComponents/ssoNotBelongToEntDialog', () => ({
    template: '<div></div>',
}));
jest.mock('@/api/send', () => ({
    getSingleSendDraftId: jest.fn().mockResolvedValue({
        data: {
            draftId: '123',
        },
    }),
    getInvalidEnt: jest.fn().mockResolvedValue({ data: { data: { enterpriseId: '123' } } }),
}));
jest.mock('@/api/contract/index.js', () => ({
    getComparePermission: jest.fn().mockRejectedValue({
        statusText: 'xx',
    }),
    getTranslatePermission: jest.fn().mockResolvedValue({ data: { canUpload: false, reason: 'xx' } }),
    hubblePermissionCheck: jest.fn().mockResolvedValue({ data: { canUpload: false, reason: 'xx' } }),
}));
const mockGetFun = jest.fn();
const mockHandleJump = jest.fn();
describe('Operates.vue', () => {
    const storeOptions = {
        state: {
            commonHeaderInfo: {
                currentEntId: 'entId',
                extendFields: {
                    isLoginFromDeveloper: true,
                },
                platformUser: {
                    userId: '123456',
                },
            },
        },
        getters: {
            checkFeat() {
                return {
                    hubbleContractCompare: true,
                };
            },
            isPerson() {
                return true;
            },
        },
    };
    const mockPushFn = jest.fn();
    const handleShowDialogFn = jest.fn();
    const mockConfirm = jest.fn().mockResolvedValue({});
    const dataOptions = {
        propsData: {
            buttonDisplayRule: {},
            contractBaseInfo: {
                sender: {
                    desensitization: false,
                },
                signers: [
                    { receiverType: 'EDITOR' },
                ],
                documents: [
                    { documentId: 123 },
                ],
            },
        },
        provide: {
            handleShowDialog: handleShowDialogFn,
        },
        mocks: {
            $router: {
                push: mockPushFn,
            },
            $http: {
                switchEntId: jest.fn().mockResolvedValue({ data: '' }),
                get: mockGetFun,
            },
            $route: {
                params: {
                    contractId: '123',
                },
            },
            $confirm: mockConfirm,
            $i18n: {
                locale: 'zh',
            },
        },
    };
    test('点击作废按钮，直接进入作废申明发送页', async() => {
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.handleClick('INVALID');
        await flushPromises();
        expect(mockPushFn).toBeCalledWith(`/send/123/single/upload?isCancel=true`);
    });
    test('点击驳回重签按钮', () => {
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.handleReSign();
        expect(mockPushFn).toBeCalledWith('/doc-manage/reject-signer?contractId=undefined&contractTitle=undefined');
    });
    test('关闭SSO弹窗', () => {
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.handleCloseSSoDialog();
        expect(wrapper.vm.ssoParams.visible).toBeFalsy();
    });
    test('重新发起合同-用户不属于开发者', async() => {
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        mockGetFun.mockResolvedValueOnce({ data: { developerName: 'test', ifBelong: false } });
        wrapper.vm.handleResend();
        await flushPromises();
        expect(wrapper.vm.ssoParams.developerName).toEqual('test');
        expect(wrapper.vm.ssoParams.visible).toBeTruthy();
    });
    test('代理签', () => {
        const mockFun = jest.fn();
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.handleMulProxySign = mockFun;
        wrapper.vm.handleClick('PROXY_SIGN');
        expect(mockFun).toBeCalledWith({ 'type': 'PROXY_SIGN' }, undefined);
    });
    test('下载签约存证', () => {
        const mockCheckDownloadAppendixFn = jest.fn();
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.$refs = {
            downloadHook: {
                checkDownloadAppendix: mockCheckDownloadAppendixFn,
            },
        };
        wrapper.vm.contractBaseInfo.currentUserIsSenderSide = true;// 发送方
        wrapper.vm.contractBaseInfo.contractId = '123';
        wrapper.vm.handleDownloadattach();
        expect(mockCheckDownloadAppendixFn).toBeCalledWith({ contractId: '123' });
    });
    test('补全按钮点击', async() => {
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        mockGetFun.mockResolvedValueOnce({ data: 'entId' });
        wrapper.vm.handleClick('EDITOR');
        await flushPromises();
        expect(handleShowDialogFn).toHaveBeenCalled();
    });
    test('合同比对按钮点击', () => {
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.handleJump = mockHandleJump;
        wrapper.vm.handleHubble('HubbleCompare');
        expect(mockHandleJump).toBeCalledWith('HubbleCompare');
        // wrapper.vm.handleComparison();
        // expect(mockHandleJump).toBeCalledWith('Compare');
    });
    test('比对时出现选择文档弹窗', () => {
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.selectDialogConfirmCallBack = mockHandleJump;
        wrapper.vm.handleJump('HubbleCompare');
        expect(wrapper.vm.selectedDocumentId).toBe(123);
        expect(wrapper.vm.selectDialogType).toBe('HubbleCompare');
        expect(mockHandleJump).toBeCalled();
    });
    test('比对选择文档弹窗确定', () => {
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.selectDialogType = 'HubbleCompare';
        wrapper.vm.toHubbleCompare = mockHandleJump;
        wrapper.vm.selectDialogConfirmCallBack();
        expect(mockHandleJump).toBeCalled();
    });
    test('校验比对权限', async() => {
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.checkHubblePermission('COMPARE');
        await flushPromises();
        expect(mockConfirm).toBeCalledWith('xx', {
            title: 'templateCommon.tip',
            type: 'warning',
            confirmButtonText: 'batchImport.iKnow',
            showCancelButton: false,
        });
    });
    test('校验翻译权限', async() => {
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.checkHubblePermission('TRANSLTAION');
        await flushPromises();
        wrapper.vm.handleHubble('HubbleTranslate');
    });
    // test('校验翻译权限', async() => {
    //     const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
    //     wrapper.vm.handleClick('CONTRACT_TRANSLATION');
    //     await flushPromises();
    //     expect(mockConfirm).toBeCalledWith('xx', {
    //         title: 'templateCommon.tip',
    //         type: 'warning',
    //         confirmButtonText: 'batchImport.iKnow',
    //         showCancelButton: false,
    //     });
    // });
    // test('去翻译', async() => {
    //     mockPushFn.mockClear();
    //     const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
    //     wrapper.vm.contractBaseInfo.contractId = '123';
    //     wrapper.vm.selectedDocumentId = '456';
    //     wrapper.vm.toHubbleTranslate();
    //     expect(mockPushFn).toBeCalledWith('/hubble-apply/doc-translation/123/456');
    // });
    test('去比对', async() => {
        mockPushFn.mockClear();
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.contractBaseInfo.contractId = '123';
        wrapper.vm.selectedDocumentId = '456';
        wrapper.vm.toHubbleCompare();
        expect(mockPushFn).toBeCalledWith(`/hubble-apply/contract-comparison/${wrapper.vm.$route.params.contractId}/456`);
    });
    // test('去比对', async() => {
    //     mockPushFn.mockClear();
    //     const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
    //     wrapper.vm.contractBaseInfo.contractId = '123';
    //     wrapper.vm.selectedDocumentId = '456';
    //     wrapper.vm.toCompare();
    //     expect(mockPushFn).toBeCalledWith('/contract-comparison/123/456');
    // });
    test('请选择申请项目', async() => {
        mockPushFn.mockClear();
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.contractBaseInfo.contractId = '123';
        wrapper.vm.selectedDocumentId = '456';
        wrapper.vm.handleDownloadAttachment('certificate');
        expect(wrapper.vm.downloadAttachmentValue).toEqual('');
    });
    test('点击合同比对', async() => {
        const mockFun = jest.fn();
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.handleMulProxySign = mockFun;
        wrapper.vm.handleClick('CONTRACT_COMPARISON');
        // expect(mockFun).toBeCalled();
    });
    test('点击合同翻译', async() => {
        const mockFun = jest.fn();
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.handleMulProxySign = mockFun;
        wrapper.vm.handleClick('CONTRACT_TRANSLATION');
        // expect(mockFun).toBeCalled();
    });
    test('点击合同抽取', async() => {
        const mockFun = jest.fn();
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.handleMulProxySign = mockFun;
        wrapper.vm.handleClick('CONTRACT_EXTRACT');
        // expect(mockFun).toBeCalled();
    });
    test('去抽取', async() => {
        mockPushFn.mockClear();
        const wrapper = initWrapper(Operates, cloneDeep(storeOptions), cloneDeep(dataOptions));
        wrapper.vm.contractBaseInfo.contractId = '123';
        wrapper.vm.selectedDocumentId = '456';
        wrapper.vm.toHubbleExtract();
        expect(mockPushFn).toBeCalledWith('/hubble-apply/contract-extract?contractId=123&documentId=456');
    });
});
