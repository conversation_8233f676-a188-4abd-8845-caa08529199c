<template>
    <div class="doc-detail-container">
        <div class="doc-detail-content">
            <!--  合同基本信息,所有人都可以见 -->
            <BaseInfo
                :contractBaseInfo="contractBaseInfo"
                :buttonDisplayRule="buttonDisplayRule"
                :currentUserIsSenderSide="currentUserIsSenderSide"
                :ssoDocDtl="ssoDocDtl"
                :source="source"
            >
            </BaseInfo>
            <!-- 合同收件方 -->
            <Receivers
                :source="source"
                :contractBaseInfo="contractBaseInfo"
                :contractId="contractBaseInfo.contractId"
                :displayAutoSign="displayAutoSign"
                :ifSender="!!(subContractDetails || []).length"
            >
            </Receivers>
            <!-- 合同详细信息，发件方可以见 -->
            <template v-for="(item,index) in subContractDetails">
                <!-- 合同详细信息，发件方可以见 -->
                <DetailInfo
                    :key="index"
                    :title="item.docTitle"
                    :contractBaseInfo="contractBaseInfo"
                    :subContractItem="item"
                    :canViewDetail="subContractDetails.length > 1"
                    @updateContractDetailForSender="updateContractDetailForSender($event,index)"
                >
                </DetailInfo>
            </template>
            <template v-if="!contractBaseInfo.ifMultiPlatformContract">
                <!-- 审计日志（原企业内部操作日志） -->
                <OperationLogs v-if="entInternalDetails.length"
                    :contractId="contractBaseInfo.contractId"
                    :entInternalDetails="entInternalDetails"
                ></OperationLogs>
                <PersonOeprationLog
                    v-if="personClaimDetails.length"
                    :personClaimDetails="personClaimDetails"
                ></PersonOeprationLog>
                <ComparisonLog></ComparisonLog>
                <!-- 关联合同 -->
                <LinkContracts :linkedContractId="contractBaseInfo.contractId"></LinkContracts>
            </template>
        </div>
        <!-- 合同缩略图 -->
        <MiniDoc
            :documents="contractBaseInfo.documents"
            :contractId="contractBaseInfo.contractId"
            :hybridServer="commonHeaderInfo.hybridServer"
            :hybridAccessToken="commonHeaderInfo.hybridAccessToken"
            :contractTitle="contractBaseInfo.contractTitle"
            @showPreview="handlePreview"
        ></MiniDoc>
        <!-- 弹框处理 -->
        <div class="doc-detail-dialogs">
            <component
                :is="curDialog"
                :params="curDialogParams"
                @close="handleDialogClose"
                @refreshTable="handleCancelDone"
            >
            </component>
        </div>
        <Preview ref="preview"></Preview>
    </div>
</template>
<script>
import { docDetailMixin } from 'src/mixins/docDetail.js';
export default {
    mixins: [docDetailMixin],
    data() {
        return {
            detailUrl: `/contract-center/outer-contract/detail/${this.$route.params.contractId}`,
        };
    },
};
</script>
