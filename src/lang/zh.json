{"templateCommon.tempMgmt": "模板管理", "templateCommon.usageHelp": "使用帮助", "templateCommon.mark": "备注", "templateCommon.set": "设置", "templateCommon.operation": "操作", "templateCommon.selectedFile": "已选中{number}份文件", "templateCommon.edit": "编辑", "templateCommon.use": "使用", "templateCommon.approval": "审批", "templateCommon.add": "新增", "templateCommon.delete": "删除", "templateCommon.deleteSucc": "删除成功", "templateCommon.batchDelete": "批量删除", "templateCommon.copy": "复制", "templateCommon.group": "归档", "templateCommon.remove": "移出", "templateCommon.confirm": "确定", "templateCommon.singleSendContract": "单独发送合同", "templateCommon.batchSendContract": "使用Excel批量发送", "templateCommon.cancel": "取消", "templateCommon.save": "保存", "templateCommon.inputPlaceHolder": "请输入", "templateCommon.selectPlaceHolder": "请选择", "templateCommon.reset": "重置", "templateCommon.search": "搜索", "templateCommon.searchPlaceholder": "输入搜索内容", "templateCommon.btnCreateTemp": "立即创建模板", "templateCommon.next": "下一步", "templateCommon.previous": "上一步", "templateCommon.rename": "重命名", "templateCommon.stick": "置顶", "templateCommon.complete": "完成", "templateCommon.tip": "提示", "templateCommon.quit": "退出", "templateCommon.copySucc": "复制成功", "templateCommon.copyFail": "复制失败", "templateCommon.setSuccess": "设置成功", "templateCommon.understand": "我知道了", "templateCommon.setting": "更多设置", "templateCommon.moneyUnit.RMB": "人民币", "templateCommon.moneyUnit.EUR": "欧元", "templateCommon.moneyUnit.Dollar": "美元", "templateCommon.moneyUnit.GBP": "英镑", "templateCommon.selectAll": "全选", "templateCommon.useTip": "温馨提示", "templateCommon.goOn": "继续", "templateCommon.abandon": "放弃", "templateCommon.tempSave": "暂存并退出", "templateCommon.allType": "不限格式", "templateCommon.warmTip": "温馨提示", "templateCommon.specialSealConfirm": "以下签署方未配置模板专用章，可以使用<b>任意印章</b>签署：", "templateConfigGuide.contract": "合同", "templateConfigGuide.hrContract": "人事合同", "templateConfigGuide.salesContract": "经销商合同", "templateConfigGuide.userGuide": "快捷设置引导", "templateConfigGuide.title": "模板快捷设置引导", "templateConfigGuide.sendContract": "发起合同", "templateConfigGuide.funExplain": "功能释义", "templateConfigGuide.functionDes": "模板是对一类业务中用到的合同的初始化配置，可配置签约方、签约条件、及签署位置等。配置好后，在下一阶段使用模板时可以直接引用这些配置条件，将合同发送给不同企业/个人。", "templateConfigGuide.configUse": "开始设置", "templateConfigGuide.selectTemplateSceneType": "根据您合同的使用场景，从以下模板中选择，开始配置。", "templateConfigGuide.hrScene": "人事场景模板", "templateConfigGuide.salesScene": "经销商场景模板", "templateConfigGuide.commonScene": "其他场景模板", "templateConfigGuide.sendContractDirect": "无需创建模板，快速发起合同", "templateConfigGuide.uploadFile": "上传文件（标准合同文件已准备好）", "templateConfigGuide.uploadTemplateFile": "上传合同模板文件", "templateConfigGuide.uploadingTip": "请等待文档上传完成后操作", "templateConfigGuide.addContractTip": "您需要在“上传文件”中上传文档，或在“文件预留”中将空白文档位的数量设为1", "templateConfigGuide.uploadFileDesc": "上传标准合同文件（包含标准合同文本内容），文件中需保留原合同内容空白处，这些空白处未来可供发件方或收件方填充具体内容值生成不同合同。请点击并上传文件。", "templateConfigGuide.sceneDoc.HR": "人事场景中有劳动合同、保密协议", "templateConfigGuide.sceneDoc.SALES": "经销商场景中有经销商代理协议、企业货物运输合同", "templateConfigGuide.sceneDoc.COMMON": "通用场景中主要是常用的合同", "templateConfigGuide.filePreserve": "文件预留（合同文件未准备好）", "templateConfigGuide.filePreserveDesc1": "若您未准备好合同文件，可通过此步骤为文件预留文档位，文件有几份即需要占几位，后续在使用合同时可补充准备好的合同文件。", "templateConfigGuide.blankDocNumIs": "待上传的文档位需要设置数量是", "templateConfigGuide.fillDesField": "填写合同描述字段", "templateConfigGuide.setDesFieldTip1": "合同描述字段方便您对合同本身进行记录、管理。上传完文件后，填写合同描述字段，如合同类型、公司内部编号等。", "templateConfigGuide.setDesFieldTip2": "合同类型、公司内部编号、签约截止时间、合同到期日属于系统默认描述字段，您可点击有色字体“字段设置”，配置是否展示或隐藏这些描述字段。", "templateConfigGuide.contractTypeDesp1": "如果您想增加合同类型，可进入“", "templateConfigGuide.contractTypeDesp2": "”，选择“合同类型”的编辑按钮，自主增加类型描述。", "templateConfigGuide.consoleRouteText": "企业控制台-业务字段管理-合同描述", "templateConfigGuide.configBusinessFieldInfo": "自定义合同描述字段", "templateConfigGuide.descFieldDesp1": "除了“合同类型”合同描述字段外，您也可以自定义其他描述字段，您可以进入", "templateConfigGuide.descFieldDesp2": "自定义设置。", "templateConfigGuide.backToPage": "回到页面", "templateConfigGuide.setContractType": "填写合同类型", "templateConfigGuide.setFieldNecessary": "设置合同描述项选必填", "templateConfigGuide.setReceiverRole": "设置签约角色", "templateConfigGuide.receiverRoleExplain": "签约角色指合同中需要盖章/签字的企业或个人。", "templateConfigGuide.backToSetTemplateName": "您需要回到界面进行信息补充", "templateConfigGuide.hrTemplateRoleDesc": "人事场景已默认配置好两个角色，", "templateConfigGuide.salesRoleDesc": "经销商场景已默认配置好两个角色，", "templateConfigGuide.commonRoleDesc": "通用场景已默认配置好一个角色，", "templateConfigGuide.changeRoleNameTip": "您可以修改角色名称，或继续新增签约角色", "templateConfigGuide.role": "签约角色{num}：", "templateConfigGuide.addPersonRole": "+ 新增个人签约角色", "templateConfigGuide.addEntRole": "+ 新增企业签约角色", "templateConfigGuide.entPlaceholder": "如：企业、部门", "templateConfigGuide.personPlaceholder": "如：员工", "templateConfigGuide.batchImportRoleInfo": "批量添加签约角色", "templateConfigGuide.batchImportRoleInfoDesc": "若在使用模板发送合同时，需要批量发送给不同企业/个人，可在对应的角色后勾选“需要使用excel批量导入账号模式”。系统会自动根据您excel中的签约角色和账号进行匹配。请选择需要批量发送的角色：", "templateConfigGuide.needExcelImport": "需要使用excel批量导入账号", "templateConfigGuide.person": "个人", "templateConfigGuide.ent": "企业", "templateConfigGuide.setRoleSignConfig": "设置签约角色要求", "templateConfigGuide.setRoleSignConfigDesc": "可提前设置签约角色的签署方式及签署要求。已明确的请直接填写，未明确的，可在使用模板发送合同时补充。", "templateConfigGuide.pointPosition": "指定签署位置", "templateConfigGuide.pointPositionDesc": "在此步骤，可以在文件的空白处设置占位符，可从页面左侧“临时字段”中选择符合的类型。并可在页面右侧，指定此占位符的名称及填写人。", "templateConfigGuide.templateConfigDesc": "您的模板已经具备基本的可用性。通过以下步骤可以进一步提升模板的功能性：1.合理分配各类模板权限，确保其分配给合适的人员或角色；2.访问模板详情页面，完成进一步的配置工作。", "templateConfigGuide.templateConfig": "模板权限设置", "templateConfigGuide.addContractField": "在文件盖章/签字处，可为各个签署人指定签署位置。以空白文档为例，您需要进行如下操作", "templateConfigGuide.addSignFieldPosition": "若是空白文档，需要提前设置文档中每一个签约色盖章/签字处含有的关键字，通过关键字定位签署位置。", "templateConfigGuide.templateConfigAuthTip": "通过 模板管理-权限管理 定义模板权限", "templateConfigGuide.templateDetailTip": "通过 模板详情页 设置应对模板使用中的各种场景", "templateConfigGuide.toSetting": "去设置", "templateConfigGuide.employee": "员工", "templateConfigGuide.hr": "公司人事部", "templateConfigGuide.dealer": "经销商企业", "templateConfigGuide.finance": "公司财务部", "templateConfigGuide.entOwn": "自己本企业", "templateDetail.basic.docComId": "文档组合编号：", "templateDetail.basic.docComCreater": "文档组合创建人：", "templateDetail.basic.docComCreateTime": "文档组合创建时间：", "templateDetail.basic.docComRemark": "文档组合备注：", "templateDetail.basic.batchSendContract": "批量发送合同", "templateDetail.basic.singleSendContract": "单独发送合同", "templateDetail.basic.edit": "编辑", "templateDetail.basic.doc": "文档：", "templateDetail.basic.sender": "合同发件方：", "templateDetail.basic.senderAccount": "合同发件人：", "templateDetail.basic.receiver": "合同收件方：", "templateDetail.basic.receiverAccount": "合同收件人：", "templateDetail.basic.contractEndTime": "合同到期时间：", "templateDetail.basic.signEndTime": "签约到期时间：", "templateDetail.basic.endTimeUnit": "（天）", "templateDetail.basic.contractType": "合同类型：", "templateDetail.basic.entNo": "公司内部编号：", "templateDetail.basic.area": "所属区域：", "templateDetail.basic.senderField": "待发件方填写字段：", "templateDetail.basic.signerField": "待签署方填写字段：", "templateDetail.basic.contractInfo": "合同信息", "templateDetail.basic.pageIndex": "页数：{page}页", "templateDetail.basic.empty": "待填写", "templateDetail.basic.invalidStatementName": "与原模板保持一致", "templateDetail.msg.editSuccess": "修改成功", "templateDetail.msg.editFail": "修改失败", "templateDetail.msg.whiteDocument": "文档内容为空", "templatePermission.tempMgmt": "模板管理", "templatePermission.permissionMine": "授权方案", "templatePermission.permissionAll": "全部权限", "templatePermission.permissionAllTitle": "授权结果", "templatePermission.withdrawPermissionAll": "收回权限", "templatePermission.withdrawPermissionDeny": "无操作权限", "templatePermission.admin": "主管理员", "templatePermission.staff": "员工", "templatePermission.myTemplatePermissions": "当前账号的模板权限包含：", "templatePermission.permissionLog": "授权日志", "templatePermission.new": "新建", "templatePermission.search.composeName": "权限方案名称", "templatePermission.search.auth": "权限项", "templatePermission.search.name": "姓名", "templatePermission.search.account": "账号", "templatePermission.search.role": "角色", "templatePermission.table.permissionFederationName": "权限方案名称", "templatePermission.table.createPermissionFederation": "新建权限方案", "templatePermission.table.btnTooltips": "权限方案允许您建立一系列权限控制，并将权限方案应用到其他模板中。获得权限的人员才能对模板执行相关操作。", "templatePermission.table.editPermission": "编辑权限", "templatePermission.table.auth": "权限项", "templatePermission.table.authTo": "授权给", "templatePermission.table.operate": "操作", "templatePermission.table.authToEmployes": "被授权人员：", "templatePermission.table.authToRoles": "被授权角色：", "templatePermission.table.operateAuthTo": "授权", "templatePermission.table.time": "时间", "templatePermission.table.detail": "详情", "templatePermission.table.distribute": "分配", "templatePermission.table.withdraw": "撤回", "templatePermission.table.permission": "权限", "templatePermission.table.deleteSelectTip": "请先选择需要删除的权限方案", "templatePermission.table.resetPermission": "重新授权", "templatePermission.table.role": "角色", "templatePermission.table.roleName": "角色/成员名称", "templatePermission.table.companyName": "公司", "templatePermission.table.permissionDetail": "权限明细", "templatePermission.table.all": "所有", "templatePermission.dialog.authToRoles": "角色", "templatePermission.dialog.selectEnt": "选择企业", "templatePermission.dialog.selectRole": "选择角色", "templatePermission.dialog.selectedRole": "已选角色", "templatePermission.dialog.authToEmployes": "人员", "templatePermission.dialog.selectEmployes": "选择人员", "templatePermission.dialog.selectedEmployes": "已选人员", "templatePermission.dialog.recoverEmployesPermission": "保留人员已有权限", "templatePermission.dialog.recoverRolesPermission": "保留角色已有权限", "templatePermission.dialog.keepPermissionTip": "勾选则在现有权限的基础上继续追加新权限；去除勾选则代表将先回收现有权限，然后根据新方案授权（仅影响选定的角色或人员，未被选中者仍保持原有权限不变）。", "templatePermission.dialog.all": "全选", "templatePermission.dialog.selectPermissionFederation": "选择授权方案", "templatePermission.dialog.confirm": "确定", "templatePermission.dialog.cancel": "取消", "templatePermission.dialog.deletePermissionTipContent": "是否收回此角色的权限？", "templatePermission.dialog.deletePermissionTipContent1": "是否收回此账号的权限？", "templatePermission.dialog.deletePermissionTipContent2": "是否确定收回账号/角色的权限", "templatePermission.dialog.tip": "提示", "templatePermission.slide.editComposeName": "权限方案名称", "templatePermission.slide.editPermission": "编辑权限", "templatePermission.slide.save": "保存", "templatePermission.slide.tip": "修改权限方案不影响已按原方案分配给成员的权限，他们仍保留原权限。", "templatePermission.slide.nameNotEmpty": "权限方案名称不能为空", "templatePermission.slide.checkNotEmpty": "权限勾选不能为空", "templatePermission.slide.selectPermissionTitle": "选择授权权限", "templatePermission.msg.editSuccess": "编辑完成", "templatePermission.msg.editFail": "编辑失败", "templatePermission.msg.createSuccess": "新建成功", "templatePermission.msg.createFail": "新建失败", "templatePermission.msg.unselectFederation": "请先选择一个权限方案", "templatePermission.msg.deleteSuccess": "删除成功", "templatePermission.msg.federationEmpty": "您还没有权限方案，请先建权限方案", "templatePermission.msg.permissionSuccess": "授权成功", "templatePermission.permissonAuthChoose.title": "选择要收回的权限", "templatePermission.permissonMember": "成员/角色权限", "templatePermission.mainAdminRole": "主管理员", "templatePermission.employeeRole": "员工", "editCompose.edit": "修改文档组合", "editCompose.name": "组合名称", "editCompose.id": "组合编号", "editCompose.comment": "文档备注", "editCompose.selectDoc": "选择合同：", "editCompose.currentCompose": "当前文档组合：", "editCompose.confirm": "确定", "editCompose.errorTip.nameNeed": "请输入文档组合名称", "editCompose.errorTip.idNeed": "请输入文档组合编号", "editCompose.errorTip.selectedNeed": "请至少选择2项", "templateList.manager": "管理人", "templateList.managerTip": "拥有分配权限的人员", "templateList.createTemp": "创建模板", "templateList.createDynamicTemp": "创建动态模板", "templateList.createOfdTemp": "创建OFD模板", "templateList.dynamicTemplateEntry": "动态模板入口", "templateList.myTemp": "我的模板", "templateList.allTemp": "所有模板", "templateList.myCreateTemp": "我创建的", "templateList.grantedTemp": "授权给我的", "templateList.approvalTemp": "待我审批", "templateList.tempName": "模板名称", "templateList.tempMark": "模板备注", "templateList.tempId": "模板编号", "templateList.star": "收藏", "templateList.cancelStar": "取消收藏", "templateList.starSuccess": "收藏成功", "temolateList.starFolder": "收藏夹", "templateList.templateIdTip": "模板编号格式错误，请输入数字", "templateList.cancelStarSuccess": "取消收藏成功", "templateList.hasCacheDraft": "有一份合同曾在{time}被暂存尚未完成发送，是否继续上次操作？", "templateList.templateCategory": "模板类型", "templateList.dynamicTemplate": "动态模板", "templateList.staticTemplate": "普通模板", "templateList.createUser": "创建人", "templateList.creatingCompany": "创建企业", "templateList.folder": "文件夹", "templateList.tempPermission": "模板权限", "templateList.permissionMgmt": "权限管理", "templateList.enableStatus": "启用状态", "templateList.enabled": "启用中", "templateList.disabled": "停用中", "templateList.batchPermission": "批量授权", "templateList.permissionSuccess": "授权成功", "templateList.selectPermission": "请选择一个权限组", "templateList.selectFederation": "选择权限方案", "templateList.selectRole": "选择角色", "templateList.selectUser": "选择人员", "templateList.enableTemp": "启用模板", "templateList.disableTemp": "停用模板", "templateList.enableConfirmText": "启用后，被授权使用人将可以使用该模板，确定启用吗？", "templateList.disableConfirmText": "停用后该模板将无法被使用，确定停用吗？", "templateList.enableSuccess": "启用成功", "templateList.disableSuccess": "停用成功", "templateList.deleteTemp": "删除模板", "templateList.deleteFail": "删除失败", "templateList.deleteTempConfirmText": "删除模板后， 将无法恢复， 确定删除模板吗？", "templateList.editInfoTip": "该模板仍在编辑中，如需使用请联系模板创建人", "templateList.enable": "启用", "templateList.disable": "停用", "templateList.switchToReceiver": "已为您切换至 {receiver} ", "templateList.createUserPlaceholder": "输入须包含名称开头部分", "templateList.sendCode": "发送码", "templateList.moreFeature": "高级功能", "templateList.downloadSendCode": "下载模板发送码", "templateList.generate": "生成", "templateList.view": "查看", "templateList.close": "关闭", "templateList.alwaysEnable": "长期有效", "templateList.customDays": "自定义天数", "templateList.remainDays": "剩余{day}天", "templateList.selectValidDate": "选择到期时间：", "templateList.selectValidDateTip": "请选择到期时间", "templateList.selectValidDateErrorTip": "请选择当前时间点以后的时间", "templateList.validToDate": "有效期至：", "templateList.inputIntNumberPlaceholder": "请选择999天以内的时间", "templateList.setExpireDays": "设置模板可用天数", "templateList.noPermissions": "需要获得模板编辑权限后才可以修改", "templateList.sendCodeTip": "当前模板设置不符合发送码生成条件，请检查是否符合以下要求：", "templateList.errorOperate": "无操作模版权限", "templateList.sendCodeTipFail.1": "不包含空白文档和单据合同", "templateList.sendCodeTipFail.2": "签约方只有一个可变方（包含签署和抄送），且可变方必须是第一操作人；签署人必须设有签字盖章处", "templateList.sendCodeTipFail.3": "固定角色的账号或企业名称不能为空", "templateList.sendCodeTipFail.4": "不会触发发送前审批", "templateList.sendCodeTipFail.5": "发件方必填字段不为空（含描述字段和合同内容字段）", "templateList.sendCodeTipFail.6": "非模板组合", "templateList.sendCodeGuide.title": "发送码高级功能说明", "templateList.sendCodeGuide.info": " 模板发送码适用于不需要发件方填写内容的合同文件，例如离职证明、保密承诺书、授权委托书等，可提供任意扫码人员获取。若文件中有发件方必须填写的资料，或者发件方需要限定扫码获取到文件的相对方范围，可以使用档案+的高级功能。通过扫描档案柜的二维码，可以完成指定人员的合同自动发送，并且填充发件方需要填写的内容，甚至控制自动发送的时间节点；扫码的相对方也会存放在档案柜中，可以随时查询。具体使用方法如下：", "templateList.sendCodeGuide.tip1.main": "1. 上上签", "templateList.sendCodeGuide.tip1.sub": "", "templateList.sendCodeGuide.tip1.line1": "向上上签申请开通档案+、合同预审、智能预审", "templateList.sendCodeGuide.tip1.line2": "开通后可以到对应的菜单中操作使用", "templateList.sendCodeGuide.tip2.main": "2. 档案柜管理员", "templateList.sendCodeGuide.tip2.sub": "创建档案柜、配置智能预审", "templateList.sendCodeGuide.tip2.line1": "", "templateList.sendCodeGuide.tip2.line2": "在档案柜中创建与合同内容相同的填写字段、绑定合同模板、设置对应关系以及扫码自动发出的条件，将档案柜的二维码提供给签约的相对方。", "templateList.sendCodeGuide.tip3.main": "3. 签约方", "templateList.sendCodeGuide.tip3.sub": "扫码填资料、获取合同文件", "templateList.sendCodeGuide.tip3.line1": "", "templateList.sendCodeGuide.tip3.line2": "签约方扫描发件方提供的档案柜发送码进行填写，通过档案柜收集到的资料会存档并且自动填充到合同中，对方收到合同只需要简单盖章或签名，即可完成合同签署", "templateList.sendCodeGuide.tip4.main": "4. 档案柜管理员", "templateList.sendCodeGuide.tip4.sub": "", "templateList.sendCodeGuide.tip4.line1": "查看签约的相对方、发送的合同情况", "templateList.sendCodeGuide.tip4.line2": "发件方的管理员可以到档案柜中，查看扫码的相对方信息、合同发出的情况、是否完成签署等", "templateList.linkBoxTip": "关联档案柜ID：", "templateApproval.approvalProcess": "审批流程", "templateApproval.approvalStatus": "审批状态", "templateApproval.approving": "审批中", "templateApproval.toBeApproval": "需审", "templateApproval.approved": "已审", "templateApproval.approver": "审批人", "templateApproval.reporter": "提报人", "templateApproval.reject": "驳回", "templateApproval.agree": "同意", "templateApproval.backList": "返回列表", "templateApproval.signerInfo": "签署人信息", "templateApproval.rejectSuccess": "驳回成功", "templateApproval.approvalSuccess": "审批完成", "templateApproval.approvalStopTip": "审批人或待审批的内容已发生变化，请返回“待我审批”重新进入模板审批页面", "templateApproval.approvalAttention": "新提交的审批可能有若干秒的延迟，稍后刷新查看", "templateApproval.approvalMessage": "审批留言：", "templateApproval.approvalAgree": "审批通过", "templateApproval.approvalReject": "审批驳回", "templateApproval.templateDocumentsInfo": "模板文档信息", "templateApproval.templateDocumentName": "模板文档名称", "templateApproval.templateDocumentId": "模板编号(documentId)", "templateApproval.templateApprovingTip": "审批中的模板不能进行此操作", "templateListEntDialog.title": "模板批量授权", "templateListEntDialog.entGroupNums.1": "所选模板分属", "templateListEntDialog.entGroupNums.2": "个不同企业主体（不能跨主体授权），请根据不同主体依次授权", "templateListEntDialog.authed": "已授权", "templateListEntDialog.next": "下一步", "templateListEntDialog.confirm": "确定", "templateFolder.add": "新建文件夹", "templateFolder.edit": "编辑文件夹", "templateFolder.folder": "文件夹名称", "templateFolder.nameEmptyTip": "请输入文件夹名称", "templateFolder.nameInvalidTip": "请输入中文、英文、数字的字符组合", "templateFolder.selectFolderTip": "请选择文件夹", "templateFolder.groupSuccess": "归档成功", "templateFolder.deleteFolderTip": "删除后，文件夹中的模板将被释放，确认删除吗？", "templateFolder.deleteSucc": "删除成功", "templateSubList.tempDocument": "模板文档", "templateSubList.tempFederation": "模板文档组合", "templateSubList.templateSpecialSeal": "模板专用章", "templateSubList.tempSceneConfig": "场景定制", "templateSubList.tempFederationTip": "点此查看包含的文档组合", "templateSubList.tempInvalidStatement": "作废申明", "templateSubList.tempSupplement": "补充协议", "templateSubList.documentName": "文档名称", "templateSubList.federationId": "组合编号", "templateSubList.noFederationData": "模板还没有设置文档组合", "templateSubList.federationOptTip1": "在“模板文档”tab页配置文档组合", "templateSubList.federationOptTip2": "在发送合同时快速发出合同组合", "templateSubList.federationName": "组合名称", "templateSubList.useFederation": "组合使用", "templateSubList.createFederation": "组成文档组合", "templateSubList.federationMark": "组合备注", "templateSubList.inputFederationName": "请输入组合名称", "templateSubList.addTempFederation": "新增文档组合", "templateSubList.addTempIsAutoJoin": "新上传的文档是否自动加入这个文档组合", "templateSubList.delete": "删除", "templateSubList.deleteDocument": "删除文档", "templateSubList.deleteDocumentTipDesc": "是否删除该文档，删除后不可恢复", "templateSubList.confirmTitle": "提示", "templateSubList.deleteSuc": "删除成功!", "templateSubList.deleteFederationTipDesc": "是否删除该组合，删除后不可恢复", "templateSubList.sceneConfig.saveConfig": "保存配置", "templateSubList.sceneConfig.saveSuccess": "保存成功", "templateSubList.sceneConfig.limitedTitle": "限制条件:", "templateSubList.sceneConfig.manageVerifyCode.showCodeInContract": "在合同中显示查验码", "templateSubList.sceneConfig.manageVerifyCode.selectCodeStyle": "请选择查验码的样式", "templateSubList.sceneConfig.manageVerifyCode.preView": "预览", "templateSubList.sceneConfig.manageVerifyCode.limitOtherSignatureTip": "在合同中显示查验码不适用跨平台加签，请禁用\"跨平台加签\"配置后再操作", "templateSubList.sceneConfig.manageVerifyCode.selectCodePage": "请选择查验码展示的位置", "templateSubList.sceneConfig.manageVerifyCode.lastPage": "合同最后一页", "templateSubList.sceneConfig.manageVerifyCode.allPage": "合同全部页", "templateSubList.sceneConfig.manageVerifyCode.allPageTip": "请确保合同每页大小保持一致，方可展示全部查验码。推荐使用竖版A4文档。", "templateSubList.sceneConfig.INNER_RESOLUTION.title": "内部决议场景", "templateSubList.sceneConfig.INNER_RESOLUTION.description": "功能效果：为企业签约方设置了多个签约角色代表企业签字+1个签约角色代表企业盖章，当企业盖章后，该企业未签名的签约角色无需继续签署（已签字的签名仍生效）", "templateSubList.sceneConfig.INNER_RESOLUTION.limitItems.0": "必须启用隐藏拒签按钮", "templateSubList.sceneConfig.INNER_RESOLUTION.limitItems.1": "不适用于\"顺序签署\"场景", "templateSubList.sceneConfig.INNER_RESOLUTION.limitItems.2": "不适用于\"允许改用纸质签署\"场景", "templateSubList.sceneConfig.INNER_RESOLUTION.hideRefuseBtnConfigTip": "启用内部决议，需首先启用隐藏拒签按钮", "templateSubList.sceneConfig.INNER_RESOLUTION.limitPageSignTip": "内部决议不适用纸质签署场景，请禁用\"允许改用纸质签署\"配置后再操作", "templateSubList.sceneConfig.INNER_RESOLUTION.limitHideRefuseTip": "纸质签署不适用内部决议场景，请禁用\"内部决议场景\"配置后再操作", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.title": "跨平台加签", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.description": "允许在使用模板时上传带数字证书的PDF（即已在其他签约平台签署过的合同）", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.0": "不适用于\"在合同中显示查验码\"场景", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.1": "不适用于\"合同装饰\"应用场景，含水印、图片字段", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.2": "不适用于\"填写内容字段（发件人或签署人）\"场景", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.3": "不适用于\"合同附件功能\"", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.note": "使用小技巧：配合“模板专用章”功能后，还可以控制“跨平台互签”合同的发起后的签署行为。", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.conflictVerifyCode": "跨平台加签不适用查验码场景，请禁用\"在合同中显示查验码\"配置后再操作", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.updateHybridJarTip": "Jar包版本过低，请联系上上签支持人员升级", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent1": "已包含数字证书的文件不能作为模板文档使用。 | 尚未开启跨平台加签的模板不能使用带数字证书的文件。", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent2": "建议您：", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent3": "第一步：创建一个模板并在“更多设置-场景定制”启用“跨平台加签”（如无该配置，则需升级到付费版本）；", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent4": "第二步：在使用该模板发送合同时，上传包含数字证书的PDF，实现跨平台加签", "templateSubList.sceneConfig.paperSign.changeToPaperSign": "允许改用纸质签署", "templateSubList.sceneConfig.paperSign.hideOtherAccountLogin": "隐藏使用其他账号签约：入口隐藏后，可能会造成签署阻碍，因此该功能只适用于当面签、双方签，请谨慎隐藏", "templateSubList.sceneConfig.paperSign.signTitle": "企业签约方可以使用纸质盖章后邮寄的方式签署合同，无需使用电子签名。", "templateSubList.sceneConfig.paperSign.signTip": "纸质签署的法律有效性由传统方式保障，无法由本平台保障，请注意证据的收集、保存。", "templateSubList.sceneConfig.paperSign.postAddress": "邮寄地址：", "templateSubList.sceneConfig.paperSign.receiverName": "邮寄接收人姓名：", "templateSubList.sceneConfig.paperSign.receiverContract": "邮寄接收人联系方式：", "templateSubList.sceneConfig.paperSign.postAddressFill": "发件方企业的具体地址，选填", "templateSubList.sceneConfig.paperSign.receiverNameFill": "发件方企业的纸质合同接收人姓名，选填", "templateSubList.sceneConfig.paperSign.receiverContractFill": "发件方企业的纸质合同接收人的手机号，选填", "templateSubList.sceneConfig.paperSign.preview": "预览", "templateSubList.sceneConfig.paperSign.pdfRequired": "签署人必须提供纸质合同扫描件（PDF）", "templateSubList.sceneConfig.paperSign.paperErrorTip.receiverName": "请输入正确的接受人姓名", "templateSubList.sceneConfig.paperSign.paperErrorTip.receiverInfo": "联系方式有误，请检查后再填写", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.notice": "注意事项：", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.tip1": "合同中先签署的签约方已使用纸质签署方式，则未签署的签约方也必须使用纸质签署方式；", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.tip2": "建议配置顺序签。", "templateSubList.sceneConfig.paperSign.saveConfig": "保存纸质签设置", "templateSubList.sceneConfig.paperSign.advance": "纸质签高级要求", "templateSubList.sceneConfig.paperSign.advanceRadioText.freeChoice": "自由选择：由签约方自由选择使用纸质签或选择电子签", "templateSubList.sceneConfig.paperSign.advanceRadioText.mustUsePaperSign": "必须纸质签：所有签署人必须使用纸质签署方式，不能使用电子签方式", "templateSubList.sceneConfig.paperSign.advanceRadioText.separateConfig": "单独配置纸质签：默认使用电子签，可为合同单独配置“需纸质签”。即：发件方在某份合同详情页中开启“需纸质签”后，该合同签约方必须使用纸质签，而其他未配置的合同仍必须使用电子签", "templateSubList.sceneConfig.paperSign.openSequence": "开启顺序签", "templateSubList.sceneConfig.paperSign.cancel": "取消", "templateSubList.sceneConfig.paperSign.confirm": "确定", "templateSubList.sceneConfig.limitInnerResolutionTip": "内部决议场景依赖于隐藏拒签按钮，请禁用\"内部决议场景\"配置后再操作", "templateSubList.sceneConfig.limitSettingTip": "请先查看右侧边栏的设置说明", "templateSubList.sceneConfig.openSuccess": "开启成功", "templateSubList.sceneConfig.closeSuccess": "关闭成功", "templateSubList.sceneConfig.hideRefuseSignButton": "隐藏拒签按钮", "templateSubList.sceneConfig.viewCase": "查看示例", "templateSubList.sceneConfig.enableContractAlias": "启用\"合同\"的别名", "templateSubList.sceneConfig.contractAliasDesc": "合同接收方在签署此模板合同的过程中，看到的“合同”字样，由以下文案代替。", "templateSubList.sceneConfig.hideSenderInfoInPreviewPage": "隐藏预览页的发件方信息", "templateSubList.sceneConfig.isAutoSignConfigOn": "是否允许签约方启用“自动签”功能签署此模板发送的合同", "templateSubList.sceneConfig.autoSign": "签约方自动签管控", "templateSubList.sceneConfig.selfEntAccessOnly": "仅允许我方企业/集团可以使用自动盖章（含发给企业的签字，但不包含发给个人的签字），其他合同签约方均不可以自动签", "templateSubList.sceneConfig.close": "关闭", "templateSubList.sceneConfig.save": "保存", "templateSubList.sceneConfig.signRoleEnable": "以下签约角色也可以使用自动签", "templateSubList.sceneConfig.allSignerNoAccess": "所有签约方均不可自动签", "templateSubList.sceneConfig.autoSignNotAllowTip.notice": "您设置签署方自动签署的条件后，还需要签署方主动启用基于印章或签名的自动签功能，才能最终完成对合同的自动签署", "templateSubList.sceneConfig.autoSignNotAllowTip.title": "配置了以下签署要求的签约方不能使用自动签功能签署此模板发送的合同：", "templateSubList.sceneConfig.autoSignNotAllowTip.tip1": "必须手写签名", "templateSubList.sceneConfig.autoSignNotAllowTip.tip2": "必须刷脸签署", "templateSubList.sceneConfig.autoSignNotAllowTip.tip3": "接收方付费", "templateSubList.sceneConfig.autoSignNotAllowTip.tip4": "签署方填写字段", "templateSubList.sceneConfig.autoSignNotAllowTip.tip5": "签署方提交资料", "templateSubList.sceneConfig.autoSignNotAllowTip.tip6": "手写笔记识别", "templateSubList.sceneConfig.autoSignNotAllowTip.tip7": "阅读完毕再签署", "templateSubList.sceneConfig.autoSignNotAllowTip.tip8": "需校验经办人实名信息，但经办人实名信息不一致或未实名", "templateSubList.sceneConfig.autoSignNotAllowTip.tip9": "业务核对章", "templateSubList.sceneConfig.autoSignNotAllowTip.tip10": "企业多人签字", "templateSubList.sceneConfig.autoSignNotAllowTip.tip11": "FDA签名", "templateSubList.sceneConfig.specificationBusinessFields": "规范合同业务字段", "templateSubList.sceneConfig.descriptionField": "合同描述字段", "templateSubList.sceneConfig.contentField": "合同内容字段", "templateSubList.sceneConfig.addField": "新增字段", "templateSubList.sceneConfig.tip": "提示", "templateSubList.sceneConfig.deleteConfirmTip.0": "删除后，模板中已设置的字段将直接消失；您可以直接修改字段名称，模板中已设置的字段的属性不变，但字段名称使用更名后的名称。", "templateSubList.sceneConfig.deleteConfirmTip.1": "是否继续删除？", "templateSubList.sceneConfig.deleteSuc": "删除成功", "templateSubList.sceneConfig.editName": "修改名称", "templateSubList.sceneConfig.fda.enableFdaSignature": "启用FDA签名样式", "templateSubList.sceneConfig.fda.description": "勾选后签名符合FDA 21 CFR Part 11，对企业签字及个人签名生效", "templateSubList.sceneConfig.fda.addOption": "添加选项", "templateSubList.sceneConfig.fda.allowInput": "允许签署人自行输入", "templateSubList.sceneConfig.fda.allowInputTip": "勾选后签署人可自行编辑Reason", "templateSubList.sceneConfig.fda.inputOption": "请输入选项", "templateSubList.sceneConfig.fda.inputOptionLimit": "请输入长度小于{limit}的内容", "templateSubList.sceneConfig.signerWatch.title": "签署关键人跟踪", "templateSubList.sceneConfig.signerWatch.target": "跟进人：", "templateSubList.sceneConfig.signerWatch.sender": "发件人", "templateSubList.sceneConfig.signerWatch.CC_USER": "抄送人", "templateSubList.sceneConfig.signerWatch.SIGNER": "签署人", "templateSubList.sceneConfig.signerWatch.COMPLETE": "补全", "templateSubList.sceneConfig.signerWatch.role": "签约角色：", "templateSubList.sceneConfig.signerWatch.configResult": "配置完成后，跟进人在关心的角色签署完成后，会收到邮件通知。", "templateSubList.sceneConfig.signerWatch.emailContent": "邮件标题为：“《XXX合同》- 张三已签署完成”便于快速推进业务。", "templateSubList.sceneConfig.signerWatch.configTipHead": "温馨提示：", "templateSubList.sceneConfig.signerWatch.setEmailTip": "1）请提前提醒会收到该通知的同事，配置好通知邮箱（如果登录账号已经是邮箱的，可以不用配置)；", "templateSubList.sceneConfig.signerWatch.limitedTip": "2）跟进人暂不支持前台代收、跨业务线认领等待认领合同的签署人；", "templateSubList.sceneConfig.signerWatch.signOrderlyTip": "3）该功能暂不支持非顺序签，请在使用模板时，注意勾选上【顺序签署】。", "templateSubList.sceneConfig.customsCheck": "海关加签文件检测，辅助检查文件是否符合海关加签标准（针对文件名称长度、文件字体以及是否含有空白页进行检测）", "templateSubList.sceneConfig.enterpriseSignatureOnly": "允许企业签字单独使用", "templateSubList.sceneConfig.isSwitch": "是否开启", "templateSubList.sceneConfig.featureDescription": "功能描述", "templateSubList.sceneConfig.featureConfiguration": "功能配置", "templateSubList.specialSeal.templateRole": "模板角色", "templateSubList.specialSeal.isLimitEnable": "是否启用专用章", "templateSubList.specialSeal.configLimitInfo": "设置专用章", "templateSubList.specialSeal.configLimitInfoOwn": "设置己方专用章", "templateSubList.specialSeal.configLimitInfoOther": "设置相对方专用章", "templateSubList.specialSeal.settingTip": "提示", "templateSubList.specialSeal.companySealSetTip": "已为该企业设置了专用章，是否代替原有专用章？", "templateSubList.specialSeal.companySealSetTip1": "以下企业已经设置了专用章：", "templateSubList.specialSeal.companySealSetTip2": "是否需要更新为此次设置的专用章", "templateSubList.specialSeal.deleteTip": "删除模板专用章提示", "templateSubList.specialSeal.confirmTip": "确认删除模板专用章？", "templateSubList.specialSeal.enable": "已启用", "templateSubList.specialSeal.disable": "未启用", "templateSubList.specialSeal.config": "查看编辑", "templateSubList.specialSeal.sealImgSource": "专用章来源", "templateSubList.specialSeal.fromTemplate": "来自模板的印章", "templateSubList.specialSeal.fromContract": "来自合同的印章", "templateSubList.specialSeal.sourceIdInputTip": "请输入模板编号或合同编号", "templateSubList.specialSeal.inputIdText": "请输入{text}", "templateSubList.specialSeal.templateNo": "模板编号", "templateSubList.specialSeal.contractNo": "合同编号", "templateSubList.specialSeal.limitedSealsList": "当前专用章列表", "templateSubList.specialSeal.newLimitSealsList": "本次新增的专用章", "templateSubList.specialSeal.limitSealTip": "已存在的专用章图案不会被更新。如需更新，您需要删除该专用章后再操作", "templateSubList.specialSeal.noSealTip": "暂无该模板发送合同相关的印章图案|暂无合同相关的印章图案", "templateSubList.specialSeal.correctNoTip": "请输入正确的{text}", "templateSubList.specialSeal.confirm": "确认", "templateSubList.specialSeal.cancel": "取消", "templateSubList.specialSeal.save": "保存", "templateSubList.specialSeal.saveSuccess": "保存成功", "templateSubList.specialSeal.removeSuccess": "删除成功", "templateSubList.specialSeal.sameSealTip": "已设置该印章", "templateSubList.specialSeal.companyName": "企业", "templateSubList.specialSeal.plsInputEnterpriseName": "请输入企业名称", "templateSubList.invalidStatement.description.0": "通过《作废申明》可将合同的状态变更为“已作废”。《作废申明》会向所有人公布（含未参与合同的人员），具体途径为：", "templateSubList.invalidStatement.description.1": "（1）已作废合同在上上签官网验签后的验签结果展示页中展示；", "templateSubList.invalidStatement.description.2": "（2）已作废合同上的合同查验码，经扫一扫后打开的合同查验页展示。", "templateSubList.invalidStatement.reset": "重置《作废申明》", "templateSubList.invalidStatement.delete": "删除", "templateSubList.invalidStatement.page": "页", "templateSubList.invalidStatement.create": "设置《作废申明》", "templateSubList.invalidStatement.deleteTip": "删除成功", "templateSubList.invalidStatement.deleteConfirm": "删除后您将不能通过上上签系统完成合同作废流程。是否确定删除", "templateSubList.invalidStatement.deleteConfirmTip": "提示", "templateSubList.invalidStatementTabTip": "请先在合同模板中设置签约方", "templateSubList.supplement.add": "新增补充协议", "templateSubList.supplement.inputTemplateId": "请输入需要补充的模板ID", "templateSubList.supplement.inputCorrectTip": "请输入正确的模板ID编号", "templateSubList.contractConfidentiality.name": "合同保密", "templateSubList.contractConfidentiality.autoTransferDesc": "本集团/本企业的合同参与人完成合同操作任务后，自动转交合同给新账号持有，原合同参与人不再持有合同。", "templateSubList.contractConfidentiality.viewTemplate": "查看示意", "templateSubList.contractConfidentiality.holderDesc": "注：新持有人账号需与原合同持有人在同一企业才能生效。", "templateSubList.contractConfidentiality.holderTip": "（合同持有人：参与了合同，含发送、审批、签署、补全以及被抄送的本企业/集团成员账号）", "templateSubList.contractConfidentiality.participantsType": "参与方式", "templateSubList.contractConfidentiality.newHolderAccount": "新持有人账号", "templateSubList.contractConfidentiality.accountPlaceholder": "手机/邮箱，不填则不转交", "templateSubList.contractConfidentiality.sendContract": "发送合同", "templateSubList.contractConfidentiality.approvalContract": "审批合同", "templateSubList.contractConfidentiality.signContract": "签署合同", "templateSubList.contractConfidentiality.editContract": "补全合同", "templateSubList.contractConfidentiality.editContractTip": "在合同上预留字段，发出合同后再补全字段内容。大部分业务中都用不到此种参与方式，一般不需要做配置。", "templateSubList.contractConfidentiality.hideContractDesc": "自动移入保险柜：合同统一进入“合同保险柜”中，合同详情仅能由持有人查看，不持有合同的管理员也不能查看。除非合同本身需要保密，否则不建议开启此功能。因为会影响合同的正常统计和查找，也会影响API调用此类合同。", "sendContract.tempSaveTip.title": "“暂存并退出”按钮", "sendContract.tempSaveTip.content1": "合同尚未发送完毕。当您再次选择此模板发送合同时，可以继续发送此合同。", "sendContract.tempSaveTip.content2": "一个模板最多为您暂存一份，在7天内可以继续发送。", "choseBoxForReceiver.dataNeedForReceiver": "签约主体需要提交的资料", "choseBoxForReceiver.dataFromDataBox": "签约主体提交的资料需要通过某个档案柜的资料采集来获取", "choseBoxForReceiver.searchTp": "请输入档案柜名称或编号", "choseBoxForReceiver.search": "搜索", "choseBoxForReceiver.boxNotFound": "未找到档案柜", "choseBoxForReceiver.cancel": "取 消", "choseBoxForReceiver.confirm": "确 认", "contractInfo.attachmentInfo": "附属文件信息", "contractInfo.contractDescInfo": "合同描述信息", "contractInfo.batchImportLabelsTip": "以下字段不在excel表格中填写，以此页面为准", "contractInfo.contractName": "合同标题", "contractInfo.fieldConfig": "字段配置", "contractInfo.fieldConfigDesp": "设置字段的隐藏展示", "contractInfo.fieldConfigTooltip": "该字段配置强制与首份文档的字段配置同步，若要单独修改，请先将首份文档的“同步到模板的其他文档”关闭。如图示意：", "contractInfo.confirmTitle": "提示", "contractInfo.fieldConfigConfirmTip": "本模板各个文档的合同描述字段配置都会被首份文档更新，是否确定要开启字段同步？", "contractInfo.contractNameRequire": "请输入合同标题", "contractInfo.contractNameTooltip": "合同标题请不要包含特殊字符，且长度不超过100字", "contractInfo.customNumber": "公司内部编号", "contractInfo.contractType": "合同类型", "contractInfo.contractTypeIdsForApprove": "合同类型（备用）", "contractInfo.signValidateTerm": "签约有效期(天)", "contractInfo.signDeadLine": "签约截止时间", "contractInfo.signDeadLineTooltip": "如果合同在此日期前未完成签署，则无法继续签署", "contractInfo.selectDate": "选择日期时间", "contractInfo.contractExpireDate": "合同到期日", "contractInfo.contractExpireDays": "合同有效期(天)", "contractInfo.expireDateTooltip": "合同内容中的到期时间，便于您后续的合同管理", "contractInfo.notNecessary": "可不填", "contractInfo.dateTips": "已为您自动识别合同到期日，请确认", "contractInfo.contractTitleErr": "合同名称请不要包含特殊字符", "contractInfo.contractTitleLengthErr": "合同名称长度请不要超过100字", "contractInfo.contractTitleEmptyErr": "合同名称不能为空", "contractInfo.inputFieldTip": "请填写字段信息", "contractInfo.signExpireDaysLimitTip": "签约有效期仅支持设置{limit}天时间内", "contractInfo.lockMustHasValue": "需要填写内容后才能锁定", "contractInfo.lockOpenTip": "开启后，当前字段内容将被锁定，在发送合同时禁止修改字段内容。", "contractInfo.lockLimitTip": "模板管理员已禁止该字段内容被修改", "contractInfo.excelImportTip": "以下待excel表格导入字段", "contractInfo.contractContentInfo": "合同内容字段", "contractInfo.contractContentPicture": "合同装饰图片", "contractInfo.contractContentAttachment": "合同附件文件", "contractInfo.justImg": "上传只能是图片格式", "contractInfo.lessThan5": "上传图片大小不能超过5MB", "contractInfo.importSuccess": "成功导入", "contractInfo.clickToView": "点击查看", "contractInfo.hasImported": "已批量导入", "contractInfo.readyToImport": "待批量导入", "contractInfo.noticeContractName": "多文档合同标题", "contractInfo.noticeContractNameTooltip1": "本次发送的合同，在签署过程的各类通知中对应的合同名称均使用此名称，如：签署提醒、审批、抄送、合同完成等。", "contractInfo.noticeContractNameTooltip2": "在合同管理页面，使用此名称做为多文档合同的统一名称，使用文档的“合同标题”做为子合同的独立名称。", "contractInfo.msgExample": "举例（短信）：", "contractInfo.noticeContractNameExample1": "...有限公司给您的账号***发来了《", "contractInfo.noticeContractNameExample2": "》，签约截止...", "contractInfo.example": "举例", "contractInfo.view": "查看", "contractInfo.replace": "替换", "contractInfo.delete": "删除", "contractInfo.attachmentType": "请上传Pdf、Word、Excel及图片格式", "contractInfo.attachmentUploadError": "上传失败", "contractInfo.attachmentDocumentTip": "文件为{type}格式，需下载到本地查看，是否立即下载？", "configTemplate.contractListAbove30": "合同文档总数不能超过30", "configTemplate.crossPlatformContract": "跨平台合同", "configTemplate.config": "设置模板", "configTemplate.use": "使用模板", "configTemplate.save": "保存模板", "configTemplate.justSave": "保存", "configTemplate.nextStep": "下一步", "configTemplate.uploadContract": "上传文档", "configTemplate.confirmContract": "核对文档", "configTemplate.configContract": "设置文档", "configTemplate.prepareReceiver": "预置签约方", "configTemplate.configReceiver": "配置签约方", "configTemplate.pointPosition": "指定签署位置", "configTemplate.batchSend": "使用模板批量发合同", "configTemplate.batchImportInfo": "批量导入合同信息和签约方信息", "configTemplate.templateName": "合同模板名称：", "configTemplate.templateNameRequired": "请输入合同模板名称", "configTemplate.templateNameFormatTip": "模板名称不能包含方括号", "configTemplate.templateNote": "备注：", "configTemplate.selectApproval": "模板审批流", "configTemplate.selectedApprover": "模板审批人", "configTemplate.customizedApprove": "自定义审批流", "configTemplate.addContract": "添加合同", "configTemplate.contractInfoError": "合同基本信息填写有误，请检查后提交", "configTemplate.uploadContractTip": "请上传合同文件", "configTemplate.existEmptyContractTip": "存在空白文档，请补充上传合同内容", "configTemplate.accountPlaceholder": "请输入手机或邮箱", "configTemplate.accountPlaceholderJa": "请输入邮箱", "configTemplate.noSendContract": "用户无发送合同权限", "configTemplate.defaultTemplateName": "作废申明", "configTemplate.configSenderField.toContractFill": "去合同上填写", "configTemplate.configSenderField.pageName": "发件方填写字段", "configTemplate.configSenderField.backTip": "直接返回不会保存数据，是否返回？", "configTemplate.configSenderField.fieldValidateTip": "字段信息校验不通过，请确认", "downloadPwdDialog.copy": "复制", "downloadPwdDialog.copySucc": "复制成功", "downloadPwdDialog.copyFailed": "复制失败", "downloadPwdDialog.downloadTip": "下载提示", "downloadPwdDialog.downloadContentTip": "您所下载的合同文件《{fileName}.zip》已被系统加密，解压密码如下：", "downloadPwdDialog.downloadCodeTip": "注：每次下载密码随机发送！", "batchImport.optTip": "填写完成后直接上传，（已上传的文件名见模板sheet2、sheet3，复制填写模板即可）。", "batchImport.optDecTip": "上传文件压缩包。合同中需要添加的图片等附件，作为压缩包上传。", "batchImport.excelTemplate": "Excel模板", "batchImport.downloadExcelTemplate": "下载Excel模板，", "batchImport.contentFileImportSuccess": "以下文件内容和签署人已批量导入成功", "batchImport.batchImport": "批量导入", "batchImport.reBatchImport": "重新批量导入", "batchImport.batchImportTip": "批量导入提示", "batchImport.iKnow": "我知道了", "batchImport.longTimeLoadingTip": "合同数量较多，正在解析，请耐心等待!", "batchImport.zipImport": "上传文件压缩包", "batchImport.zipReimport": "重新上传文件压缩包", "batchImport.importView": "导入查看", "batchImport.documentsImport": "上传文档压缩包", "batchImport.documentsReimport": "重新上传文档压缩包", "batchImport.msg.success": "导入成功！", "batchImport.msg.fail": "导入失败", "batchImport.msg.confirm": "确定", "batchImport.msg.templateDisabled": "无法使用该模板", "batchImport.msg.authRestored": "您对该模板的使用权限被模板创建者收回", "batchImport.msg.hasDel": "此模板已被创建者删除", "batchImport.msg.listBack": "回到模板列表", "batchImport.selectedBlank": "上传文档提示", "batchImport.selectedBlankTip2Title": "2. 点击“上传文档”按钮", "batchImport.selectedBlankTip2Content": "确保上传的文件适用于所有合同的签约方（即所有签约方看到的合同内容是一致的）。", "batchImport.selectedBlankTip1Title": "1. 点击“上传文档压缩包”按钮", "batchImport.selectedBlankTip1Content": "如果不同合同的签约方需要查看不同的文件，请将这些文件打包成一个压缩文件。点击“上传文档压缩包”按钮，选择压缩包上传。", "batchImport.checkBlankDocsTitle": "勾选需要以压缩包形式上传的文档（未勾选的则需要通过“上传文档”按钮上传）。", "batchImport.selectedBlankMsg": "请勾选至少一个文档", "batchImport.confirm": "确定", "batchImport.blankDecTip": "上传文档压缩包（不超过50M）。合同中需要添加的合同文档，作为压缩包上传。", "batchImport.useZip": "请使用压缩包上传文档", "batchImport.step": "第 {step} 步：", "addressBook.searchAll": "全选", "addressBook.innerMember.title": "企业内部成员", "addressBook.innerMember.tips": "调整企业成员信息，让发件人都能更快找到内部联系人", "addressBook.innerMember.operation": "去控制台", "addressBook.outerContacts.title": "外部企业联系人", "addressBook.outerContacts.tips": "邀请您的合作伙伴提前注册实名，以便与您顺利开展业务", "addressBook.outerContacts.operation": "邀请您的合作伙伴", "addressBook.myContacts.title": "我的联系人", "addressBook.myContacts.tips": "修改联系人，确保签署人信息准确无误", "addressBook.myContacts.operation": "去用户中心", "addressBook.myContacts.toConsole": "去控制台", "addressBook.selected": "已选账号", "addressBook.search": "搜索", "addressBook.all": "全部", "addressBook.loadMore": "加载更多", "addressBook.end": "全部加载完成", "addressBook.resigned": "已离职", "addressBook.editOutContact": "修改外部联系人信息", "addressBook.noEditPermission": "没有权限，请联系主管理员", "addReceiver.prepareReceiver": "指定合同签约方", "addReceiver.viewSignOrders": "查看签署顺序", "addReceiver.sender": "发件方", "addReceiver.proxyOuterSend": "代理外部企业发送合同", "addReceiver.proxyOuterSendTip": "勾选后，可选择授权了代理发送权限的外部企业发合同", "addReceiver.noOuterSenderTip": "没有授权代理发送的外部企业，可以到档案柜提醒对方完成授权", "addReceiver.waitSenderInfoInit": "发件方数据初始化中，请稍后重试。", "addReceiver.signInOrder": "按顺序签署", "addReceiver.signInOrderTip": "勾选后，所有签署人按照设定顺序进行签署。一方完成签署后，另一方才能开始签署", "addReceiver.innerSigner": "中国签约方", "addReceiver.internalSigner": "国际签约方", "addReceiver.configForReviewerBehind": "对以下签约方设置：", "addReceiver.receiver": "接收手机/邮箱 |（最多10个，用分号隔开）", "addReceiver.orderSignLabel": "顺序签署", "addReceiver.contactAddress": "联系人地址簿", "addReceiver.signOrder": "签署顺序", "addReceiver.account": "账号", "addReceiver.accountPlaceholder": "手机/邮箱（必填）", "addReceiver.accountReceptionCollection": "前台代收", "addReceiver.accountReceptionCollectionTip1": "不知道对方具体账号或对方没有账号，", "addReceiver.accountReceptionCollectionTip2": "请选择前台代收", "addReceiver.signSubjectPerson": "签约主体：个人", "addReceiver.nameTips": "姓名（选填，用于签约身份核对）", "addReceiver.requiredNameTips": "姓名（必填，用于签约身份核对）", "addReceiver.entOperatorNameTips": "姓名（选填）", "addReceiver.needAuth": "需要实名", "addReceiver.signSubjectEnt": "签约主体：公司", "addReceiver.entNameTips": "企业名称（必填，用于签约身份核对）", "addReceiver.operator": "经办人", "addReceiver.sign": "签署", "addReceiver.done": "已完成", "addReceiver.more": "更多", "addReceiver.messageAndFaceVerify": "刷脸+验证码校验", "addReceiver.messageAndFaceVerifyTips": "该用户需完成刷脸和验证码校验后，才能签署合同。刷脸签署前用户需完成实名认证，只支持大陆居民使用", "addReceiver.faceFirst": "优先刷脸，备用验证码签署", "addReceiver.faceFirstTips": "签署时系统默认采用刷脸校验，当刷脸不通过的次数达到当日上限时自动切换为验证码校验", "addReceiver.mustFace": "必须刷脸签署", "addReceiver.handWriteNotAllowed": "使用上上签系统签名", "addReceiver.mustHandWrite": "必须手写签名", "addReceiver.fillIDNumber": "身份证号", "addReceiver.fillNoticeCall": "通知手机", "addReceiver.fillNoticeCallTips": "请填写通知手机", "addReceiver.addNotice": "添加私信", "addReceiver.attachTips": "添加合同附属资料", "addReceiver.faceSign": "必须刷脸签署", "addReceiver.faceSignTips": "该用户需要通过刷脸校验才能完成签署（刷脸签署暂只支持大陆居民使用）", "addReceiver.handWriteNotAllowedTips": "该用户只能选择上上签系统签名进行签署", "addReceiver.handWriteTips": "该用户需要手写签名才能完成签署", "addReceiver.idNumberTips": "用于签约身份核对", "addReceiver.verifyBefore": "查看文件前验证身份", "addReceiver.verify": "验证身份", "addReceiver.verifyTips": "最多20字", "addReceiver.verifyTips2": "您须将此验证信息提供给该用户", "addReceiver.sendToThirdPlatform": "发送给第三方平台", "addReceiver.platFormName": "平台名称", "addReceiver.fillThirdPlatFormName": "请输入第三方平台名称", "addReceiver.attach": "资料", "addReceiver.attachName": "资料名称", "addReceiver.exampleID": "例：身份证照片", "addReceiver.attachInfo": "备注", "addReceiver.attachInfoTips": "例：请上传本人的身份证照片（选填）", "addReceiver.addAttachRequire": "增加资料", "addReceiver.addSignEnt": "添加签约企业", "addReceiver.addSignPerson": "添加签约个人", "addReceiver.addCC": "添加抄送方", "addReceiver.addCCEnt": "抄送给企业成员", "addReceiver.addCCPerson": "抄送给个人用户", "addReceiver.addCCUser": "添加抄送人", "addReceiver.addSignUser": "添加签字人", "addReceiver.addFromAddressBook": "从地址簿添加", "addReceiver.selectContact": "选择联系人", "addReceiver.save": "保 存", "addReceiver.searchVerify": "查询校验", "addReceiver.fillImageContentTips": "请填写图片内容", "addReceiver.ok": "确定", "addReceiver.findContact": "从合同中找到以下签约方", "addReceiver.signer": "签约方", "addReceiver.signerTips": "小提示：选择签约方后，平台可以帮助定位签字及盖章位置。", "addReceiver.add": "添加", "addReceiver.notAdd": "不添加", "addReceiver.cc": "抄送", "addReceiver.notNeedAuth": "不需要实名", "addReceiver.extracting": "提取中", "addReceiver.autoFill": "自动填写签署人", "addReceiver.failExtracting": "未提取到签约方", "addReceiver.idNumberForVerifyErr": "请输入正确的身份证", "addReceiver.noAccountErr": "账号不能为空", "addReceiver.ccError": "抄送人只能有一个接收手机或邮箱", "addReceiver.editorError": "补全人只能有一个接收手机或邮箱", "addReceiver.noUserNameErr": "姓名不能为空", "addReceiver.noIDNumberErr": "身份证号码不能为空", "addReceiver.noEntNameErr": "企业名称不能为空", "addReceiver.accountFormatErr": "请输入正确的手机号或邮箱", "addReceiver.emailFormatErr": "请输入正确的邮箱", "addReceiver.enterpriseNameErr": "请输入正确的公司名称", "addReceiver.userNameFormatErr": "请输入正确的姓名", "addReceiver.riskCues": "风险提示", "addReceiver.riskCuesMsg": "如果签约方未实名签署，在文件发生纠纷时，需要您自己提供该签约方身份认定的证据。如需避免风险，请选择需要实名。", "addReceiver.confirmBtnText": "选择需要实名", "addReceiver.cancelBtnText": "选择不需要实名", "addReceiver.attachLengthErr": "您最多只能为单个签署人添加50个附件要求", "addReceiver.collapse": "收起", "addReceiver.expand": "展开", "addReceiver.delete": "删除", "addReceiver.saySomething": "说点什么吧", "addReceiver.addImage": "添加文档", "addReceiver.addImageTips": "支持word、excel、pdf以及图片，可在签署前在线预览。不超过{num}份", "addReceiver.addSourceFile": "添加源文件", "addReceiver.addSourceFileTips": "支持word、excel、pdf，不可在线预览必须下载后才能查看，但下载后仍为word或excel或pdf，方便继续编辑。不超过{num}份。", "addReceiver.addFile": "添加压缩文件", "addReceiver.addFileTips": "支持zip文件，压缩文件的哈希值将记入合同的签约存证页中。限制大小为{size}M，仅限1份。", "addReceiver.addFileTipsApproval": "支持zip文件。限制大小为{size}M，仅限1份。", "addReceiver.canDownload": "允许被下载", "addReceiver.shouEntry": "显示入口", "addReceiver.shouEntryTip": "隐藏后，发件方使用模板发送合同时，不再看到该添加文件的入口，以避免误操作。", "addReceiver.itemRequire.1": "选项", "addReceiver.itemRequire.2": "必填", "addReceiver.uploadFile": "上传文件", "addReceiver.selectFile": "选择文件", "addReceiver.confirm": "确 定", "addReceiver.emptyFile": "没有可以选择的文件，请上传文件", "addReceiver.pleaseSelectOne": "请选择一个文件", "addReceiver.keepSourceFiles": "保持源文件", "addReceiver.keepSourceTip": "若选择保持源文件，则下载的文件格式与上传的格式保持一致，但无法在线预览，需下载后才能查看。", "addReceiver.give": "给", "addReceiver.fileMax": "上传数量超过数量上限!", "addReceiver.noReceivers": "目前尚未给合同指定任何签署方。所有拟参与签署的企业或个人，包括发件方在内（即本企业或本人），都必须在当前页添加签约企业或签约个人。", "addReceiver.needStamp": "企业签字要求：设置企业签字后，签约企业{entName}还需设置盖章方式的签约角色。", "addReceiver.role": "角色：", "addReceiver.skip": "知道了", "addReceiver.toSetting": "去设置", "addReceiver.signerLimit": "您当前的版本不支持超过{limit}个相对签署/抄送方。", "addReceiverGuide.usageGuide": "使用引导", "addReceiverGuide.guideTitle": "如何添加新的签署人", "addReceiverGuide.receiverType": "您需要选择签署人参与合同的方式（六选一）：", "addReceiverGuide.asEntSign": "代表企业签署：", "addReceiverGuide.sealSub": "签署人需在合同上加盖公章或合同专用章等", "addReceiverGuide.signatureSub": "法人或高管在合同上代表企业签字。签字完成后的合同是可以被企业收走的", "addReceiverGuide.vipOnly": "高级版本可用", "addReceiverGuide.stampSub": "签署人既要盖章，也要代表企业签字", "addReceiverGuide.confirmSeal": "代表企业使用业务核对章", "addReceiverGuide.confirmSealSub": "财务对账单、询证函等文件先核实再盖章", "addReceiverGuide.asPersonSign": "代表个人签署：", "addReceiverGuide.asPersonSignTip": "仅代表个人签字，不代表任何企业", "addReceiverGuide.asPersonSignDesc": "签署人的私人合同，如借贷合同、入职离职协议等", "addReceiverGuide.scanSign": "扫码签字", "addReceiverGuide.scanSignDesc": "发合同时不需要写签署人，发出后任何人扫码/点查验页链接都可签署，适用物流单据收货场景", "addReceiverGuide.selectSignTypeTip": "请先选择签署方参与合同的方式", "addReceiverGuide.howToConfigSignType": "如何设置更多签署方式？", "addReceiverGuide.entConfigSignType": "签约方为企业时，可设置以下方式：", "addReceiverGuide.personConfigSignType": "签约方为个人时，可设置以下方式：", "addReceiverGuide.howConfig": "如何设置：", "addReceiverGuide.howConfigTip1": "1. 首先在合同签约方页面点击“+添加签约企业”或“添加签约个人”。", "addReceiverGuide.howConfigTip2": "2.点击右上角“盖章”或“签字”，弹窗中可选更多方式。", "addReceiverGuide.signRoleNote": "签署人设置签约角色的作用？", "addReceiverGuide.baseFunction": "基础能力：", "addReceiverGuide.baseUsage1": "更方便定位签署人在合同中的角色，可按角色名称填写联系方式、定位签署位置；", "addReceiverGuide.baseUsage2": "不同签约角色使用不同的印章，一份合同可以规定多种印章；", "addReceiverGuide.baseUsage3": "可以按签约角色切换合同付费方", "addReceiverGuide.advanceFunction": "高级用法：", "addReceiverGuide.advanceUsage1": "按签约角色的状态（如已读未签等）设置快捷入口，跟踪合同进度；", "addReceiverGuide.advanceUsage2": "开发者调用API需要用到签约角色字段；", "addReceiverGuide.advanceUsage3": "合同作废等高级功能需要用到签约角色字段；", "addReceiverGuide.advanceUsage4": "可以按签约角色规定印章图案；", "addReceiverGuide.advanceUsage5": "可以按签约角色设置是否允许自动签；", "addReceiverGuide.stepTip1": "点击后，在下拉框中还可以调整签约方参与合同的方式。", "addReceiverGuide.stepTip2": "请在此设置合同签约方在合同中的“签约角色”。", "addReceiverGuide.stepTip3": "如果不需要添加该签约方，点击此处可删除。", "templateReceiverConfig.err.setCurrentSender": "请指定当前发件方", "templateReceiverConfig.err.atLeastOneReceiver": "至少添加一个签约方", "templateReceiverConfig.err.atLeastOneValidReceiver": "至少添加一位可用签约方", "templateReceiverConfig.err.atLeastOneZhReceiver": "未添加中国平台的签署人", "templateReceiverConfig.err.atLeastOneJaReceiver": "未添加国际平台的签署人", "templateReceiverConfig.err.atLeastOneNecessary": "经办人姓名和经办人身份证号，至少有一项需要选择必填", "templateReceiverConfig.err.signaturesCountLarge": "经办人身份核验的数量不能少于企业签字签署位置的数量", "templateReceiverConfig.err.signaturesCountLess": "经办人身份核验的数量不能大于接收账号总数", "templateReceiverConfig.err.batchMultiUseConflict": "批量添加签约方时，不能同时使用企业多人签字", "templateReceiverConfig.err.idNumber": "请输入正确的身份证号", "templateReceiverConfig.err.idNumberNotEmpty": "身份证号不能为空", "templateReceiverConfig.err.idNumberWrong": "请输入正确的身份证号", "templateReceiverConfig.err.accountNotEmpty": "账号不能为空", "templateReceiverConfig.err.accountWrong": "请输入正确的手机号或邮箱", "templateReceiverConfig.err.userNameNotEmpty": "姓名不能为空", "templateReceiverConfig.err.userNameWrong": "请输入正确的姓名", "templateReceiverConfig.err.receiverWrong": "签约方信息填写不规范，请仔细查看页面上的错误提示（通常以红色标注），根据提示内容进行修改并重试", "templateReceiverConfig.err.atLeastOneSigner": "请至少添加一个签署人", "templateReceiverConfig.err.atLeastOtherOpertar": "跨境合同须设置跨平台的签约方", "templateReceiverConfig.err.attachNotEmpty": "请填写附件名称", "templateReceiverConfig.err.onlyOnProxyOrMultiAccounts": "同一个签署顺序中的企业，最多只有一个签约角色能使用前台代收或多账号接收合同的功能。您需要勾选“按顺序签署”，并将他们设置为不同的签署顺序。", "templateReceiverConfig.err.saveEditWrong": "账号信息错误、字段内容空缺或Excel格式错误，无法继续", "templateReceiverConfig.err.roleNameNotEmpty": "签约角色不能为空", "templateReceiverConfig.err.roleNameNotRepeat": "签约角色不能重复", "templateReceiverConfig.err.importSignerTip": "还未导入签约人", "templateReceiverConfig.err.importPicTip": "还未导入图片", "templateReceiverConfig.err.importFail": "导入失败", "templateReceiverConfig.err.errorInfoNote": "（请在下方表格中拖动滚动条，可以看到红色标注的报错说明）", "templateReceiverConfig.err.lost": "缺失：{name};", "templateReceiverConfig.err.ddlTooLittle": "签署时效不能小于15分钟", "templateReceiverConfig.err.encryptionSignPasswordLimit": "加密签署码为4-8位数字", "templateReceiverConfig.tip.moreThanTipPre": "页面仅展示Excel表中前500条，更多合同格式检查结果需", "templateReceiverConfig.tip.download": "下载文件", "templateReceiverConfig.tip.moreThanTipEnd": "至本地查看", "receiverItem.setNoticelang": "设置通知语言", "receiverItem.limitFaceConfigTip": "你的合同单价过低，该功能不可用，请联系上上签协商", "receiverItem.mutexMsg": "已设置“{msg}”，请先删除“{msg}”的设置后再选择", "receiverItem.batchImported": "已批量导入", "receiverItem.batchNotImported": "待批量导入", "receiverItem.batchCheckbox": "使用时可批量添加", "receiverItem.importedNum": "成功导入{batchNum}个签约方", "receiverItem.checkTooltip": "查看效果示意图", "receiverItem.authDropdownTooltip": "对签署方身份的校验", "receiverItem.signTypeTooltip": "以何种方式参与到合同中", "receiverItem.moreConfigTooltip": "设置更多的签署细节", "receiverItem.caseDlgTitle": "勾选使用批量导入的效果示意图", "receiverItem.clickView": "点击查看", "receiverItem.entName": "公司名称", "receiverItem.entNameTips": "提示：签约方的企业名称完全一致才能签署", "receiverItem.userName": "经办人", "receiverItem.userNamePlace": "姓名（{necessary}）", "receiverItem.userAccount": "接收手机/邮箱", "receiverItem.userAccountJa": "接收邮箱", "receiverItem.userAccountDemand": "（最多10个，用分号隔开）", "receiverItem.proxy": "对方前台代收", "receiverItem.addressBookTooltip": "联系人地址簿", "receiverItem.proxyTips": "不知道对方具体账号或对方没有账号，请选择前台代收", "receiverItem.dai": "代", "receiverItem.name": "姓名", "receiverItem.IDCard": "身份证号", "receiverItem.IDCardPlace": "用于签约身份核对（{necessary}）", "receiverItem.addressBook": "联系人地址簿", "receiverItem.inputAndNecessaryTip": "请输入{name}，{necessary}", "receiverItem.role": "签约角色", "receiverItem.rolePlace": "如员工/经销商", "receiverItem.roleTooltip": "用于标记不同的签署方，使用模板时可以根据角色名称定位签署方，填写联系方式；不同签约角色使用不同的印章或签名", "receiverItem.byAddressBook": "由您的\"联系人地址薄\"带出", "receiverItem.error.userAccountLessThan": "接收手机/邮箱不能超过{num}个", "receiverItem.error.userAccountLessThanJa": "接收邮箱不能超过{num}个", "receiverItem.error.userAccountNotRepeat": "接收手机/邮箱不能重复", "receiverItem.error.userAccountNotRepeatJa": "接收邮箱不能重复", "receiverItem.error.entNameLessThan": "企业名称不能超过{num}个字符", "receiverItem.error.signerInContract": "该签署人加入合同中", "receiverItem.error.signerNotInContract": "该签署人不加入合同中", "receiverItem.error.userInSameCompany": "请选择同一个企业的联系人", "receiverItem.invite": "邀请您的合作伙伴", "receiverItem.addFromBook": "从地址簿选择", "receiverItem.userNameToolTip.0": "经办人姓名仅用于您后期的管理和统计，不会用于校验签署经办人的姓名是否一致。", "receiverItem.userNameToolTip.1": "如需校验， 您需要启用“ 经办人身份核验” 功能。", "receiverItemHeader.contractDownloadControl": "合同下载码", "receiverItemHeader.signerPerson": "签约个人", "receiverItemHeader.signerEnt": "签约企业", "receiverItemHeader.needAuth": "需要实名", "receiverItemHeader.notNeedAuth": "不需要实名", "receiverItemHeader.needAuthEnt": "经办人需要实名", "receiverItemHeader.notNeedAuthEnt": "经办人不需要实名", "receiverItemHeader.sign": "签字", "receiverItemHeader.entSign": "企业签字", "receiverItemHeader.stamp": "盖章", "receiverItemHeader.stampSign": "盖章并签字", "receiverItemHeader.requestSeal": "业务核对章", "receiverItemHeader.cc": "抄送", "receiverItemHeader.editor": "补全", "receiverItemHeader.scanSign": "扫码签字", "receiverItemHeader.signCheck": "签署校验", "receiverItemHeader.messageAndFaceVerify": "刷脸+验证码校验", "receiverItemHeader.faceFirst": "优先刷脸，备用验证码签署", "receiverItemHeader.faceMust": "必须刷脸签署", "receiverItemHeader.noHand": "使用上上签系统签名", "receiverItemHeader.mustHand": "必须手写签名", "receiverItemHeader.notify": "签约须知", "receiverItemHeader.handwritingRec": "开启笔迹识别", "receiverItemHeader.readAll": "阅读完毕再签署", "receiverItemHeader.dataCollect": "采集材料", "receiverItemHeader.attachDoc": "添加合同附属资料", "receiverItemHeader.mainDoc": "提交签约主体资料", "receiverItemHeader.attachDocTips": "提交的资料用于帮助您追踪合同履约状态，判断业务执行是否正常。设置后，该签署人必须按要求提交", "receiverItemHeader.mainDocTips": "提交的资料用于帮助您查验签约方的主体资质，判断是否可以与其开始或继续开展业务。如果签约方已提交过的相同资料可以不再重复提交", "receiverItemHeader.scanSignTip": "发合同时不需要写签署人，发出后任何人扫码/点查验页链接都可签署，适用物流单据收货场景。", "receiverItemHeader.other": "其他", "receiverItemHeader.notifyOff": "关闭短信/邮件通知", "receiverItemHeader.notifyOffJa": "关闭邮件通知", "receiverItemHeader.notifyForeign": "使用外文通知", "receiverItemHeader.notifyForeignTips": "外文通知不支持短信，仅支持邮箱", "receiverItemHeader.signerPay": "付费方", "receiverItemHeader.signerPayDesc": "该功能暂不支持自动签，纸质签场景", "receiverItemHeader.more": "更多", "receiverItemHeader.ddl": "签署时效", "receiverItemHeader.encryptionSign": "使用加密签署", "receiverItemHeader.existSummaryTask": "合同摘要", "receiverItemHeader.twoFactorAuthentication": "开启二要素认证", "receiverItemExtends.twoFactorAuthenticationTips": "该签署方需要完成二要素认证才能签署", "receiverItemExtends.encryptionSignTips": "使用加密签署（当前签署人需要通过该密码，才可以完成合同的签署。）", "receiverItemExtends.encryptionSignCode": "加密签署码", "receiverItemExtends.existSummaryTask": "合同摘要", "receiverItemExtends.contractExtractTips": "列举关键词对应待提取的合同关键信息;为方便大模型能更准确理解关键词的内涵，建议写出“是什么”和“不是什么”", "receiverItemExtends.pleaseInput": "请输入4-8位数字", "receiverItemExtends.contractDownloadControl": "启用下载码", "receiverItemExtends.contractDownloadControlTips": "签约方在下载合同时，需填写下载码，发件方可在合同详情页中查看下载码。", "receiverItemHeader.signerAuthCheck": "经办人身份核验", "receiverItemExtends.messageAndFaceVerify": "刷脸+验证码校验", "receiverItemExtends.messageAndFaceVerifyTips": "该用户需完成刷脸和验证码校验后，才能签署合同。刷脸签署前用户需完成实名认证，只支持大陆居民使用", "receiverItemExtends.faceFirst": "优先刷脸，备用验证码签署", "receiverItemExtends.faceFirstTips": "签署时系统优先推荐刷脸校验，刷脸校验不通过则改用验证码校验", "receiverItemExtends.faceMust": "刷脸签署", "receiverItemExtends.faceMustTips": "该用户需要通过刷脸校验才能完成签署", "receiverItemExtends.faceApplicable": "适用人群", "receiverItemExtends.faceMustTooltip.1": "刷脸签署只支持：", "receiverItemExtends.faceMustTooltip.2": "1、大陆居民（身份证）", "receiverItemExtends.faceMustTooltip.3": "2、持有港澳居民通行证的港澳居民", "receiverItemExtends.faceMustTooltip.4": "3、持有外国人永久居留身份证的外国居民", "receiverItemExtends.faceMustTooltip.5": "4、定居国外的中国公民（使用国内护照）", "receiverItemExtends.faceMustTooltip.6": "其他用户暂不支持，将默认进行验证码校验签署。", "receiverItemExtends.faceMustTooltip.7": "上述1证件以外的刷脸方式只支持微信H5刷脸，", "receiverItemExtends.faceMustTooltip.8": "如您配置了必须支付宝刷脸，则2、3、4证件用户将默认验证码校验签署。", "receiverItemExtends.noHand": "使用上上签系统签名", "receiverItemExtends.noHandTips": "该用户只能选择上上签系统签名进行签署", "receiverItemExtends.mustHand": "必须手写签名", "receiverItemExtends.mustHandTips": "该用户需要手写签名才能完成签署", "receiverItemExtends.useScanCodeClaim": "启用扫码认领", "receiverItemExtends.scanCodeClaimTip": "针对前台代收合同，勾选后，签约方只能扫查验码才能认领，其他方式均不能认领合同。查验码可在合同发出后，通过查验码接口或在合同详情页下载查验码，然后自行通知到对应认领人。（收件方可在合同详情或合同内容页看到查验码，自行扫码完成认领）。", "receiverItemExtends.notifyLabel.1": "签约须知", "receiverItemExtends.notifyLabel.2": "（限255字）", "receiverItemExtends.notifyLabel.3": "发送后再补全签约须知", "receiverItemExtends.handwritingRec": "开启笔迹识别", "receiverItemExtends.handwritingRecTips": "该用户手写的姓名将与发件人指定的或实名信息中的姓名进行比对，比对一致才可完成签署（适用人群：仅支持以汉字作为姓名的签署人；不对以外文作为姓名的签署人生效）", "receiverItemExtends.readAll": "阅读完毕再签署", "receiverItemExtends.signerPay": "付费方", "receiverItemExtends.signerPayTip": "本合同由该接收方付费", "receiverItemExtends.readAllTips": "阅读完毕再签署", "receiverItemExtends.attachDoc": "添加合同附属资料", "receiverItemExtends.attachDocTip": "（每份文件资料不大于10M）", "receiverItemExtends.mainDoc": "签约主体资料", "receiverItemExtends.notifyOff": "关闭短信通知", "receiverItemExtends.notifyOffTips": "启用后，该签约方不接收签约通知（关闭状态下默认发送）", "receiverItemExtends.notifyForeign": "使用外文通知", "receiverItemExtends.notifyForeignTips": "给所有签约方发送外文通知，", "receiverItemExtends.English": "英语", "receiverItemExtends.Japanese": "日语", "receiverItemExtends.Chinese": "中文", "receiverItemExtends.Arabic": "阿拉伯语", "receiverItemExtends.dataName": "资料名称", "receiverItemExtends.dataType": "资料类型", "receiverItemExtends.remarks": "备注", "receiverItemExtends.itemRequire.1": "选项", "receiverItemExtends.itemRequire.2": "必填", "receiverItemExtends.addressLine": "收集地址的详细程度(必填)", "receiverItemExtends.require": "必填项", "receiverItemExtends.notRequire": "选填项", "receiverItemExtends.addressCheckbox.province": "省", "receiverItemExtends.addressCheckbox.city": "市", "receiverItemExtends.addressCheckbox.area": "区", "receiverItemExtends.addressCheckbox.detail": "详细地址（如街道、门牌号等）", "receiverItemExtends.storeTypeList.0": "文字资料", "receiverItemExtends.storeTypeList.1": "图片资料", "receiverItemExtends.storeTypeList.2": "单选资料", "receiverItemExtends.storeTypeList.3": "多选资料", "receiverItemExtends.storeTypeList.4": "文件资料", "receiverItemExtends.storeTypeList.5": "日期资料", "receiverItemExtends.storeTypeList.6": "地址资料", "receiverItemExtends.storeTypeList.7": "", "receiverItemExtends.storeTypeList.8": "数值资料", "receiverItemExtends.storeTypeList.9": "金额资料", "receiverItemExtends.storeTypeList.10": "表单资料", "receiverItemExtends.boxContent.dataBox": "档案柜", "receiverItemExtends.boxContent.basicDataCollection": "基础资料采集", "receiverItemExtends.boxContent.customDataCollection": "自定义资料采集", "receiverItemExtends.boxContent.personalRealName": "个人实名认证", "receiverItemExtends.boxContent.selectRequire": "(必选项)", "receiverItemExtends.boxContent.applyForAuthorization": "申请获取授权", "receiverItemExtends.boxContent.entAuthorizationList.entAuth": "企业实名认证", "receiverItemExtends.boxContent.entAuthorizationList.seal": "企业印章（用于代理签署）", "receiverItemExtends.boxContent.entAuthorizationList.send": "合同代发", "receiverItemExtends.notify.title": "启用后，该签约方不接收签约通知（关闭状态下默认发送）", "receiverItemExtends.notify.explain.1": "不给固定签约方发，可变签约方不受影响", "receiverItemExtends.notify.explain.2": "不给所有签约方发", "receiverItemExtends.attach.dataName": "资料名称", "receiverItemExtends.attach.dataType": "资料类型", "receiverItemExtends.attach.imgFile.name": "图片资料", "receiverItemExtends.attach.imgFile.support": "支持png、jpg、jpeg", "receiverItemExtends.attach.imgFile.eg": "例：身份证照片", "receiverItemExtends.attach.imgFile.holderText": "例：请上传本人的身份证照片（选填）", "receiverItemExtends.attach.docFile.name": "文件资料", "receiverItemExtends.attach.docFile.support": "支持pdf、excel、word、txt、zip、xml", "receiverItemExtends.attach.docFile.eg": "例：体检报告", "receiverItemExtends.attach.docFile.holderText": "例：请上传体检报告（选填）", "receiverItemExtends.attach.remake": "备注", "receiverItemExtends.attach.addData": "添加资料", "receiverItemExtends.attach.error.dataNotEmpty": "资料名称不能为空", "receiverItemExtends.attach.error.attachNameNotSame": "合同附属资料名称不能相同", "receiverItemExtends.attach.collapse": "收起", "receiverItemExtends.attach.expand": "展开", "receiverItemExtends.attach.delete": "删除", "receiverItemExtends.auth.new": "新增", "receiverItemExtends.auth.newLimit": "(最多5个)", "receiverItemExtends.auth.field.name": "经办人姓名", "receiverItemExtends.auth.field.id": "经办人身份证号", "receiverItemExtends.auth.placeholder.name": "姓名一致才能代表企业签署（选填）", "receiverItemExtends.auth.placeholder.nameRequire": "姓名一致才能代表企业签署（必填）", "receiverItemExtends.auth.placeholder.id": "身份证一致才能代表企业签署（选填）", "receiverItemExtends.auth.placeholder.idRequire": "身份证一致才能代表企业签署（必填）", "receiverItemExtends.auth.placeholder.nameRule": "姓名一致才能代表企业签署（需填写签署人官方证件上的姓名）", "receiverItemExtends.auth.tips.entSignature": "此经办人仅需要使用个人签名（对应个人CA证书）完成签署，无需盖章。但仍需为该企业额外指定盖章人", "receiverItemExtends.auth.tips.stampSignature": "使用企业印章签署时，需同时添加个人签名完成签署。签署前需要完成个人实名认证", "receiverItemExtends.auth.checkboxLabel.onlyStamp": "启用经办人身份核验。启用后，经办人需要实名签署", "receiverItemExtends.auth.checkboxLabel.onlyStampTip": "姓名身份证均不填，则只要求经办人实名，但不检查经办人是谁", "receiverItemExtends.auth.checkboxLabel.withSignature": "启用经办人身份核验（企业签字本身是需要经办人实名的，但勾选并填写姓名或身份证后，才会在签署时校验姓名或身份证是否完全一致）", "receiverItemExtends.auth.checkboxLabel.useMulti": "启用企业多人签字：", "receiverItemExtends.auth.checkboxLabel.sigNum.0": "给企业签字设置", "receiverItemExtends.auth.checkboxLabel.sigNum.1": "处签署位置", "receiverItemExtends.auth.checkboxLabel.showHideTip": "隐藏后，发件方使用模板发送合同时，不再看到该配置项以避免误操作。", "receiverItemExtends.auth.checkboxLabel.showThis": "显示此项", "receiverItemExtends.auth.checkboxLabel.hideThis": "隐藏此项", "receiverItemExtends.auth.error.atLeast": "经办人姓名和经办人身份证号，至少有一项需要选择必填", "receiverItemExtends.ddl": "签署时效", "receiverItemExtends.ddlDesc.0": "收到合同后，必须在", "receiverItemExtends.ddlDesc.1": "天", "receiverItemExtends.ddlDesc.2": "小时", "receiverItemExtends.ddlDesc.3": "分钟内，完成签署，否则合同状态会变为“逾期未签”。开启签署时效的签署人，系统不再自动推送即将截止签约的提醒。", "receiverItemExtends.scanSign.tip": "发合同时不需要写签署人，发出后任何人扫码/点查验页链接都可签署，适用物流单据收货场景", "receiverItemExtends.scanSign.notValidateSignVerificationCode": "只在登录时校验验证码，无需签署校验", "receiverItemExtends.authCheckMove.tip": "注意：原先此处\"经办人身份核验\"功能已移动至右侧更多菜单中，请在更多菜单中进行操作。", "sendedEdit.editorNameTip": "与发件方企业保持一致", "sendedEdit.completeDescription.0": "可在合同上预留部分内容，在合同发出后再由该账号补全，补全合同信息的执行人账号必须是发件方企业成员账号，不参与盖章或签字。适用于我方参与人数众多或业务耗时长的业务场景。", "sendedEdit.completeDescription.1": "查看设置方法", "sendedEdit.completeAttachment": "补全附件。补全合同信息的执行人必须是第一顺序，且模板已提前配置了合同附件字段。", "sendedEdit.completeOther": "补全其他。需勾选待补全的内容，如合同的签约须知、合同内容字段、签约个人的账号等。", "sendedEdit.completeMustSignOrderTip": "请先勾选“顺序签署”，且合同补全执行人的顺序不能是最后一位", "sendedEdit.sendedEditMustSignByOrder": "当前存在补全角色，补全功能要求必须启用“顺序签署”", "sendedEdit.name": "发送后补全签署人", "sendedEdit.sendedEditDesc": "发件人在发送合同时可以不填写该签约角色的账号信息，如果填写了账号信息，也需由补全合同信息的执行人账号确认后才生效。", "sendedEdit.howToPrepare": "设置方法", "sendedEdit.prepareContentTip": "签约角色A作为合同补全执行人不需要补齐任何字段或账号，不符合“发出合同再补全”功能的用法。您可以直接删除签约角色A。", "sendedEdit.configGuideTip": "可在合同上预留合同内容字段或签约个人的账号，在合同发出后再由签约角色{role}补全。", "sendedEdit.viewConfigGuide": "查看设置方法", "sendedEdit.prepareLabels": "合同预留内容字段：", "sendedEdit.prepareLabelsDesc": "编辑模板时，将内容填写人设置为发件人，并选择由哪位签约角色补全。", "sendedEdit.prepareRoles": "合同预留签约个人：", "sendedEdit.prepareSignerDesc": "1. 在设置签约方页面，添加签约个人后勾选发送后补全签署人。", "sendedEdit.prepareCompletorDesc": "2. 选择该签约方信息由哪位签约角色补全。", "sendedEdit.batchImportConflictEditorTip": "执行补全操作的签约角色不支持批量添加。将其切换为“盖章”等其他合同参与方式后才可勾选“使用时可批量添加”", "sendedEdit.batchImportConflictEmptyRoleTip": "待发送后补全的账号不支持批量添加。取消“发送后补全签署人”后，才可勾选“使用时可批量添加”", "sendedEdit.editorConfigFirstTip": "在待补全信息的签约个人签署顺序前，需存在一个补全合同的执行人", "sendedEdit.senderFill": "发送时填写", "sendedEdit.fillByRole": "由签约角色\"{role}\"填写", "sendedEdit.editorCompleteInfo": "请补全以下信息", "sendedEdit.completeBy": "由签约角色", "sendedEdit.complete": "补全", "dataBoxInvite.title": "邀请您的合作伙伴", "dataBoxInvite.step1": "分享链接给您的合作伙伴提前创建企业", "dataBoxInvite.step2": "通过链接/二维码授权后的合作伙伴会出现在地址簿", "dataBoxInvite.step3": "在“档案+”对您的合作伙伴做更多管理", "dataBoxInvite.imgName": "分享采集二维码", "dataBoxInvite.saveQrcode": "保存二维码到本地", "dataBoxInvite.copy": "复制", "dataBoxInvite.copySuccess": "复制成功", "dataBoxInvite.copyFailed": "复制失败", "selectSender.addReceiver": "请添加签约方", "selectSender.currentSender": "当前发件方：", "selectSender.toBeDetermined": "【待确定】", "selectSender.tips.0": "该模板已获得代发授权，发出的每份合同以已授权的签约方企业名义展示。", "selectSender.tips.1": "若同份合同出现多个已授权的签约方企业，则不作为代发合同。", "selectSender.multiSenderSelectTip": "如果您拥有多个业务或企业，可以在此切换不同身份来发送合同", "selectSender.sendEnt": "发件方企业", "selectSender.sendEntTip": "可选的发件方企业范围内的企业为：1）模板授权账号“发送合同”权限的企业；（2）账号已获得“集团控制台-集团管理权限”中管理的企业", "selectSender.sendContractNoPermissionTip": "使用此模板代表{entName}发送合同，你需要获得该模板的\"发送合同\"权限。建议你联系模板创建者或拥有模板分配权限的企业成员", "selectSender.viewTempaltePermission": "查看我在此模板中的权限", "selectSender.noSendPermission": "尚未获得该企业发送权限", "upload.addBlank": "添加待上传文档", "upload.addBill": "添加单据合同", "upload.addWordBill": "添加word单据合同", "upload.exampleAsFollows": "示例如下：", "upload.wordBillTip1": "支持word类型文档根据填写内容，动态合成新的单据。|发件方或签署方填写字段以是否有@在文档中区分定义", "upload.wordBillTip2": "上传的word需提前添加定位字段（字段名自定义）", "upload.billTip1": "支持excel类型文档根据接口传入内容，动态合成新的单据。|上传的excel需提前添加定位字段（字段名自定义），发送合同接口传参参数名与模板中的字段名需保持一致。", "upload.field1": "{{字段名}}", "upload.field2": "{#字段名}", "upload.wordBillfield": "明细字段，用户标记自动插入新数据（向下新增行），使用[字段名]，即中括号标记", "upload.billTip2": "1. 基本字段，数据填入一个单元格（横向填充），使用{text}，即双括号标记。| 2. 明细字段，用于标记自动插入新数据（向下新增行），使用{text}，即单括号加#标记。| 3. 示例如下：上传后，通过接口发送合同。", "upload.newBillTip1": "1. 基本字段，数据填入一个单元格（横向填充）", "upload.newBillTip2": "2. 明细字段，用于标记自动插入新数据（向下新增行）", "upload.newBillTip3": "发件方：基本字段用{{字段名}}表示；明细字段用[字段名]表示", "upload.newBillTip4": "签署方：基本字段用{{@字段名}}表示；明细字段用[@字段名]表示", "upload.addLocal": "上传本地文档", "upload.uploadDoc": "上传文档", "upload.typeTips": "文件支持jpg、png、doc、docx、pdf 等格式", "upload.dynamicTypeTips": "仅支持上传.doc 或.docx格式", "upload.gamaTypeTips": "文件支持以下格式：图片、PDF，不支持Word，Excel", "upload.ofdTypeTips": "仅支持上传ofd格式", "upload.newBlank": "添加待上传文档", "upload.modifyTItle": "修改合同标题", "upload.needUpdateTitle": "发送时使用文档名称为合同标题。即：勾选后，新上传文档的名称为合同标题；不勾选则一律使用已配置的“合同标题”。", "upload.docTitle": "合同标题", "upload.inputDocTitle": "请输入合同标题", "upload.fileLessThan": "请上传小于{num}M的文件", "upload.usePdf": "上传时请使用PDF文件或图片", "upload.useDoc": "上传时请使用DOC文件", "upload.useExcel": "上传时请使用XLSX或XLS文件", "upload.useOfd": "上传时请使用OFD文件", "upload.fileNameMoreThan": "文件名称长度超过{num}，已为您自动截取", "upload.blankTips": "设置模板时可暂不上传文档，在使用模板发送合同时再补齐文档。待上传文档仅用于‘占位’。", "upload.createContract": "新建合同", "upload.fileNameTips.0": "此处设置的“合同标题”的作用有两个：", "upload.fileNameTips.1": "1、如果有多个文档，可作为各个文档的标记，方便对文档进行定位；", "upload.fileNameTips.2": "2、可在发送合同时作为默认的合同标题；", "upload.fileNameReplace": "上传文档后，取文档名作为合同文档标题。", "localCommon.download": "下载", "localCommon.cancel": "取消", "localCommon.confirm": "确认", "localCommon.toSelect": "请选择", "localCommon.seal": "盖章", "localCommon.signature": "签名", "localCommon.signDate": "签署日期", "localCommon.text": "文本", "localCommon.date": "日期", "localCommon.qrCode": "二维码", "localCommon.number": "数字", "localCommon.dynamicTable": "动态表格", "localCommon.terms": "合同条款", "localCommon.checkBox": "复选框", "localCommon.radioBox": "单选框", "localCommon.datetime": "时刻", "localCommon.image": "图片", "localCommon.confirmSeal": "业务核对章", "localCommon.confirmRemark": "不符合章的备注", "localCommon.optional": "选填", "localCommon.require": "必填", "localCommon.tip": "提示", "localCommon.comboBox": "下拉框", "localCommon.submit": "提交", "localCommon.yes": "是", "localCommon.no": "否", "field.approval": "发送前审批", "field.send": "发送", "field.contractDispatchApply": "申请发送合同", "field.contractNeedYouSign": "该文件需要您签署", "field.ifSignRightNow": "是否马上签署", "field.signRightNow": "马上签署", "field.signLater": "稍后签署", "field.signaturePositionErr": "请为每个签署方指定签署位置", "field.sendSucceed": "发送成功", "field.confirm": "确定", "field.cancel": "取消", "field.qrCodeTips": "签署后扫码，即可查看签署详情、验证签名有效性及该合同是否被篡改", "field.pagesField": "第{currentPage}页，共{totalPages}页", "field.suitableWidth": "适合宽度", "field.signCheck": "签名查验", "field.locateSignaturePosition": "定位签署位置", "field.append": "添加", "field.privateLetter": "私信", "field.signNeedKnow": "签约须知", "field.maximum5M": "请上传小于5M的文档", "field.maximumSize": "请上传小于{size}M的压缩文件", "field.uploadServerFailure": "上传到服务器失败", "field.uploadFailure": "上传失败", "field.uploadRepeatFileTip": "请勿重复上传同名文件", "labels.signTime": "签署日期", "labels.optionLimitTip": "选择器个数已达上限", "labels.pageLimitTip": "超出页面边界，无法添加", "labels.optionName": "备选项{count}", "labels.singerName": "签署人", "labels.fdaDate": "签署时间", "labels.fdaReason": "待选择", "labels.sealArea": "盖章处", "labels.designateSeal": "去指定章", "labels.cancelSeal": "清除", "labels.changeSeal": "切换章", "labels.selectableSeal": "可选印章", "labels.noSealList": "该签署人没有可用的印章。需要管理员为其分配印章后，才能指定", "customLabelEdit.labelName": "名称", "customLabelEdit.require": "必填", "customLabelEdit.format": "格式", "customLabelEdit.equalWidth": "列表等宽", "customLabelEdit.adjustWidth": "自适应宽度", "customLabelEdit.contentFiller": "内容填写人", "customLabelEdit.senderOnly": "该内容仅发件人可以填写", "customLabelEdit.sender": "发件人", "customLabelEdit.senderTip": "选择“发件人”，则此合同内容在合同发出前由发件人填写", "customLabelEdit.signer": "签署人", "customLabelEdit.signerTip": "选择“签署人”，则此合同内容在签署人签署时填写", "customLabelEdit.signatureSize": "签字尺寸", "customLabelEdit.default": "默认", "customLabelEdit.custom": "自定义", "customLabelEdit.labelSize": "显示尺寸", "customLabelEdit.labelWidth": "宽度", "customLabelEdit.labelWidthPlaceHolder": "请输入宽度", "customLabelEdit.labelHeight": "高度", "customLabelEdit.labelHeightPlaceHolder": "请输入高度", "customLabelEdit.labelChangeAllPageTip": "已启用\"投影\"功能，移动位置对所有页码均生效。\n请检查其他页码上变化效果", "customLabelEdit.autoSystemFill": "系统自动填写", "customLabelEdit.alternativeItem": "备选项", "customLabelEdit.dateFormat": "样式", "customLabelEdit.labelFontSize": "字号", "customLabelEdit.labelFontSizePlaceHolder": "请选择字号", "customLabelEdit.labelAlign": "对齐方式", "customLabelEdit.labelDescribe": "填写说明", "customLabelEdit.labelDescribeTip": "选填，不超过20个字符", "customLabelEdit.labelRequire": "填写要求", "customLabelEdit.labelRequireTip": "必填，不填不能发送或签署", "customLabelEdit.labelFillAllRegion": "图片充满整个占位区", "customLabelEdit.labelFillAllRegionTip": "根据图片区域大小进行自动适配，充满整个图片区域", "customLabelEdit.confirm": "确定", "customLabelEdit.cancel": "取消", "customLabelEdit.defaultValue": "设置默认值", "customLabelEdit.selectDateDefaultValue": "选择日期", "customLabelEdit.messageTip.nameError": "请输入名称", "customLabelEdit.messageTip.itemError": "请输入备选项", "customLabelEdit.messageTip.itemSameError": "备选项不能相同", "customLabelEdit.messageTip.itemRegError": "备选项名字只能为中文、英文、数字的组合", "customLabelEdit.messageTip.widthError": "宽度请输入大于等于28的值", "customLabelEdit.messageTip.widthMaxError": "宽度请输入小于{width}的值", "customLabelEdit.messageTip.heightError": "高度请输入大于等于20的值", "customLabelEdit.messageTip.heightMaxError": "高度请输入小于{height}的值", "customLabelEdit.messageTip.markOptionValueTip": "备选项名字不能包含特殊字符\\/#@()", "customLabelEdit.messageTip.hasEnComma": "备选项的名称，不允许输入英文逗号", "customLabelEdit.messageTip.cannotHasEnComma": "备选项不能含英文逗号。你需要重新编辑模板，将英文逗号删除", "customLabelEdit.messageTip.wordNumError": "请填写字段内容字数", "customLabelEdit.messageTip.overCountLimit": "最多支持500个备选项", "customLabelEdit.messageTip.numberDefaultInteger": "请输入整数", "customLabelEdit.messageTip.numberDefaultError": "请输入数字，最多输入{decimalPlace}位小数", "customLabelEdit.defaultValueTip": "勾选前方选择框将其设置为默认值", "customLabelEdit.addOption": "添加选项", "customLabelEdit.batchAddOption": "批量添加选项", "customLabelEdit.selectTermType": "请选择条款类别", "customLabelEdit.wordNum": "内容字数", "customLabelEdit.wordNumTip": "用于计算字段内容的预留宽度，不限制字段内容字数，默认为5，超出界面部分会被截断", "customLabelEdit.location": "坐标位置", "customLabelEdit.xLocation": "距左边", "customLabelEdit.yLocation": "距顶部", "customLabelEdit.integer": "整数", "customLabelEdit.decimalLimit": "限制", "customLabelEdit.decimal": "位小数", "customLabelEdit.numberFormat": "格式", "customLabelEdit.formatValid": "格式校验", "customLabelEdit.noFormat": "不校验格式", "customLabelEdit.idCard": "大陆居民身份证", "customLabelEdit.phoneNumber": "11位手机号", "customLabelEdit.formatValidTip": "若填写的字段内容不符合格式要求，签署人将无法提交。", "customLabelEdit.labelDescribeTooltip": "签署合同时可查看“填写说明”中预设的文字，以确保签署人准确理解所需填写的内容。", "labelEdit.ridingSealSetTip": "对当前合同的所有骑缝章都生效", "labelEdit.ridingConfig": "每页都加盖骑缝章，适合单面打印。文档页数需大于等于2，否则签署完成后不会展示骑缝章。 | 仅单数页加盖骑缝章，适合双面打印。文档页数需大于等于3，否则签署完成后不会展示骑缝章", "labelEdit.advancedSettings": "高级设置", "labelEdit.receiver": "接收方", "labelEdit.info.0": "请注意：", "labelEdit.info.1": "1）如果模板中多个位置添加了同一个业务字段，填写人只需填写一次，并保存为同一个值；", "labelEdit.info.2": "2）属性设置会同步更新当前模板内所有同名业务字段。", "labelEdit.info.3": "注：签署人只有选择不符合章后才需要填写", "labelEdit.wordbillLabel.selectType": "选择字段类型", "labelEdit.wordbillLabel.detailAccording": "选择字段明细行数参照列", "labelEdit.wordbillLabel.maxRow": "参照行数最多的列", "labelEdit.wordbillLabel.customRow": "自定义参照列", "labelEdit.sealSyncPosition.title": "签署位置同步", "labelEdit.sealSyncPosition.description": "说明", "labelEdit.sealSyncPosition.closeTip": "是否确定关闭\"签署位置同步\"？", "labelEdit.sealSyncPosition.tipContent": "关闭后，本次发送的整个过程中，该签署位置就不能重新开启“签署位置同步”了。（只能重新开始一次新的批量发送流程才能再次启用“签署位置同步”）", "labelEdit.sealSyncPosition.noteText": "注：每个签署位置都是独立配置，互不影响的", "labelEdit.sealSyncPosition.funDescTitle": "签署位置同步功能说明", "labelEdit.sealSyncPosition.prepareSendNContract": "正准备发送{num}种不同的合同。", "labelEdit.sealSyncPosition.funDescTip.open": "开启“签署位置同步”后，当前合同调整的签署位置/字段的坐标对其他{num}种合同同步生效。", "labelEdit.sealSyncPosition.funDescTip.close": "关闭“签署位置同步”后，对当前合同生效，其他{num}种合同仍保持原样。", "labelEdit.sealSyncPosition.funDescTip.note": "注：在当前合同新增或删除签署位置，对其他{num}种合同都是自动生效的。", "labelEdit.sealSyncPosition.funDescTip.keyPositionTip": "使用了关键字定位后，关键字对应的签署位置默认关闭“签署位置同步”", "labelEdit.sealSyncPosition.switchContractTip": "可切换至不同合同查看调整后的效果。切换合同的方法如下：", "labelEdit.sealSyncPosition.switchContactFun": "点击页面右上角数据条目数，来切换当前要展示的合同。", "labelEdit.sealSyncPosition.reopenDisabledTip": "一旦关闭“签署位置同步”，本次发送过程中就不能重新打开了。", "labelEdit.keywordPosition": "字段坐标位置", "labelEdit.keywordMatch": "按关键字匹配(支持手动调整位置)", "labelEdit.keyword": "合同中的关键字", "labelEdit.keywordPlaceHolder": "如“盖章处”", "labelEdit.keywordNum": "第几处关键字", "labelEdit.keywordNumPlaceHolder": "50以内数字", "labelEdit.nameError": "请填写名称！", "labelEdit.keywordMove": "移动偏移量（相对纸张大小）", "labelEdit.keywordMoveX": "横向移动", "labelEdit.keywordMoveY": "纵向移动", "labelEdit.excelHeaderPosition.title": "Excel表头定位", "labelEdit.excelHeaderPosition.keyword": "表头关键字", "labelEdit.excelHeaderPosition.keywordPlaceHolder": "如实收数量", "labelEdit.excelHeaderPosition.keywordNum": "第几处关键字", "labelEdit.excelHeaderPosition.referenceCol": "参照列列名", "labelEdit.excelHeaderPosition.referenceColPlaceHolder": "如货物名称", "labelEdit.excelHeaderPosition.headerKeyword": "Excel表头关键字", "labelEdit.excelHeaderPosition.result": "效果如下：", "labelEdit.excelHeaderPosition.headerKeywordTipsList.0": "1.传入excel表单（如收货单）时，系统会自动在表头关键字下每个单元格中设置字段。", "labelEdit.excelHeaderPosition.headerKeywordTipsList.1": "2.字段：名称默认自增，如设定字段名为实收，则后续自增字段名分别为实收_1，实收_2，实收_3，...", "labelEdit.excelHeaderPosition.headerKeywordTipsList.2": "3.第几处关键字：若文档中含有多个相同关键字，则定位至第N个关键字（其他位置关键字不设置字段）。", "labelEdit.excelHeaderPosition.setReferenceCol": " 设置参照列", "labelEdit.excelHeaderPosition.setReferenceColTips.0": "1. 必填，否则该功能不生效。", "labelEdit.excelHeaderPosition.setReferenceColTips.1": "2. 填入关键字，则字段与该列数据对齐，遇到空行则自动终止。", "labelEdit.excelHeaderPosition.setReferenceColTips.2": "3. 参照列最多支持100行，超出不做处理。", "labelEdit.sealSet": "印章设置", "labelEdit.slant": "向左倾斜", "pointPositionDoc.pageTip": "第{pageNum}页共{pageSize}页", "pointPositionDoc.nextDoc": "进入下一份文档", "pointPositionDoc.checkboxName": "备选项{count}", "pointPositionDoc.confirmSeal": "符合章", "pointPositionDoc.notConfirmSeal": "不符合章", "pointPositionDoc.deleteTip": "删除成功", "pointPositionDoc.viewHighDpiImg": "查看高清图片|查看原图", "pointPositionDoc.boxSelect.name": "框选", "pointPositionDoc.boxSelect.selectLabelNum": "选中{x}个字段", "pointPositionDoc.boxSelect.alignType": "字段对齐方式", "pointPositionDoc.boxSelect.revoke": "撤销", "pointPositionDoc.boxSelect.leftAlign": "左对齐", "pointPositionDoc.boxSelect.bottomAlign": "底对齐", "pointPositionDoc.boxSelect.tipAccess": "字段对齐指导", "pointPositionDoc.boxSelect.selectGuideTip": "按住鼠标框选字段，可以在右侧边栏选择对齐操作。", "pointPositionMiniDoc.superTotalContract": "共{num}条数据，仅展示500条，请选择", "pointPositionMiniDoc.totalContract": "共{num}条数据，请选择", "pointPositionMiniDoc.totalContractAfter": "条展示", "pointPositionMiniDoc.contractSwitchTip": "一条记录对应着批量导入的Excel表格里的一行数据", "pointPositionMiniDoc.document": "文档", "pointPositionMiniDoc.documentsLength": "共{documentsLength}份", "pointPositionMiniDoc.pager": "页码", "pointPositionMiniDoc.page": "页", "pointPositionMiniDoc.totalPages": "页数", "pointPositionMiniDoc.goToPage": "跳转至：第", "pointPositionMiniDoc.skipRiskTip": "当前发送的合同中包含的企业签约方已超过500家，不再进行年审风险提示。您可在档案柜中查看相对方的年审情况，确认无误后再进行发送。", "pointPositionMiniDoc.findNextSignPosition": "查找签署位置", "pointPositionSite.step1": "第一步：", "pointPositionSite.selectSigner": "选择签约方", "pointPositionSite.noneReceivers": "暂未指定签约方", "pointPositionSite.step2": "第二步：", "pointPositionSite.step3": "第三步：", "pointPositionSite.moreConfig": "更多配置", "pointPositionSite.multipleSigners": "此签约方已开启多人签字功能", "pointPositionSite.dragSignaturePosition": "拖动签署位置", "pointPositionSite.insertSignaturePosition": "插入签署位置", "pointPositionSite.signField": "签署字段", "pointPositionSite.tempField": "临时字段", "pointPositionSite.businessField": "业务字段", "pointPositionSite.addBusinessField": "添加业务字段", "pointPositionSite.manageBusinessField": "管理业务字段", "pointPositionSite.tempFieldFillTip": "设置字段由签署人填写", "pointPositionSite.searchBusinessField": "请输入", "pointPositionSite.edit": "编辑", "pointPositionSite.decorateField": "合同装饰", "pointPositionSite.optional": "（选填）", "pointPositionSite.wordBillLabelConfig": "以下字段需要配置：", "pointPositionSite.whatTempField": "什么是临时字段？", "pointPositionSite.whatTempTip.0": "临时字段可用于设置模板变量， 设置后只在该模板生效， 无法在其他模板重复使用。", "pointPositionSite.whatTempTip.1": "通过以下配置可以使临时字段被搜索：", "pointPositionSite.know": "知道了", "pointPositionSite.whatBusinessField": "什么是业务字段（合同内容字段）？", "pointPositionSite.whatBusinessTip.0": "合同内容字段可用于设置模板变量，设置后企业成员均可在设置模板时重复使用。", "pointPositionSite.whatBusinessTip.1": "发起模板后在合同内容字段内填入的合同内容可搭配合同管理的“列表配置”功能支持查看和搜索。", "pointPositionSite.groupSignMOpenTip": "获得集团控制台-集团管理权限-业务管理权限后，点击此按钮将进入字段配置管理页。", "pointPositionSite.groupSignMJumpTip": "获得权限后，点击此按钮可以直接添加合同内容字段，设置后所有企业成员在配置模板时可以重复使用。", "pointPositionSite.groupSignMJumpDesc": "获取权限有两种途径，满足任意一种条件即可", "pointPositionSite.groupSignMJumpOpt1": "1、获得集团管理权限中的“业务管理”权限", "pointPositionSite.groupSignMJumpOpt2": "2、获得企业角色中的“业务管理”权限", "pointPositionSite.CommonSignMOpenTip": "获得企业控制台业务管理权限后，可以直接添加合同内容字段，设置后所有企业成员在配置模板时可以重复使用。", "pointPositionSite.CommonSignMJumTip": "获得企业控制台业务管理权限后，点击此按钮将进入字段配置管理页", "pointPositionSite.seal": "盖章", "pointPositionSite.entSignature": "盖章人签字", "pointPositionSite.operatorSignature": "经办人签字", "pointPositionSite.scanSignature": "扫码签字", "pointPositionSite.signature": "签名", "pointPositionSite.confirmSeal": "业务核对章", "pointPositionSite.confirmRemark": "不符合章的备注", "pointPositionSite.signDate": "签署日期", "pointPositionSite.text": "文本", "pointPositionSite.singleBox": "单选框", "pointPositionSite.multipleBox": "复选框", "pointPositionSite.comboBox": "下拉框", "pointPositionSite.watermark": "水印", "pointPositionSite.decorataRidingSeal": "骑缝章", "pointPositionSite.picture": "图片", "pointPositionSite.innerSignComment": "批注", "pointPositionSite.watermarkTip": "发送合同后自动替换为真实信息", "pointPositionSite.singlePageRidingSealTip": "单页文档无法添加骑缝章", "pointPositionSite.goDecorateField": "进入合同装饰", "pointPositionSite.decorateFieldTip": "提示：水印和骑缝章去合同装饰", "pointPositionSite.table": "动态表格", "pointPositionSite.term": "合同条款", "pointPositionSite.addContentFieldSteps.0": "在企业控制台一业务字段管理-合同内容字段页面，配置一个名称是“xxx”的字段。", "pointPositionSite.addContentFieldSteps.1": "等待约15分钟后，在合同管理-列表配置-隐藏字段中会出现“xxx”字段供您配置。", "pointPositionSite.number": "数字", "pointPositionSite.bizDate": "日期", "pointPositionSite.ridingSealTip": "默认对所有文档设置，发送合同时，若签约方未参与文档盖章，则对应文档骑缝章不会生效。", "pointPositionSite.ridingSealSendTip": "若签约方未参与文档盖章，则对应文档骑缝章不会生效。", "pointPositionSite.ridingSealDocConfig": "是否对以下文档设置骑缝章", "pointPosition.saveTemplateTip": "重要提醒", "pointPosition.confirm": "确定", "pointPosition.save": "继续保存", "pointPosition.send": "继续发送", "pointPosition.hasSameLabelTip": "已存在不同类型的同名字段", "pointPosition.needChangeNameTip": "已存在同名字段，请修改名称", "pointPosition.synLabel": "已将{num}个同名字段属性更新为当前字段属性值", "pointPosition.saveSuc": "保存成功", "pointPosition.isToPermissions": "为规范使用者的操作行为，建议为模板使用者设置权限（发送合同前能否修改合同的权限）", "pointPosition.remind": "提示", "pointPosition.goSet": "去设置", "pointPosition.notGoSet": "暂不设置", "pointPosition.nowSignText": "该文件需要您签署，是否马上签署?", "pointPosition.nowSignTip": "提示", "pointPosition.nowSign": "马上签署", "pointPosition.laterSign": "稍后签署", "pointPosition.contractDispatchApply": "设置审批流", "pointPosition.riskTips": "年审风险提示", "pointPosition.riskTipsCancel": "取消发送", "pointPosition.riskTipsConfirm": "全部发送", "pointPosition.realNameAnnualVerify": "实名年审", "pointPosition.realNameAnnualVerifyRecords": "年审记录", "pointPosition.customInfoAnnualVerify": "自定义资料年审", "pointPosition.viewAnnualVerifyINfo": "查看资料详情", "pointPosition.entName": "企业名称", "pointPosition.operation": "操作", "pointPosition.annualVerifyTime": "年审时间", "pointPosition.annualVerifyStatus": "年审状态", "pointPosition.annualVerifyCondition": "年审状况", "pointPosition.noDataTips": "无", "pointPosition.noRealNameAnnualVerify": "未年审", "pointPosition.realNameAnnualVerifying": "年审中", "pointPosition.realNameAnnualVerified": "已年审", "pointPosition.noCustomInfoAnnualVerify": "未年审", "pointPosition.customInfoAnnualVerifyingNo": "年审中（未提交）", "pointPosition.customInfoAnnualVerifyingYes": "年审中（已提交）", "pointPosition.customInfoAnnualVerified": "已年审", "pointPosition.setApprovalTitle": "设置审批流", "pointPosition.setApprovalTitleSelect": "发送合同时，您需要选择一个审批流，但管理员尚未为您分配任何审批流，您无法发送合同。", "pointPosition.setApprovalTitleSuggest": "建议您：", "pointPosition.setApprovalTitleSuggestContent": "联系管理员在企业控制台审批管理页面为您添加可用的审批流程，或关闭“审批流程严格管控”这个配置项。", "pointPosition.setApprovalTitleSuggestSender": "在企业控制台-审批管理-审批流管理，点击审批流中的“合同发件人 修改”即可添加编辑审批流触发人员。", "pointPosition.setApprovalTitleSuggestStrict": "在企业控制台-审批管理-标题栏右侧，点击“审批流严格管控”。在弹窗中即可开启关闭该项。", "pointPosition.setApprovalTitleSuggestCompany": "此次发送涉及到的所有子企业的审批流配置情况都需要检查。", "pointPosition.deleteRidingSeal": "删除文档骑缝章", "pointPosition.deleteEntRidingSealDes": "删除签约角色\"{showName}\"的骑缝章盖章处：", "pointPosition.deleteCurrentRidingSeal": "仅删除当前一个文档的骑缝章盖章处", "pointPosition.deleteAllRidingSeal": "删除所有文档中骑缝章盖章处", "labelLackTip.document": "文档", "labelLackTip.signer": "签署人", "labelLackTip.lackLabelTips": "在下列合同文件中，签署人的签署位置尚未被指定。请查看文件确定是否需要添加签署位置。", "labelLackTip.allLackLabelTips": "在所有合同文件中都未指定签署位置，合同无法发送。请调整签署位置或删除该签署人。", "labelLackTip.goEdit": "去修改", "labelLackTip.operationTip": "了解如何实现只看不签？", "labelLackTip.caseTip.1": "方案一：给 xxx签约角色添加附有合同文件的签约须知。", "labelLackTip.caseTip.2": "方案二：将xxx签约角色的账号再添加一个抄送方式的签约方，即可查看所有合同文件。建议您关闭该抄送人的消息通知。", "labelLackTip.caseTip.3": "方案三：xxx签约角色的账号作为发件人、审批人，或获得查看企业合同权限时，即有权限查看合同文件。", "labelLackTip.ridingSealInvalid": "以下合同文档中将不展示骑缝章：", "labelLackTip.onePageLimint": "合同只有一页，无需加盖骑缝章", "labelLackTip.noSealLimit": "{name}不参与该合同签署", "signCharge.deductPublicNotice": "对私合同可用份数不足时会扣除对公合同", "signCharge.isReceivePayer": "该合同由您指定的签署方付费", "signCharge.isCCReceiverPayer": "该合同由您指定的抄送方付费", "signCharge.CCReceiverPayerFailTip": "抄送方余额不足或者不支持您指定的抄送方付费", "signCharge.charge": "计费：", "signCharge.units": "{num}份", "signCharge.contractToPrivate": "对私合同", "signCharge.contractToPublic": "对公合同", "signCharge.costTips.1": "对公合同：签署人（不包含发件人）中有企业账户的合同", "signCharge.costTips.2": "对私合同：签署人（不包含发件人）中没有企业账户的合同", "signCharge.costTips.3": "计费份数根据文件份数计算", "signCharge.costTips.4": "计费份数 = 文件份数 × 批量导入用户组（行）数", "signCharge.confirm": "确定", "signCharge.cancel": "取消", "signCharge.toCharge": "去充值", "signCharge.contractNeedCharge.1": "可用合同份数不足，无法发送", "signCharge.contractNeedCharge.2": "可用合同份数不足，请联系主管理员充值", "signCharge.contractNeedCharge.3": "专用套餐余额不足，请点击下方充值按钮完成充值", "signCharge.accountCharge.notice": "该合同按参与账号数计费", "signCharge.accountCharge.able": "可以正常发送", "signCharge.accountCharge.unable": "可用账号数不足，请联系上上签客服", "signCharge.signRole": "合同发送成功后，以下签约角色将立即完成签署：", "signCharge.signRoleNone": "无", "signCharge.signProxy": "当前模板已被用于“业务协作”，不能使用发送并签署功能。", "signCharge.cannotSign": "您无法使用\"发送并签署功能\"。", "signCharge.signRoleTerm": "满足以下条件的签约角色可在发送合同时自动完成签署：", "signCharge.signRoleTermBtn": "收起", "signCharge.signRoleTermOpenBtn": "展开", "signCharge.sendAndSignTerms.0": "（1） 已为签约角色配置了“模板专用章”；", "signCharge.sendAndSignTerms.1": "（2） 签约角色的经办人账号为当前账号，且已获得“专用章”；", "signCharge.sendAndSignTerms.2": "（3） 该签约角色为合同第一顺序签署人（或合同未启用“顺序签署”），仅需要通过验证码校验即可完成签署", "signCharge.sendAndSignTerms.3": "（4） 该签约角色未启用签署前审批，或合同未启用发送前审批。", "signCharge.sendOnly": "发送合同", "signCharge.sendAndSign": "发送合同并签署合同", "signCharge.verCodeInputErr": "请先获取验证码", "signCharge.lackVerCode": "请先输入验证码", "signCharge.timingSend": "定时发送合同", "signCharge.sender": "发件方企业", "signCharge.payer": "付费方", "signCharge.payerChargeUsageDesc": "相对方到付功能介绍", "signCharge.switchPayer": "1.相对方到付：", "signCharge.feePayer": "2.费用支付：", "signCharge.keyAdv": "核心优势：", "signCharge.decreaseCost": "1.降低成本：", "signCharge.optMulti": "2.操作灵活：", "signCharge.connectToOpen": "联系上上签开启付费方功能", "signCharge.connectAdminToOpen": "您没有操作权限，请联系管理员{adminAccount}", "signCharge.decreaseCostDesc": "签约方支付费用，有效降低您的成本负担。", "signCharge.optMultiDesc": "根据商业惯例或双方协商，自由选择付费方。", "signCharge.switchPayerTip": "注意：为避免合同流程阻塞，请提前与付费的签约方协商一致。", "signCharge.payerChargeUsageTip1": "由被选定的签署方角色支付合同费用。贵司以及其他合同参与方不会被收取合同费用。", "signCharge.payerChargeUsageTip2": "根据付费方合同余额情况，签署合同时自动扣费或提示充值。", "signCharge.noSignInAllTip": "签约方：{roleName} 未参与所有文档签署，不能被设置为合同付费方，是否取消设置付费方", "sendPrepare.selectTemplate": "选择模板", "sendPrepare.selectTemplateFileTip": "请先选择模板文件", "sendPrepare.batchImportExcel": "使用excel批量发送", "sendPrepare.batchReplaceExcel": "批量发送不同合同", "sendPrepare.sendContract": "单独发合同", "sendPrepare.allTemplate": "全部模板", "sendPrepare.allMode": "显示全部", "sendPrepare.federationMode": "仅显示文档组合", "sendPrepare.documentMode": "仅显示文档", "sendPrepare.selectModeTitle": "模板视图", "sendPrepare.selectDocument": "请先勾选文档", "sendPrepare.viewDetail": "查看详情", "sendPrepare.sendGuideStep1": "如何选择模板？", "sendPrepare.sendGuideStep2": "如何批量发送合同?", "sendPrepare.sendGuideStep3": "不想用模板发合同，应如何处理?", "sendPrepare.sendGuideDialogText1": "找到场景对应的模板，从中勾选一份文档或多份文档，将这些文档一次性发给签约方。 | 经常被一起发送的多份文档，可以配置为“模板文档组合”，以方便快捷选中。", "sendPrepare.sendGuideDialogText2": "单独发送/发起合同：一次只发出一份文档，或一次发出一份文档组合。 | 使用Excel批量发送合同：实现一次给N个人发送N份合同，这些合同都是模板合同。 | 批量发送不同合同：实现一次给N个人发送N份合同，这些合同以压缩包的方式从本地上传，每份合同的内容各不相同。", "sendPrepare.sendGuideDialogText3": "选择带“+”的文档（或者勾选“待上传文档”的文档），点击“上传文档”按钮添加本地文件。（如果没有这样的文档，请到模板管理页中创建）", "sendPrepare.emptyContract": "空白合同", "sendPrepare.ofdNotSupport": "OFD模板不支持批量发送", "sendPrepare.noTemplate": "您还没有任何模板", "sendPrepare.sendQuickly": "无需创建模板，快速发起合同", "sendPrepare.allocateTemplate": "请联系企业管理人员为您分配模板", "sendPrepare.allocateTemplatePermission": "或：请管理员在控制台-角色管理中，为你分配“创建模板”权限。你就可以直接发送合同或创建模板后发送合同了", "sendPrepare.batchSend": "批量发送", "sendPrepare.normalSend": "单独发送", "contractItem.emptyContract": "文档待上传", "contractItem.uploadDoc": "上传合同", "contractItem.replaceDoc": "替换文档", "contractItem.addAttachment": "添加合同附件", "contractItem.uploading": "正在上传中...", "contractItem.pageCount": "{num}页", "contractItem.downloadTemplate": "下载Excel模板", "contractItem.uploadExcelTable": "上传Excel表格", "contractItem.more": "更多...", "contractItem.configAttachment": "配置附件字段", "contractItem.federationTip": "（使用模板发送合同组合，假设文档有a,b,c，场景1经常需要发送a,b，场景2经常需要发送a,b,c，那么可以提前将文档进行组合，发送时直接选择对应组合即可。）", "contractItem.federations": "模板文档组合", "contractItem.fillDocument": "补齐文档", "contractItem.uploadDocument": "上传文档", "contractItem.uploadDocumentContinue": "继续上传文档", "contractItem.temporarySaveTip": "当前合同尚未发送出去。 | 当你再次选择当前这个模板发送合同时，可以在7天内继续发送这份“暂存”过的合同。 | 点击模板的使用，即可找到暂存未发送的合同。", "contractItem.notRemind": "下次不再提醒", "contractItem.interanlSignTip": "当前模板已设置为“仅用于对内文件签字场景”，不应被用于正式合同（如劳动合同等）。", "contractItem.attachedToBoxTip": "此模板已绑定档案柜的代发代签，不可创建临时文档和附件字段。", "contractItem.replaceTip1": "您即将用于替换的新文档仅包含{page}页，而在替换前的原文档的第{pageStr}页中可能存在以下字段信息，如盖章、签字、签署日期和合同内容字段。\n需要您删除这些字段后再重新替换，步骤如下：", "contractItem.replaceTip2": "1、连续点击页面右上角的“下一步”按钮，进入“指定签署位置”页", "contractItem.replaceTip3": "2、仔细检查第{pageStr}页，手动删除上述字段信息。", "contractItem.replaceTip4": "3、返回至当前页面“上传文档”页，重新做替换操作", "contractItem.replaceTip5": "4、替换成功后，再次进入“指定签署位置”页，在新文档上手动添加上述被删除的字段，以免影响您的业务。", "descriptionFields.newField": "新增字段", "descriptionFields.title": "字段配置", "descriptionFields.syncDoc": "同步到模板的其他文档", "descriptionFields.placeholder": "请输入字段", "descriptionFields.cancel": "取消", "descriptionFields.confirm": "确定", "descriptionFields.saveSuc": "保存成功", "getSeal.selectSeal": "选择印章", "getSeal.chooseApplyPerson": "选择申请人", "getSeal.getSealBtn": "获取印章", "getSeal.nowApplySealList": "您正在请求以下印章", "getSeal.chooseApplyPersonToDeal": "请选择执行盖章操作的人员，您的申请以及合同将会转交给所选人来处理（你仍能继续查看、跟进此合同）", "getSeal.chooseApplyPersonToMandate": "请选择印章人，所选人收到通知、审核通过后，您将获得该印章的使用权限，届时可以使用该印章来盖章并签署合同", "getSeal.cancel": "取消", "getSeal.confirm": "确定", "getSeal.sealApplySentPleaseWait": "印章分配申请已发送，请等待审核通过。或者您可以选择其他盖章方式", "getSeal.entNoSeal": "您的企业尚未上传印章", "getSeal.contactGroupAdminToDistributeSeal": "请联系集团管理员分配印章", "getSeal.getSealTip": "你需要先获得企业印章才能查看此 | 你需要先获得以下企业印章才能查看此", "authIntercept.title": "要求您以：", "authIntercept.name": "姓名为：", "authIntercept.id": "身份证号为：", "authIntercept.descNoAuth1": "请确认以上身份信息为您本人，并以此进行实名认证。", "authIntercept.descNoAuth2": "实名认证通过后，可查看并签署合同。", "authIntercept.descNoSame1": " 的身份签署合同", "authIntercept.descNoSame2": "这与您当前登录的账号已完成的实名信息不符。", "authIntercept.tips": "注：身份信息完全一致才能签署合同", "authIntercept.goOn": "是我本人，开始认证", "authIntercept.goMore": "去补充认证", "authIntercept.authTip": "进行实名认证。", "authIntercept.viewAndSign": "完成认证后即可查看和签署合同", "authIntercept.tips2": "注：企业名称完全一致才能查看和签署合同。", "authIntercept.requestOtherAnth": "请求他人认证", "authIntercept.goAuth": "去实名认证", "authIntercept.requestSomeoneList": "请求以下人员完成实名认证：", "authIntercept.ent": "企业", "authIntercept.entName": "企业名称", "authIntercept.account": "账号", "authIntercept.accountPH": "手机或邮箱", "authIntercept.send": "发送", "authIntercept.lackEntName": "请填写企业名称", "authIntercept.errAccount": "请填写正确的邮箱或手机号", "authIntercept.successfulSent": "发送成功", "authIntercept.me": "我", "authIntercept.myself": "本人，", "authIntercept.reAuthBtnTip": "我是当前手机号的实际使用者，", "authIntercept.reAuthBtnContent": "重新实名后，该账号的原实名会被驳回，请确认。", "authIntercept.cancel": "取消", "authIntercept.confirmOk": "确认", "authIntercept.goHome": "返回合同列表页>>", "authIntercept.authInfo": "检测到您当前账号的实名身份为 ", "authIntercept.in": "于", "authIntercept.finishAuth": "完成实名，用于合规签署合同", "authIntercept.ask": "是否继续以当前账号签署？", "authIntercept.reAuthBtnText": "是的，我要用本账号重新实名签署", "authIntercept.changePhoneText": "不是，联系发件方更改签署手机号", "authIntercept.changePhoneTip1": "应发件方要求，请联系", "authIntercept.changePhoneTip2": "，更换签署信息(手机号/姓名)，并指定由您签署。", "applyJoinEnt.beenAuthenticated": "已被实名", "applyJoinEnt.assignedIdentity": "发件方填写的签约主体为：", "applyJoinEnt.entBeenAuthenticated": "该企业已被实名，主管理员信息如下：", "applyJoinEnt.entAdminName": "管理员姓名：", "applyJoinEnt.entAdminAccount": "管理员账号：", "applyJoinEnt.applyToBeAdmin": "我要申诉成为主管理员", "applyJoinEnt.contactToJoin": "联系管理员加入企业", "applyJoinEnt.applicant": "申请人", "applyJoinEnt.inputYourName": "请输入您的姓名", "applyJoinEnt.account": "账号", "applyJoinEnt.send": "发送", "applyJoinEnt.contract": "合同", "applyJoinEnt.sendWishToJoin": "您可以通过账号申诉成为管理员，也可以向管理员发送加入企业的申请", "applyJoinEnt.applyToJoin": "您还未加入该企业，无法查看或签署该{alias}，是否要申请加入？", "applyJoinEnt.sentSuccessful": "发送成功", "receiverItemDisclaimer.title": "功能使用提示", "receiverItemDisclaimer.desc.0": "根据《电子签名法》相关规定，合法有效的电子合同，必须要保证签约主体能够随时调取查用，禁止签约方下载、查看的做法将违反了《电子签名法》的要求。", "receiverItemDisclaimer.desc.1": "请确保签约人可以查看、下载已经签署的协议。", "receiverItemDisclaimer.desc.2": "我已阅读并同意以上内容", "receiverItemDisclaimer.confirm": "继续", "templateListDynamicEntryConfirm.title": "提示", "templateListDynamicEntryConfirm.content.0": "检测到您已升级新合同，是否继续升级新动态模板，升级后动态模板可使用新合同功能：", "templateListDynamicEntryConfirm.content.1": "包括模板附件添加，模板文档组合，发送审批合同等功能，升级请联系客服或客户成功经理。", "templateListDynamicEntryConfirm.content.2": "注意：升级后历史动态模板将被转移至新合同模板中； 若之前使用的是html动态模板，则升级后模板不会同步，请提前保存模板数据至本地。", "templateListDynamicEntryConfirm.cancel": "不再提示", "templateListDynamicEntryConfirm.confirm": "确定", "templateListOpenDynamicConfirm.title": "是否开通动态模板？", "templateListOpenDynamicConfirm.content.0": "开启动态模板后， 你可以：", "templateListOpenDynamicConfirm.content.1": "在上传的word文档内直接设置业务字段， 不需要在web页面上拖拽了；", "templateListOpenDynamicConfirm.content.2": "在线编辑模板文稿； ", "templateListOpenDynamicConfirm.content.3": "插入任意行列数的动态表格。", "templateListOpenDynamicConfirm.tip": "马上联系上上签销售人员开通。", "templateListOpenDynamicConfirm.confirm": "我知道了", "templateDynamicPosition.notice": "提醒", "templateDynamicPosition.initLoadingTip": "模板字段及编辑器初始化中，请耐心等待......", "templateDynamicPosition.saveLoadingTip": "数据保存中，请勿刷新或关闭页面，以免数据丢失", "templateDynamicPosition.modifyNotice": "模板字段名称的修改必须通过页面右侧弹窗中完成，在文本中直接修改是不生效的，会被弹窗中的名称覆盖。", "templateDynamicPosition.modifyName": "经系统检测，部分字段名称的修改操作不符合规范，请核对后重新保存。", "templateDynamicPosition.modifyClose": "我知道了", "templateDynamicPosition.initDocError": "文档初始化失败", "templateDynamicPosition.helpPoper": "动态模板已全面升级，如需了解更多使用小技巧，请点击使用帮助查看。", "templateDynamicPosition.help": "使用帮助", "templateDynamicPosition.preview": "效果预览", "templateDynamicPosition.someTextNoName": "请为每个文本标签填写名称", "templateDynamicPosition.allSignDone": "请为每个签署方指定签署位置", "templateDynamicPosition.sameName": "已存在同名字段，请修改名称", "templateDynamicPosition.saveSuccess": "保存成功", "templateDynamicPosition.optionsNotEnough.title": "提示", "templateDynamicPosition.optionsNotEnough.content": "单复选框{alias}备选项不足两项，请补充", "templateDynamicPosition.optionsNotEnough.confirmBtn": "知道了", "templateDynamicPosition.labelValid.confirmBtnWithTime": "知道了（{num}）", "templateDynamicPosition.labelValid.noCloseTip": "5秒倒计时结束前，切不可关闭页面", "templateDynamicPosition.labelValid.waiting": "系统正在检查模板字段是否正常，5秒倒计时结束后再重新点击保存按钮。请耐心等待！", "templateDynamicPosition.noTerm": "当前暂无可用条款，请前往企业控制台添加", "templateDynamicPosition.helpSlider.title": "使用帮助", "templateDynamicPosition.helpSlider.firstStep": "第一步：选择签约方", "templateDynamicPosition.helpSlider.secondStep": "第二步：插入签署位置", "templateDynamicPosition.helpSlider.secondStepDetail": "选择需要插入的字段，左键单击后在文档中单击插入即可，如要修改文本内容，则需要在右侧的弹窗中修改，否则视为无效（注：业务字段的高亮底色是不会出现在最终发送的合同中的）", "templateDynamicPosition.bookmarkSensorTip": "以下字段在模板内未保存生效，如需使用请重新添加，若不再使用请点击删除。", "templateDynamicPosition.deleteInvalidBookmarkTip": "（注意：模板保存中点击刷新或关闭页面，可能导致字段保存失败）。", "templateDynamicPosition.moreThenTip": "等{num}个书签", "templateDynamicPosition.dataSyncFailTip": "动态模板数据处理异常，请联系客服/技术支持处理", "templateDynamicFieldEdit.receiver": "接收方", "templateDynamicFieldEdit.info.notice": "请注意：", "templateDynamicFieldEdit.info.table.0": "动态表格不允许重名；", "templateDynamicFieldEdit.info.table.1": "当前编辑器内动态表格为占位符，使用模板时按真实行列数展示", "templateDynamicFieldEdit.info.noTable.0": "如果模板中多个位置添加了同一个业务字段，填写人只需填写一次，并保存为同一个值；", "templateDynamicFieldEdit.info.noTable.1": "属性设置会同步更新当前模板内所有同名业务字段", "authorityApprove.account": "账号：", "authorityApprove.name": "姓名", "authorityApprove.sealReselect": "申请人已拥有此印章，签署合同需要重新授权", "authorityApprove.electronicSeal": "选择印章", "authorityApprove.admin.title": "转交主管理员", "authorityApprove.admin.sender": "转交人：", "authorityApprove.admin.powerTitle": "转交人申请保留权限", "authorityApprove.admin.detailTitle": "{name}（账号：{applyUserAccount}）正在将 {entName} 的系统主管理员转交给您，主管理员主要职责与权限有：", "authorityApprove.admin.detailInfo": "1、企业印章使用与分配； | 2、企业成员管理； | 3、企业合同管理；", "authorityApprove.admin.tip": "系统主管理员通常由企业法定代表人、财务管理者、法务管理者、IT部门管理者或企业业务负责人等角色担任，以确保职责的有效履行。", "authorityApprove.admin.tip2": "您是否接受成为企业主管理员？", "authorityApprove.admin.resultSuccess.title": "您已接受转交", "authorityApprove.admin.resultSuccess.tip": "同意即代表您愿意成为企业管理员，以及同意授权相关权限及印章。", "authorityApprove.admin.resultFail.title": "您已拒绝转交", "authorityApprove.admin.resultFail.tip": "您已拒绝成为企业管理员，拒绝原因为：{reason}", "authorityApprove.admin.resultDetail": "转交审批详情", "authorityApprove.power.title": "权限审批", "authorityApprove.power.tip": "{applyUserName}为非企业成员，同意权限以后自动将该人员添加为企业成员。", "authorityApprove.power.applyUserName": "申请人姓名：", "authorityApprove.power.powerTitle": "申请权限：", "authorityApprove.power.resultSuccess.title": "您已完成授权", "authorityApprove.power.resultSuccess.tip": "申请人将获得部分权限和功能以完成{contractAlias}的签署。", "authorityApprove.power.resultFail.title": "您已驳回申请", "authorityApprove.power.resultFail.tip": "申请人将收到您的回复，并根据您的意见：{reason}，调整申请。", "authorityApprove.power.resultDetail": "申请审批详情", "authorityApprove.preAuth.applyUserName": "转交人：", "authorityApprove.preAuth.title": "企业实名认证", "authorityApprove.preAuth.powerTitle": "申请权限：", "authorityApprove.powerTitleTip": "申请查看权限的{contractAlias}包括未指定签署人账号的待认领{contractAlias}或者使用其他账号认领指定签署账号的{contractAlias}。", "authorityApprove.preAuth.presetConfiguration": "预设配置", "authorityApprove.preAuth.realName": "企业认证", "authorityApprove.preAuth.step1": "第一步：请您预设印章样式", "authorityApprove.preAuth.step2": "第二步：转交认证人员权限设置", "authorityApprove.preAuth.applyInfo.t1": "向您申请企业认证的用户", "authorityApprove.preAuth.applyInfo.t2": "申请认证的企业名称", "authorityApprove.preAuth.signInfo": "需要签署来自发件方 {contractSenderName} 的{contractAlias} {contractTitle}。", "authorityApprove.preAuth.seal.c1": "方式一: 使用系统默认样式", "authorityApprove.preAuth.seal.c2": "方式二: 上传图片生成企业印章", "authorityApprove.preAuth.tip": "{applyUserName}为非企业成员，同意权限以后自动将该人员添加为企业成员。", "authorityApprove.preAuth.resultDetail": "申请详情", "authorityApprove.viewEntContract": "查看企业{contract<PERSON><PERSON><PERSON>}", "authorityApprove.contractLimit": "{contractAlias}范围：", "authorityApprove.entSeal": "签署企业合同（电子公章）", "authorityApprove.entSealTooltip": "申请到印章后，在签署权限范围内的企业都可以使用该印章签署", "authorityApprove.getMore": "获取更多权限", "authorityApprove.distributionSeal": "分配印章", "moreRightDialog.moreRight": "更多权限", "moreRightDialog.confirmBtn": "确定", "moreRightDialog.viewContract": "查看企业合同", "moreRightDialog.nowContract": "当前合同", "moreRightDialog.nowSenderProxyContract2": "{contractSenderName}发送的{contractAlias}", "moreRightDialog.allSenderProxyContract": "所有{contract<PERSON><PERSON><PERSON>}", "moreRightDialog.proxyContractTip": "申请查看权限的合同包括未指定签署人账号的待认领合同或者使用其他账号认领指定签署账号的合同。", "moreRightDialog.signContract": "签署企业{contract<PERSON><PERSON><PERSON>}（{sealName}）", "moreRightDialog.nowSenderContract": "当前发件方发送的{contractAlias}", "moreRightDialog.nowSenderContract2": "{contractSenderName}发送的{contractAlias}", "moreRightDialog.allSenderContract": "所有合同", "moreRightDialog.noApplySignRight": "我不签署，不需要章", "moreRightDialog.canSignTip": "在可查看范围内允许签署。", "moreRightDialog.allTip": "说明：当前发件方包含该企业及其集团和子公司、业务线。", "batchOrAllOperateContract.iKnow": "我知道了", "batchOrAllOperateContract.oneTypeSeal": "同企业使用一种章", "batchOrAllOperateContract.moreTypeSeal": "不同合同用不同章", "batchOrAllOperateContract.noSealAuthTip": "由于您尚未获取必要权限签署这批合同，无法执行批量签署的操作。建议你先试着单独签署一份合同，找到不能批量签署的原因。", "batchOrAllOperateContract.noSealAuthTipTitle": "常见原因：", "batchOrAllOperateContract.noSealAuthTip1": "1、你尚未加入签约方企业，无法签署它的合同", "batchOrAllOperateContract.noSealAuthTip2": "2、你还没有印章，无法签署合同", "batchOrAllOperateContract.noSealAuthTip3": "3、你的企业规定某些发件方企业所发送的合同，需要额外签署授权才能签署，但你缺少该授权", "batchOrAllOperateContract.amountOfContract": "份合同", "batchOrAllOperateContract.noAuthEnt": "未实名企业", "batchOrAllOperateContract.personSign": "个人默认签名", "batchOrAllOperateContract.noChangeSeal": "暂无需要更改的印章数据，已为您使用默认印章", "batchOrAllOperateContract.noSealTip": "没有可以切换的印章", "batchOrAllOperateContract.selectSeal": "选择印章", "batchOrAllOperateContract.useSeal": "使用", "batchOrAllOperateContract.changeSeal": "更换印章", "batchOrAllOperateContract.moreContractSealTip": "需同时满足以下条件才能使用：1、您在上一步“批量签署的合同范围”中选择的是“仅勾选中的合同”；2、企业给您分配了多个章", "batchOrAllOperateContract.signFooterTip": "合同数量越多，批量签署结束的时间越长，请耐心等待", "batchOrAllOperateContract.yes": "是", "batchOrAllOperateContract.no": "否", "batchOrAllOperateContract.reject": "驳回", "batchOrAllOperateContract.agree": "同意", "batchOrAllOperateContract.cancel": "取消", "batchOrAllOperateContract.confirm": "确定", "batchOrAllOperateContract.continue": "继续", "batchOrAllOperateContract.continueSign": "继续签署", "batchOrAllOperateContract.batch": "批量", "batchOrAllOperateContract.nowBatch": "此次批量", "batchOrAllOperateContract.contractRange": "的合同范围是：", "batchOrAllOperateContract.operate": "操作", "batchOrAllOperateContract.allSelect": "共选中", "batchOrAllOperateContract.outputFailTip": "至少在合同列表选中一份合同进行导出", "batchOrAllOperateContract.changeStatusTip": "合同，非“逾期未签”合同状态的合同不会被操作。“逾期未签”的合同的状态将被修改为“签署中”，合同签约截止时间为7天后，是否向未签署合同的账号推送签约提醒?", "batchOrAllOperateContract.changeStatusSupplement": "为签署人设置的“签署时效”将不再生效", "batchOrAllOperateContract.approvalTip": "份合同，其中不需要您审批的合同不会被操作。请选择审批结果", "batchOrAllOperateContract.outputTip": "份合同。导出结果中是否包含多文档合同中的各个子合同?", "batchOrAllOperateContract.revokeTip": "份合同，其中已取消和已完成的合同不会被操作。", "batchOrAllOperateContract.remindTip": "份合同，其中已取消和已完成的合同不会被操作。", "batchOrAllOperateContract.transferTip": "份合同", "batchOrAllOperateContract.batchSignTip": "份合同，其中不需要您签署的合同不会被操作，此次批量签署统一使用的印章是:", "batchOrAllOperateContract.batchTagTip": "如果合同不是你的企业发出的，那么这份合同是不能被添加标签的。", "batchOrAllOperateContract.downloadTip": "份合同。", "batchOrAllOperateContract.batchOperateResultPage": "批量任务中心", "batchOrAllOperateContract.toHomePage": "回到首页", "batchOrAllOperateContract.refresh": "刷新页面", "batchOrAllOperateContract.logTips": "数量越多，批量操作所需时间越长，可先处理其它任务再进入此页面查看。（进入入口：1、合同管理 → 批量操作记录；2、档案＋ → 批量任务中心；3、控制台-充值管理-使用记录 → 使用记录下载任务 ）", "batchOrAllOperateContract.operateName": "操作名称", "batchOrAllOperateContract.operateTime": "操作时间", "batchOrAllOperateContract.operateResult": "操作结果", "batchOrAllOperateContract.operateLog": "操作日志", "batchOrAllOperateContract.remark": "备注", "batchOrAllOperateContract.contractTask": "合同管理批量任务", "batchOrAllOperateContract.archiveTask": "档案+批量任务", "batchOrAllOperateContract.useRecordTask": "使用记录下载任务", "batchOrAllOperateContract.createTime": "操作时间", "batchOrAllOperateContract.bizName": "档案柜名称", "batchOrAllOperateContract.bizId": "档案柜ID", "batchOrAllOperateContract.total": "数据条数", "batchOrAllOperateContract.cost": "预计耗时", "batchOrAllOperateContract.fileStatus": "文件状态", "batchOrAllOperateContract.archiveExport": "档案柜列表导出", "batchOrAllOperateContract.archiveImport": "档案柜预导入", "batchOrAllOperateContract.collectImport": "采集导入", "batchOrAllOperateContract.audit": "全量合同预审", "batchOrAllOperateContract.realNameAnnualVerify": "实名年审", "batchOrAllOperateContract.customInfoAnnualVerify": "自定义资料年审", "batchOrAllOperateContract.transfer": "资料转交", "batchOrAllOperateContract.queryResultsExport": "相对方企业查询结果导出", "batchOrAllOperateContract.status0": "等待进行", "batchOrAllOperateContract.status1": "进行中，请稍后...", "batchOrAllOperateContract.status2": "{day}天后过期", "batchOrAllOperateContract.status3": "已过期", "batchOrAllOperateContract.status4": "任务失败", "batchOrAllOperateContract.status5": "已取消", "batchOrAllOperateContract.download": "下载文件", "batchOrAllOperateContract.useRecordExport": "使用记录导出", "batchOrAllOperateContract.batchChangeStatus": "批量逾期后延期", "batchOrAllOperateContract.batchSetTag": "批量设置标签", "batchOrAllOperateContract.batchArchive": "批量归档", "batchOrAllOperateContract.batchRemind": "批量提醒", "batchOrAllOperateContract.batchRevoke": "批量撤回", "batchOrAllOperateContract.batchExport": "批量导出明细", "batchOrAllOperateContract.batchImport": "合同导入", "batchOrAllOperateContract.batchSend": "批量发送", "batchOrAllOperateContract.batchExportDownload": "下载", "batchOrAllOperateContract.view": "查看", "batchOrAllOperateContract.downloadLinkFailure": "链接已失效", "batchOrAllOperateContract.downloadCancel": "取消", "batchOrAllOperateContract.downloadError": "下载出错", "batchOrAllOperateContract.batchExportFail": "导出失败", "batchOrAllOperateContract.batchTransfer": "批量转交", "batchOrAllOperateContract.batchApproval": "批量审批", "batchOrAllOperateContract.batchDownload": "批量下载", "batchOrAllOperateContract.zip": "压缩包", "batchOrAllOperateContract.batchModifyLife": "批量修改合同到期日", "batchOrAllOperateContract.batchModifyTip": "合同到期时间≠签约截止时间，若设置签约提醒，请另外设置签约截止时间。", "batchOrAllOperateContract.batchSign": "批量签署", "batchOrAllOperateContract.notStart": "执行中", "batchOrAllOperateContract.doing": "执行中", "batchOrAllOperateContract.discontinue": "已终止", "batchOrAllOperateContract.viewProgress": "查看进度", "batchOrAllOperateContract.operateProgress": "操作进度", "batchOrAllOperateContract.tip": "提示", "batchOrAllOperateContract.discontinueOperate": "终止批量任务", "batchOrAllOperateContract.confirmDiscontinue": "终止任务后，未被处理的合同需要你重新进行操作。", "batchOrAllOperateContract.operateSuccess": "操作成功", "batchOrAllOperateContract.taskTerminated": "任务已被终止", "batchOrAllOperateContract.done": "已完成", "batchOrAllOperateContract.detail": "共需操作{totalCount}份合同，已操作{nowCount}份合同，数据最终以操作日志为准。", "batchOrAllOperateContract.moreBatch": "更多批量操作", "batchOrAllOperateContract.batchLog": "批量操作记录", "batchOrAllOperateContract.feedback": "协议信息管理问卷反馈", "batchOrAllOperateContract.tagTip": "某企业创建的标签只能设置到该企业发送的合同中，不能设置到其他企业发送的合同。", "batchOrAllOperateContract.changeTip": "继续操作则可将“逾期未签”的合同的状态修改为“签署中”，以便重启合同签署。", "batchOrAllOperateContract.remindFooterTip": "当日已经被提醒过6次的用户或尚未轮到签署/审批的用户不能被提醒。", "batchOrAllOperateContract.signTip.tip1": "包含以下签署要求的合同将不能在电脑网页批量签署：", "batchOrAllOperateContract.signTip.tip2": "（1）签署前必须完成以下操作才能签署的合同：通过签署前审批、阅读合同全文、填写合同业务字段、提交合同附属资料、提交签约主体资料、完成实名认证、获得指定印章、业务核对章。", "batchOrAllOperateContract.signTip.tip3": "（2）签署时需要完成以下操作的合同：必须手写签名", "batchOrAllOperateContract.signTip.tip4": "建议你先试着单独签署一份合同，找到不能批量签署的原因，或扫一扫二维码，在手机批量签署。", "batchOrAllOperateContract.downloadFooterTip": "若某企业未授予您下载权限，则您将无法下载该企业的合同。", "batchOrAllOperateContract.setOption1": "仅设置勾选中的合同", "batchOrAllOperateContract.changeOption1": "仅修改勾选中的合同", "batchOrAllOperateContract.revokeOption1": "仅撤回勾选中的合同", "batchOrAllOperateContract.remindOption1": "仅提醒勾选中的合同", "batchOrAllOperateContract.transferOption1": "仅转交勾选中的合同", "batchOrAllOperateContract.outputOption1": "仅导出勾选中的合同明细", "batchOrAllOperateContract.approvalOption1": "仅审批勾选中的合同", "batchOrAllOperateContract.signOption1": "仅签署勾选中的合同", "batchOrAllOperateContract.downloadOption1": "仅下载勾选中的合同", "batchOrAllOperateContract.setOption2": "设置列表中所有合同，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量操作的合同范围。", "batchOrAllOperateContract.changeOption2": "修改列表中所有合同，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量操作的合同范围。", "batchOrAllOperateContract.revokeOption2": "撤回当前列表中所有审批中和签署中的合同，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量操作的合同范围。", "batchOrAllOperateContract.remindOption2": "提醒当前列表中所有审批中和签署中的合同，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量操作的合同范围。", "batchOrAllOperateContract.transferOption2": "转交当前列表中所有合同，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量操作的合同范围。", "batchOrAllOperateContract.outputOption2": "导出当前列表中所有合同的合同明细，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量导出合同明细的合同范围。", "batchOrAllOperateContract.approvalOption2": "审批当前列表中所有待审批的合同，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量操作的合同范围。", "batchOrAllOperateContract.signOption2": "签署当前列表中所有待签署的合同，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量操作的合同范围。", "batchOrAllOperateContract.batchExportTip": "不超过5万条合同明细", "batchOrAllOperateContract.downloadOption2": "下载当前列表中所有合同，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量操作的合同范围。", "batchOrAllOperateContract.contractOutPut.outOfQuantity": "共选中{num}份合同，已超过5万份上限，无法批量导出。", "rejectSigner.tipTitle": "驳回重签提示", "rejectSigner.tipContent": "请先勾选需要驳回重签的签署字段（签名或盖章或签署人填写的字段），再点击“驳回重签”按钮。", "rejectSigner.noTip": "不再提示", "rejectSigner.iKnow": "我知道了", "rejectSigner.rejectBtn": "驳回重签", "rejectSigner.noOptionTip": "请先选择需要驳回重签的签署字段（签名或盖章或签署人填写的字段），再点击“驳回重签”按钮。", "rejectSigner.writeMustKnow": "填写重签须知", "rejectSigner.confirm": "确定", "rejectSigner.cancel": "取消", "rejectSigner.success": "驳回成功", "rejectSigner.fail": "正在重签的用户不能驳回重签，请等待用户签完后再操作。", "rejectSigner.mustKnowPlaceHolder": "选填，限255个字", "rejectSigner.mustKnowTip": "请完善签约须知，帮助签署人顺利重签", "rejectSigner.placeTop": "将重叠的顶层置底", "counterDialog.longTime": "正在发送合同，全部发送完成，大约还需要", "counterDialog.close": "我知道了", "counterDialog.minutes": "分钟", "timingSend.individualApprovalLimit": "各公司灵活配置审批，是不能使用“定时发送”功能的", "timingSend.hasNoTimingSendFeature": "您的企业尚未开通此功能", "timingSend.approveTopTip": "定时审批：“发送前审批”的第一顺序审批人收到合同的时间：", "timingSend.approveBottomTip": "下一位审批人收到合同的时间，是上一位审批人审批通过的时间。", "timingSend.signTopTip": "定时签署: 选择第一顺序签署人收到合同的时间： | 定时签署: 选择签署人收到合同的时间：", "timingSend.signBottomTip1": "如果设定的定时发送时间早于审批完成的时间，则需审批完成后才会立即发送。", "timingSend.signBottomTip2": "下一位签署人收到合同的时间，是上一位签署人签署完成的时间。", "timingSend.signBottomTip3": "如果第一顺序签署人所在企业设置了“签署前审批”，则为审批人收到合同的时间。在审批通过后签署人才能收到合同。| 如果签署人所在企业设置了“签署前审批”，则为审批人收到合同的时间。在审批通过后签署人才能收到合同。", "timingSend.receiveNow": "合同发送成功后立即收到", "timingSend.previousDay": "前一天", "timingSend.nextDay": "后一天", "timingSend.timeLimitTip1": "定时签署的时间不能早于定时审批时间", "timingSend.timeLimitTip2": "定时发送的时间至少需要设定在当前时间的15分钟以后。", "autoSignDialog.waitTip": "提示", "autoSignDialog.errorTip": "自动签失败原因", "autoSignDialog.title": "自动签失败原因提示", "autoSignDialog.reason": "因不满足以下条件，您不能使用自动签署功能签署此合同：", "autoSignDialog.wait": "正在准备自动签。", "tagManage.deleteDone": "删除成功", "tagManage.setDone": "设置成功", "tagManage.noEditPermission": "您没有配置履约提醒的权限", "tagManage.addLabel": "新增标签", "tagManage.delLabel": "移除标签", "tagManage.slotTip1": "· 配置规则对当前文件夹中的合同生效，不影响企业控制台-标签管理中的配置；", "tagManage.slotTip2": "· 文件夹与文件夹之间的配置规则也是互不影响的。", "tagManage.afterDelLblTip": "移除后，已在履约文件夹合同上的标签将消失", "tagManage.delLblAA": "移除标签“{name}”", "tagManage.confirm": "确认", "tagManage.cancel": "取 消", "tagManage.noTagTip": "企业控制台-标签管理中尚未设置标签", "tagManage.addTag": "添加标签", "tagManage.updateTip": "合同上的标签将被更新为:", "tagManage.noUseLabel": "不使用任何标签", "tagManage.confirmTitle": "提示", "tagManage.send": "所发送的合同:", "settingDownloadFileName.settingTitle": "设置合同下载名称", "settingDownloadFileName.defaultName": "系统默认文件名", "settingDownloadFileName.titleAndId": "(合同标题+合同ID)", "settingDownloadFileName.defineName": "自定义文件名", "settingDownloadFileName.defineNameTip": "（最多支持选择5个）", "settingDownloadFileName.limitNumTip": "请至少选择一个自定义文件名", "settingDownloadFileName.noInputValueTip": "请输入自定义文件名", "settingDownloadFileName.hasIllegalCharacter": "自定义文件名中含有非法字符", "settingDownloadFileName.inputPlaceholder": "请输入文件名称", "settingDownloadFileName.contractTitle": "合同标题", "settingDownloadFileName.contractId": "合同ID", "settingDownloadFileName.receiver": "接收方", "settingDownloadFileName.personOrEntName": "个人/企业名称", "settingDownloadFileName.hasSelectedCase": "已选组合：", "settingDownloadFileName.noRemindBtn": "登录期间不再提示", "settingDownloadFileName.tip": "注：采用私有存储方式的合同定义的文件名称将无法生效", "pageTitle.doc.docList": "合同管理", "pageTitle.doc.docDetail": "合同详情", "pageTitle.doc.docExport": "合同导出", "pageTitle.doc.docView": "合同预览", "pageTitle.doc.sealApply": "申请用印", "pageTitle.doc.authIntercept": "个人实名拦截", "pageTitle.doc.entAuthIntercept": "企业实名拦截", "pageTitle.doc.applyJoinEnt": "申请加入企业", "pageTitle.doc.batchLog": "批量任务中心", "pageTitle.doc.rejectSigner": "驳回重签", "pageTitle.dynamicTemplate.set": "设置模板", "pageTitle.dynamicTemplate.preview": "动态模板效果预览", "pageTitle.entDoc.manage": "履约文件夹管理", "pageTitle.entDoc.permission": "权限管理", "pageTitle.entDoc.list": "企业履约管理", "pageTitle.entDoc.detail": "企业合同详情", "pageTitle.send.prepare": "选择模板", "pageTitle.send.send": "发送合同", "pageTitle.send.batchImport": "批量发送合同", "pageTitle.send.inputField": "填写发件方业务字段", "pageTitle.permission.confirm": "权限确认页", "pageTitle.permission.apply": "权限申请", "pageTitle.template.list": "模板列表", "pageTitle.template.childList": "模板子列表", "pageTitle.template.detail": "模板详情", "pageTitle.template.permission": "权限管理", "pageTitle.template.config": "设置模板", "pageTitle.template.approval": "模板审批", "transferContract.transferContract": "移交", "transferContract.contractTransfer": "移交", "transferContract.tip": "提示", "transferContract.confirmTip": "移交后，将不再由您持有，您的管理列表中无法搜索到。确定移交吗?", "transferContract.accountNameTip": "接收人姓名与账号不匹配，请确保接收人信息真实准确。", "transferContract.noJoinEntTip": "该接收人账号未加入企业，移交后对方有可能无法完成签署，是否确定移交。", "transferContract.success": "移交成功", "transferContract.resultInfo": "已成功移交给{receiverName}（{receiverAccount}），他将收到签署通知。", "transferContract.verifyCodeTip1": "您可通过查验码或立即跳转小程序。", "transferContract.verifyCodeTip2": "查看签署进度。查看后，「上上签查合同」小程序将为您保留查验记录。", "transferContract.downloadVerifyCode": "下载查验码", "transferContract.receiverAccount": "接收人账号", "transferContract.receiverName": "接收人姓名", "transferContract.receiverAccountPlaceholder": "请输入接收人手机号或邮箱", "transferContract.accountErrorTip": "请输入正确的账号", "transferContract.receiverNamePlaceholder": "请输入接收人真实姓名", "transferContract.transferNamePlaceholder": "请输入您的姓名", "transferContract.transferName": "移交人姓名", "transferContract.transferAccount": "移交人账号", "transferContract.transferTip": "请注意接收人账号务必填写正确，如若移交错误，需要联系企业主管理员重新移交给正确签署人。", "transferContract.adminAccount": "账号：{entAdminAccount}", "transferContract.adminName": "企业昵称：{entAdminName}", "transferContract.admin": "主管理员", "transferContract.confirmTransfer": "确认移交", "transferContract.pleaseInputReceiverInfo": "请填写接收人信息：", "transferContract.pleaseInputTransferInfo": "请填写移交人信息：", "transferContract.name": "姓名：", "transferContract.account": "账号：", "transferContract.transfer": "移交", "transferContract.cancel": "取消", "ssoSendDialog.title": "提示", "ssoSendDialog.main": "请确认，您将以个人身份发送合同?", "ssoSendDialog.tip": "如需开展公司业务，请点击右上角，切换至对应的企业主体后，再发送合同。", "ssoSendDialog.confirm": "去切换至企业", "ssoSendDialog.cancel": "使用个人主体", "contractComparison.comparisonBtn": "合同比对", "contractComparison.translateBtn": "合同翻译", "contractComparison.reapplytn": "申请盖章", "contractComparison.waitforStamp": "该合同正等待{person}（{account}）盖章，是否需要更换盖章人？", "contractComparison.extractionBtn": "合同抽取", "contractComparison.documentSelect": "请选择要{type}的文件,仅支持word、pdf格式", "contractComparison.noPermission": "此为高级功能，请联系您专属的上上签工作人员或拨打客服电话（400-993-6665）进行开通。", "contractComparison.prompt": "提示", "contractComparison.sendedContract": "发件方的合同", "contractComparison.uploadedContract": "您上传的合同文件", "contractComparison.comparisonResult": "对比结果", "contractComparison.differences": "（共{num}处差异点）", "contractComparison.pageNum": "第{page}页", "contractComparison.difference": "差异{num}", "contractComparison.uploadTitle": "上传需要比对的文件", "contractComparison.uploadError": "仅支持word、pdf格式", "contractComparison.download": "下载发件方合同", "contractComparison.log.title": "合同比对记录", "contractComparison.log.detail": "详情", "contractComparison.log.comparing": "比对中...", "contractComparison.log.refresh": "刷新", "contractComparison.log.download": "下载比对结果", "contractComparison.log.toDetail": "查看对比详情", "contractComparison.doCompare": "比对", "contractComparison.doTranslate": "翻译", "contractComparison.doExtract": "抽取", "safeBox.guideTitle": "合同保险柜", "safeBox.guideTitleTip": "保密合同，只有合同持有人（参与合同的人）才能查看", "safeBox.howToUse": "如何使用保险柜？", "safeBox.useGuide": "查看使用说明", "safeBox.step1": "第一步：前往企业控制台-角色管理页，为角色勾选合同权限里的的合同保密项。", "safeBox.step2": "第二步：拥有权限后，在合同详情页可以将合同移入保险柜。", "safeBox.step3": "第三步：保险柜内的保密合同在合同管理页会带有“秘”图标。", "safeBox.hasNoPermission": "合同已被移入保险柜，仅合同持有人（参与合同的人）可以查看", "safeBox.noSafeBoxPermission": "此合同为保密合同，只有合同持有人才能查看；您可以联系合同持有人将合同恢复为正常合同后再查看", "safeBox.hideInfoPlaceholder": "保险柜隐藏数据", "safeBox.receiverTip": "参与了合同，含发送、审批、签署、补全以及被抄送的本企业/集团成员账号", "safeBox.entFolderTip": "通过履约文件夹分享后，可以被更多人查看，请注意是否有泄密风险", "safeBox.view": "查看", "safeBox.safeBox": "保险柜", "lang": "zh", "customsCheck.customsStandardCheck": "海关标准检测", "customsCheck.checking": "海关文件标准检测中，请稍后...", "customsCheck.customsFontRequire": "海关字体要求", "customsCheck.customsFontUse": "海关要求，使用以下14种标准字体中的一种或多种", "customsCheck.customsCheckResult": "海关文件检测结果", "customsCheck.fileNameRequire": "文件名称长度大于64个字符或32个汉字", "customsCheck.fontErr": "文件字体不符合海关要求，当前字体为 | 字体，请使用海关要求字体", "customsCheck.checkFontRequirement": "查看海关字体要求", "customsCheck.emptyPageErr": "文件存在空白页，请删除后进行发送，空白页为第 | 页", "customsCheck.checkStandard": "检测内容制定标准", "customsCheck.checkInGov": "具体以按照在海关系统实际加签结果为准", "customsCheck.nameMulErr": "文件名称长度检测（64个字符或32个汉字以内）", "customsCheck.emptyMulErr": "文件空白页检测（文件里不允许存在空白页）", "customsCheck.checkFont": "文件字体检测", "customsCheck.notSatisfy": "不满足要求", "customsCheck.useCustomsFont": "使用海关要求字体", "customsCheck.checkSuccess": "检测完成，已通过检测", "customsCheck.checkPass": "检测通过", "customsCheck.document": "文件", "docContentTableCol.noReceiver": "未添加任何收件人", "docSlider.check2017ContractTip.title": "提示", "docSlider.check2017ContractTip.msg": "请在新打开的窗口页面中查看2017年以前合同", "docSlider.check2017ContractTip.confirm": "知道了", "docSlider.check2017Contract": "查看2017年以前合同", "docSlider.all": "全部", "docSlider.unarchive": "未归档", "docSlider.archived": "已归档", "docSlider.shareToMemberTips.title": "您将分享给：{name}的所有成员", "docSlider.shareToMemberTips.share": "分享", "docSlider.shareToMemberTips.confirm": "确定", "docSlider.shareToMemberTips.cancel": "取消", "docSlider.deleteFolderTips.title": "删除后，文件夹中的合同会移到\"所有合同\"中，确认删除吗？", "docSlider.deleteFolderTips.delete": "删除", "docSlider.deleteFolderTips.confirm": "确定", "docSlider.deleteFolderTips.cancel": "取消", "docSlider.shareToMeFolder": "别人共享给我的文件夹", "docSlider.sharedFolder": "共享文件夹", "docSlider.cancelShare": "取消共享", "docSlider.syncEntFolder": "同步至履约文件夹", "docSlider.stopSyncEntFolder": "停止同步至履约文件夹", "docSlider.share": "共享", "docSlider.initSign": "发送合同", "docSlider.otherSource": "查看其他来源文件", "docSlider.otherSourceTip": "点击后可查看接口平台发起的合同、存证文件、上上签V2合同", "docSlider.toggleSearchBar": "显示搜索\\标签栏", "docSlider.toggleSearchBar1": "当前显示搜索\\标签栏", "docSlider.toggleSearchBar2": "当前隐藏搜索\\标签栏", "docSlider.fastOperate": "快速操作", "docSlider.operateStatus.signing": "签约中", "docSlider.operateStatus.needMeOperate": "需要我操作", "docSlider.operateStatus.inApproval": "审批中", "docSlider.operateStatus.needOthersSign": "需要他人签署", "docSlider.operateStatus.closing": "即将截止签约", "docSlider.operateStatus.signComplete": "签约完成", "docSlider.operateStatus.closed": "已经截止签约", "docSlider.allDocs": "所有文件", "docSlider.allDocsType.inbox": "收件箱", "docSlider.allDocsType.outbox": "发件箱", "docSlider.allDocsType.closing": "合同即将到期", "docSlider.allDocsType.closed": "合同已到期", "docSlider.allDocsType.draft": "草稿箱", "docSlider.folder": "个人文件夹", "docSlider.rename": "重命名", "docSlider.delete": "删除", "docSlider.enterpriseFolder": "企业文件夹（合同类型）", "docSlider.enterpriseFilingContract": "企业归档合同", "docSlider.noAuthority": "没有新版合同管理入口权限", "docSlider.noAuthorityTips.info": "开通后，您将体验”企业归档合同”管理的功能，有助于企业高效的管理合同。", "docSlider.noAuthorityTips.toDetail": "详情请看", "docSlider.noAuthorityTips.cancel": "取 消", "docSlider.noAuthorityTips.open": "开通", "docSlider.assignAuthority": "请联系管理员给您分配权限", "docSlider.msgBox.tips": "提示", "docSlider.msgBox.info": "请在新打开的窗口页面中查看“其它来源文件”", "docSlider.msgBox.confirm": "知道了", "docSlider.addFolder": "添加文件夹", "docSlider.switchEntFail": "切换企业归档合同失败，请稍后再试！", "docSlider.openFail": "开通失败", "docSlider.openLater": "开通失败，请稍后再试！", "docSlider.notPublicUser": "该用户不是公有云用户", "docSlider.notEntUser": "该用户不是企业用户", "docSlider.cantReadEntFolder": "该用户不可查看企业文件夹（合同类型）", "docSlider.stick": "置顶", "docSlider.autoArchive": "合同自动归档", "docSlider.performanceManage": "进入企业履约管理", "docContentTop.searchTitlePlaceholder": "合同名称/发送人名称/发送人企业名称", "docContentTop.moreSearch": "更多搜索", "docContentTop.output": "导出", "docContentTop.import": "导入", "docContentTop.allContracts": "所有合同", "docContentTop.listConfig": "列表配置", "docContentTop.filterList.signStatus": "签约状态", "docContentTop.filterList.archiveFolders": "文件夹", "docContentTop.filterList.contractType": "合同类型", "docContentTop.filterList.sharedByMe": "我分享的", "docContentTop.filterList.sharedToMe": "被分享的", "docContentTop.filterList.allFiles": "全部文件", "docContentTop.companyTree": "组织架构", "docContentTop.unmovedContract": "未移动的合同", "docContentTop.contractLabelList.labor": "劳动合同", "docContentTop.contractLabelList.borrow": "借款合同", "docContentTop.contractLabelList.legal": "法律合同", "docContentTop.contractLabelList.loan": "贷款合同", "docContentTop.contractLabelList.transfer": "转让合同", "docContentTop.search.contractNum": "合同编号", "docContentTop.search.contractNumPlaceholder": "请输入合同编号", "docContentTop.search.sender": "发件方", "docContentTop.search.senderPlaceholder": "发件方名称或账号", "docContentTop.search.receiver": "收件方", "docContentTop.search.receiverPlaceholder": "收件方名称或账号", "docContentTop.search.sendTime": "签约发起时间", "docContentTop.search.signDeadline": "签约截止时间", "docContentTop.search.timeStartPlaceholder": "请选择起始时间", "docContentTop.search.timeEndPlaceholder": "请选择结束时间", "docContentTop.search.source": "来源平台", "docContentTop.search.ssq": "上上签", "docContentTop.search.search": "搜索", "docContentTop.searchMsg.contractNum": "请输入正确的合同编号", "docContentTop.searchMsg.sender": "请输入正确的发件方名称或账号", "docContentTop.searchMsg.receiver": "请输入正确的收件方名称或账号", "docContentTop.all": "全部", "docContentTop.signStatus.needMeApproval": "等待我审批", "docContentTop.signStatus.needMeSign": "等待我签署", "docContentTop.signStatus.inApproval": "审批中", "docContentTop.signStatus.needOthersSign": "需要他人签署", "docContentTop.signStatus.signComplete": "签约完成", "docContentTop.signStatus.signOverdue": "逾期未签", "docContentTop.signStatus.rejected": "已拒签", "docContentTop.signStatus.revoked": "已撤回", "docContentTop.searchAll": "全选", "docContentTop.confirm": "确定", "docContentTop.reset": "重置", "docContentTop.selectRange": "选择日期范围", "docContentTop.datePicker.weekend": "最近一周", "docContentTop.datePicker.month": "最近一个月", "docContentTop.datePicker.month3": "最近三个月", "docContentTop.popover.listStatus": "列表状态", "docContentTop.popover.reset": "恢复初始状态", "docContentTop.popover.showLabel": "显示字段", "docContentTop.popover.showLabelOperate": "（拖拽调整顺序，点击×删除）", "docContentTop.popover.most": "最多 50 个", "docContentTop.popover.hideLabel": "隐藏字段", "docContentTop.popover.hideLabelOperate": "（拖拽至上面的区域中进行添加）", "docContentTop.popover.confirm": "确定", "docContentTop.popover.cancel": "取消", "docContentTop.exportPopover.title": "导出字段配置", "docContentTop.exportPopover.exportLabel": "导出字段", "docContentTop.exportPopover.showLabelOperate": "（拖拽调整顺序，点击×删除）", "docContentTop.exportPopover.hideLabel": "隐藏字段", "docContentTop.exportPopover.hideLabelOperate": "（拖拽至上面的区域中进行添加）", "docContentTable.hideField": "隐藏字段", "docContentTable.showField": "展示字段", "docContentTable.notFoundField": "找不到模板字段", "docContentTable.notFoundFieldTips.0": "在模板中使用了一个名称是“xxx”的临时字段，如果需要被检索，那么：", "docContentTable.notFoundFieldTips.1": "在企业控制台-字段管理-内容字段页面，配置一个名称是“xxx”的字段并等待约15分钟后，此处将出现“xxx”字段供您配置", "docContentTable.userSigned": "该用户已签署", "docContentTable.userReject": "该用户已拒签", "docContentTable.userNotSign": "该用户未签署", "docContentTable.resend": "重新发送", "docContentTable.claim": "认领签署", "docContentTable.pendingClaim": "待认领", "docContentTable.proxySign": "代签署", "docContentTable.contractMoveToAllFolder": "合同已移动到所有合同中", "docContentTable.contractMoveToRecycle": "合同已移动到回收站中", "docContentTable.contractRestoreToAllFolder": "合同已成功恢复至【所有合同】文件夹", "docContentTable.notAllowDeleteContract": "当前文件夹不允许删除合同", "docContentTable.searchTooMuchResultTip": "当前搜索结果过多，请优化您的搜索条件", "docContentTable.cannotMoveToRecycle": "待认领合同不支持被移入回收站", "docContentTable.moveToRecycleTip": "移入回收站不影响合同状态与操作", "docContentTable.statusIconDesc.draft": "草稿", "docContentTable.statusIconDesc.signning": "签署中", "docContentTable.statusIconDesc.reject": "合同已拒签", "docContentTable.statusIconDesc.invalid": "合同已作废", "docContentTable.statusIconDesc.complete": "已完成", "docContentTable.statusIconDesc.revoked": "合同已撤回", "docContentTable.statusIconDesc.signOverdue": "合同已逾期", "docContentTable.statusIconDesc.sendApprove": "发送审批中", "docContentTable.statusIconDesc.approveReject": "审批被驳回", "docContentTable.deleteLableSuccess": "撕标签成功", "docContentTable.clickDeleteLabel": "点击揭除标签", "docContentTable.canNotBatchDownloadTip": "无法批量下载，您需要进行实名并保持实名信息一致或者向合同发起人获取下载码，才可以进行合同的下载", "docContentTable.noCanDownloadContract": "无可下载的合同", "docContentTable.downloadRemainContract": "下载其余合同", "docContentTable.cancleDownload": "取消下载", "docContentTable.remind": "提醒", "docContentTable.needDownloadCodeTip": "您需要向合同发起人获取下载码，逐一进行下载。", "docContentTable.needAuthTip": " 您需要实名认证或者与当前的实名信息保持一致，才可以进行合同的下载。", "docContentTable.revokeContractIdsTip": "合同已被撤销，无法下载。", "docContentTable.canNotDownload": "无法批量下载。", "docContentTable.partCanNotDownload": "部分合同无法批量下载", "docContentTable.noCanDeleteContract": "无可删除的合同", "docContentTable.transferSucess": "转交成功", "docContentTable.transferFailure": "转交失败", "docContentTable.notRightToTransferTip": "已勾选中的合同都不支持您进行转交，请重新勾选合同后重试", "docContentTable.batchSetTag": "批量设置标签", "docContentTable.setTag": "设置标签", "docContentTable.transfer": "转交", "docContentTable.archive": "归档", "docContentTable.invalid": "作废", "docContentTable.moveOutRecycle": "恢复", "docContentTable.moveToRecycle": "移入回收站", "docContentTable.exportSignLink": "导出签署链接", "docContentTable.exportSignLink.tip1": "批量导出签署链接", "docContentTable.exportSignLink.tip2": "被导出的签署链接需同时满足以下条件：", "docContentTable.exportSignLink.tip3": "·为当前页勾选合同的签署链接；", "docContentTable.exportSignLink.tip4": "·由您或您所在企业/集团发送的合同链接；", "docContentTable.exportSignLink.tip5": "·当前轮到签署方的签署链接。", "docContentTable.changeSigner": "更换签署人", "docContentTable.changeSignerDialog.confirm": "确认", "docContentTable.changeSignerDialog.cancel": "取消", "docContentTable.changeSignerDialog.title": "未签署合同的个人签署人", "docContentTable.changeSignerDialog.tips": "未轮到签署的个人不能被更改", "docContentTable.changeSignerTable.signerName": "姓名", "docContentTable.changeSignerTable.idCard": "身份证", "docContentTable.changeSignerTable.account": "接收手机/邮箱", "docContentTable.changeSignerTable.roleName": "签约角色", "docContentTable.changeSignerOption.must": "必填", "docContentTable.changeSignerOption.optional": "选填", "docContentTable.changeSignerVerify.invalidSubmit": "提交数据存在格式错误", "docContentTable.changeSignerVerify.success": "保存成功", "docContentTable.changeSignerVerify.fail": "保存失败", "docContentTable.changeSignerVerify.invalidIDCard": "身份证格式错误", "docContentTable.changeSignerVerify.invalidAccount": "接收手机/邮箱格式错误", "docContentTable.changeSignerVerify.invalidSignerName": "姓名存在格式错误", "docContentTable.batchOperateLoading": "批量操作正在加载中", "docContentTable.selectedContracts": "已选中{num}份文件", "docContentTable.operationTips": "请先确定业务线后再继续操作", "docContentTable.batchBtn.sign": "批量签署", "docContentTable.batchBtn.approval": "批量审批", "docContentTable.batchBtn.remind": "批量提醒", "docContentTable.batchBtn.revoke": "批量撤回", "docContentTable.batchBtn.download": "批量下载", "docContentTable.batchBtn.delete": "批量删除", "docContentTable.batchBtn.move": "批量移动", "docContentTable.operate": "操作", "docContentTable.searchNull": "未查到此类合同", "docContentTable.toDetail": "跳转详情", "docContentTable.download": "下载", "docContentTable.move": "移动", "docContentTable.reSend": "重新发起", "docContentTable.moveCancel": "取消移动", "docContentTable.signer": "接收方", "docContentTable.status": "状态", "docContentTable.sendDate": "发送日期", "docContentTable.deadline": "签约截止日期", "docContentTable.contractStatus.needMeSign": "需要我签署", "docContentTable.contractStatus.needMeApproval": "需要我审批", "docContentTable.contractStatus.inApproval": "审批中", "docContentTable.contractStatus.needOthersSign": "需要他人签署", "docContentTable.contractStatus.signComplete": "签约完成", "docContentTable.contractStatus.draft": "草稿", "docContentTable.contractStatus.signOverdue": "逾期未签", "docContentTable.contractStatus.rejected": "已拒签", "docContentTable.contractStatus.revoked": "已撤回", "docContentTable.contractStatus.beRejected": "被拒签", "docContentTable.contractStatus.deadline": "签约截止", "docContentTable.contractStatus.invalid": "已作废", "docContentTable.signStatus": "签约状态", "docContentTable.catchMap.download": "下载", "docContentTable.catchMap.reject": "拒签", "docContentTable.catchMap.revoke": "撤回", "docContentTable.catchMap.delete": "删除", "docContentTable.catchMap.cantOperate": "无法{operate}合同", "docContentTable.catchMap.hybridNetHeader": "发件方企业采用了合同私有存储的方式，但当前网络无法连接至发件方的合同存储服务器。", "docContentTable.catchMap.hybridNetMsg": "建议您：检查网络是否正常", "docContentTable.catchMap.checkNet": "请检查网络是否正常", "docContentTable.confirm": "确定", "docContentTable.cancel": "取消", "docContentTable.continue": "继续下载", "docContentTable.next": "继续", "docContentTable.delete": "移出分组", "docContentTable.searchAll": "全选", "docContentTable.nullToSign": "无可签署文件", "docContentTable.nullToApproval": "无可审批文件", "docContentTable.nullToRemind": "无可提醒文件", "docContentTable.nullToRevoke": "无可撤回文件", "docContentTable.sign": "签署", "docContentTable.approval": "审批", "docContentTable.remindSucc": "提醒成功", "docContentTable.remindFail": "以下 {errorSum} 份合同批量提醒失败：", "docContentTable.notice": "提示", "docContentTable.revoke": "撤回", "docContentTable.revokeReason": "批量撤回原因", "docContentTable.batchDownloadTip.msg1": "以下合同的发件方企业采用了合同私有存储的方式，但当前网络无法连接至发件方的合同存储服务器", "docContentTable.batchDownloadTip.msg2": "无法下载的合同名称如下:", "docContentTable.deleteSucc": "删除成功", "docContentTable.giveAuthor": "请联系管理员给您分配权限", "docContentTable.view": "查看", "docContentTable.paperSign": "纸质签署", "docContentTable.onLineSign": "电子签名", "docContentTable.usePaperSign": "启用纸质签", "docContentTable.modifyExpires": "修改合同到期日", "noBizLineDoc.title": "未指定业务线（或接收人尚未加入企业）的合同", "noBizLineDoc.confirm": "确认", "noBizLineDoc.cancel": "取消", "noBizLineDoc.noBizLineTip": "您有{number}份未指定业务线（或接收人尚未加入企业）的合同，请点此前往处理", "docDialog.searchName": "检索名称", "docDialog.inputSearchName": "请输入常用检索设置名称", "docDialog.commonSearch": "常用检索", "docDialog.saveCommonSearch": "保存为常用检索", "docDialog.setSearchName": "设置常用检索名称", "docDialog.setSearchNameTip": "为刚保存的常用检索设置名称", "docDialog.autoSetListConfitTip": "使用时，列表配置（列表表头）会按这批搜索项做更新", "docDialog.searchNameNotEmpty": "常用检索名称不能为空", "docDialog.lestSearchContentTip": "至少需要填写一个搜索内容", "docDialog.deleteSearchContent": "是否删除这项常用检索", "docDialog.maxSetCommonSearchAmout": "最多设置10个常用检索", "docDialog.approvalOpinion.title": "批量审批意见", "docDialog.approvalOpinion.yes": "同意", "docDialog.approvalOpinion.no": "驳回", "docDialog.approvalOpinion.result": "审批结果：", "docDialog.approvalOpinion.fillInContent": "请填写审批意见：", "docDialog.approvalOpinion.placeHolder": "选填，限255个字", "docDialog.notice": "提示", "docDialog.ok": "知道了", "docDialog.inputSenderPersonAndCompany": "请输入个人/企业名称", "docDialog.inputReceiverPersonAndCompany": "请输入个人/企业名称", "docDialog.contractLblSearch": "合同标签检索", "docDialog.putAway": "收起", "docDialog.unfold": "展开", "docDialog.searchFor": "搜索", "docDialog.contractInvolveTip": "个人或企业成员参与的合同", "docDialog.plsSelect": "请选择", "docDialog.moreBusinessFields": "更多（业务字段）", "docDialog.confirm": "确认", "docDialog.plsSelectData": "请选择日期", "docDialog.addShortcutEntrance": "新增快捷入口", "docDialog.reStoreState": "恢复初始状态", "docDialog.showEntrance": "展示入口", "docDialog.hideEntrance": "隐藏入口", "docDialog.shortcutManage": "快捷入口管理", "docDialog.plsInputShortcutName": "请填写快捷入口名称", "docDialog.plsChooseLable": "请选择合同标签", "docDialog.plsChooseSignStatus": "请选择操作状态", "docDialog.plsInputRoleTip": "请填写业务角色", "docDialog.plsInputInfoTip": "条件信息不完整，请补充", "docDialog.accountFormatErrorTip": "请输入正确的手机号或邮箱", "docDialog.finish": "完成", "docDialog.search": "查询", "docDialog.meetAllConditiionTip": "同时满足以上条件的合同可以在快捷入口", "docDialog.plsChooseLblName": "请选择标签名称", "docDialog.ifLableContain": "如果合同标签包含", "docDialog.defineAsLable": "按标签定义", "docDialog.addCondition": "新增条件", "docDialog.plsInputPlaceholder": "请输入业务角色|请输入邮箱/手机号", "docDialog.signStatusIsTip": "且其操作状态为", "docDialog.conditionTypeText.APPROVER": "审批人", "docDialog.conditionTypeText.ROLE": "签约角色", "docDialog.conditionTypeText.SIGNER": "签署人账号", "docDialog.conditionTypeText.SENDER": "发起人", "docDialog.ifOrEqual": "如果|等于", "docDialog.codition": "条件", "docDialog.defineRoleTip": "可以根据合同的签约角色，或根据本企业的合同签署人、审批人、合同发起人的账号以及操作进行合同的自定义筛选", "docDialog.defineByStatus": "按操作状态定义", "docDialog.back": "返回", "docDialog.chooseIdentity": "选择身份", "docDialog.plsSelectTime": "请选择一个时间", "docDialog.noMoreThan365dayTip": "天（不超过365天）", "docDialog.noMoreThan365day": "不超过365天", "docDialog.customize": "自定义", "docDialog.beforeNumDay": "前{num}天", "docDialog.belowContractOperteFail": "以下合同{tip}，操作失败：", "docDialog.plsInput": "请输入", "docDialog.goSign": "去签署", "docDialog.num": "份", "docDialog.otherNeedSignTip": "其他需要签署的合同", "docDialog.selectedContracts": "已选中{num}份", "docDialog.needClaimContract": "需要认领签署的合同", "docDialog.batchContainClaimHint": "批量签署的合同中包含了待认领合同，请确认这些合同是否应该由您来签署！", "docDialog.batchDowLoadHint": "您选中的合同需要分批下载", "docDialog.labelNotAddTips": "标签{tag}不能贴到以下合同上", "docDialog.batchDownloadTips": "您选择的合同存储于不同的存储服务器中，需分批次下载合同，混合云合同的合同附属资料无法随合同批量下载。请依次点击以下按钮完成合同下载", "docDialog.batchDownload": "批量下载", "docDialog.saver": "合同存储者", "docDialog.ssq": "上上签", "docDialog.cancel": "取 消", "docDialog.delete": "删除", "docDialog.deleteTips": "该文件夹中的文件不会一起删除", "docDialog.deleteConfirm": "确定删除该文件夹吗", "docDialog.delLabel": "删除标签", "docDialog.folderPlaceholder": "请输入文件夹名称", "docDialog.addFolder": "添加文件夹", "docDialog.addLabel": "添加标签", "docDialog.dividedLabel": "多个标签用回车分隔", "docDialog.myLabels": "我的标签", "docDialog.label": "标签{num}", "docDialog.max10": "最多添加10个标签", "docDialog.length10": "标签最长为10字符", "docDialog.labelExist": "标签已存在", "docDialog.selectMember": "选择成员", "docDialog.batchBtn.sign": "批量签署", "docDialog.batchBtn.approval": "批量审批", "docDialog.batchBtn.remind": "批量提醒", "docDialog.batchBtn.revoke": "批量撤回", "docDialog.batchSignTips": "需要您签署的，且不需要填写内容的合同可批量签署", "docDialog.supportBatch": "以下合同支持批量{type}", "docDialog.remindSucc": "提醒成功", "docDialog.remindFail": "以下 {errorSum} 份合同批量提醒失败：", "docDialog.remind": "提示", "docDialog._confirm": "确定", "docDialog._cancel": "取消", "docDialog.revoke": "撤回", "docDialog.exportDetail": "导出合同明细", "docDialog.lessThan365": "发起时间间隔最多不能超过365天", "docDialog.narrowRange": "合同条数超过{maxNum}条，请缩短时间范围", "docDialog.lessThan2000": "单次导出条数请勿超过{maxNum}条", "docDialog.sendTime": "签约发起时间", "docDialog.signDeadline": "签约截止时间", "docDialog.timeStartPlaceholder": "选择起始时间", "docDialog.to": "至", "docDialog.timeEndPlaceholder": "选择结束时间", "docDialog.signStatus": "签约状态", "docDialog.noFileExport": "暂无文件可导出", "docDialog.downloading": "下载中，请耐心等待...", "docDialog.move": "移动", "docDialog.folder": "文件夹", "docDialog.openFuzzyMatch": "当前为精确搜索，点击后将改用模糊搜索", "docDialog.closeFuzzyMatch": "当前为模糊搜索，点击后将开启精确搜索（与搜索时使用的关键字完全一致的合同才能被搜出）", "docDialog.archiveRuleDialog.title": "合同归档规则", "docDialog.archiveRuleDialog.tipContent.0": "【合同自动归档】 通过筛选条件对合同进行过滤， 对符合要求的合同自动执行归档。", "docDialog.archiveRuleDialog.tipContent.1": "【第一步】： 新建规则， 选择条件输入条件值并保存。", "docDialog.archiveRuleDialog.tipContent.2": "【 第二步】： 选择目标文件夹（ 一份合同只能同时归档进一个文件夹）。", "docDialog.archiveRuleDialog.tipContent.3": "规则触发： 在规则建立后， 新发出， 收到的合同， 未归档合同的状态类型等发生变更， 手动对历史合同执行规则， 会触发规则执行。 归档仅在每日00: 00 自动执行。", "docDialog.archiveRuleDialog.tipContent.4": "执行顺序： 按照列表顺序执行， 先按系新规则对未归档合同进行归档（ 归档的合同不再执行规则）， 若合同不符合规则， 则继续执行下一条规则。", "docDialog.archiveRuleDialog.create": "新建规则", "docDialog.archiveRuleDialog.excuTime": "归档仅在每日00:00自动执行", "docDialog.archiveRuleDialog.operation": "操作", "docDialog.archiveRuleDialog.ruleName": "规则名称", "docDialog.archiveRuleDialog.modify": "修改", "docDialog.archiveRuleDialog.enable": "启用", "docDialog.archiveRuleDialog.disable": "停用", "docDialog.archiveRuleDialog.delete": "删除", "docDialog.archiveRuleDialog.cancel": "取消", "docDialog.archiveRuleDialog.confirm": "确认", "docDialog.archiveRuleDialog.saveSuccess": "保存成功", "docDialog.archiveRuleDialog.excuAutoArchive": "对历史合同执行归档", "docDialog.archiveRuleDialog.excuSuccess": "执行成功", "docDialog.archiveRuleDialog.isRunning": "规则执行中...", "docDialog.editArchiveRuleDialog.title": "1.设置规则条件", "docDialog.editArchiveRuleDialog.rulePlaceHolderRight": "请从左边栏选择字段", "docDialog.editArchiveRuleDialog.rulePlaceHolderLeft": "条件1", "docDialog.editArchiveRuleDialog.next": "下一步", "docDialog.editArchiveRuleDialog.cancel": "取消", "docDialog.editArchiveRuleDialog.back": "返回", "docDialog.editArchiveRuleDialog.prev": "上一步", "docDialog.editArchiveRuleDialog.save": "保存", "docDialog.editArchiveRuleDialog.selectFloderTitle": "2.选择归档文件夹", "docDialog.editArchiveRuleDialog.baseOnRcpt": "基于人员", "docDialog.editArchiveRuleDialog.baseOnAttr": "基于系统字段", "docDialog.editArchiveRuleDialog.baseOntBusinessField": "基于业务/描述字段", "docDialog.editArchiveRuleDialog.baseOnTag": "基于合同标签", "docDialog.editArchiveRuleDialog.selectFolder": "请选择文件夹", "docDialog.editArchiveRuleDialog.ruleNotEmpty": "条件{index}不能为空", "docDialog.editArchiveRuleDialog.multiPlaceHolder": "多个字段值用\", \"号分隔", "docDialog.editArchiveRuleDialog.condition": "条件", "docDialog.editArchiveRuleDialog.include": "包含", "docDialog.editArchiveRuleDialog.selectOnTag": "已选基于合同标签", "docDialog.editArchiveRuleDialog.selectOnField": "已选\"{name}\"字段", "docDialog.editArchiveRuleDialog.errorMsg": "条件异常，请修改", "docBatch.agree": "同意", "docBatch.reject": "驳回", "docBatch.approve": "审批", "docBatch.approving": "审批中...", "docBatch.approved": "审批成功", "docBatch.approveAgree": "审批结果：同意", "docBatch.approveReject": "审批结果：驳回", "docBatch.approveSuggest": "审批意见", "docBatch.canNotInput": "可不填", "docBatch.confirm1": "确 定", "docBatch.cancel1": "取 消", "docBatch.inputSignPsw": "请输入签约密码", "docBatch.inputSixDigitalNum": "请输入6位数字", "docBatch.changeEmailVerify": "切换邮箱验证", "docBatch.changePhoneVerify": "切换手机号验证", "docBatch.batchApproveSuccess": "批量审批成功", "docBatch.belowContractApporveFail": "以下 {length} 份合同批量审批失败", "docBatch.tips": "提示", "docBatch.confirm": "确定", "docBatch.signVerify": "签约校验", "docBatch.signPswLockedTip": "签约密码已被锁定，3小时后自动解锁，你也可以通过", "docBatch.findBackPsw": "找回密码", "docBatch.toUnlockNow": "来立即解锁", "docBatch.signPswCanEnterTip": "签约密码错误,你还可以输入2/1次,是否", "docBatch.forgetPsw": "忘记密码", "docBatch.verifyFormatError": "验证码格式错误", "docBatch.signPassFormatError": "签约密码格式错误", "docBatch.networkTimeoutTip": "网络超时，请刷新页面查看合同签署情况", "docBatch.batchSignSuccess": "批量签署成功", "docBatch.belowContractSignFail": "以下 {length} 份合同批量签署失败", "docBatch.signWithNameTip": "当前正在以{name}的名义签署合同", "docBatch.useElectronicSeal": "使用电子印章", "docBatch.noWantUseTheSealTip": "不想使用该印章？实名认证后可更换印章", "docBatch.toAuth": "去实名认证", "docBatch.useSignature": "使用签名", "docBatch.signatureFinishTip": "签名已完成，你也可以去实名，让自己的签名更有法律保障", "docBatch.toUseSignatureTip": "请点击左侧“签名处”进行签名, 您也可以先实名，让自己的签名更有法律保障", "docBatch.batchSign": "批量签署", "docBatch.batchApprove": "批量审批", "docBatch.ssqNotReviewDiffTip": "上上签不对合同的当前版本与生效版本之间的内容差异进行审核，使用批量签署功能即代表您认可并同意签署以下合同的生效版本", "docBatch.chooseElectronicSeal": "选择印章", "docBatch.fileNumTip": "{num}份文件", "docBatch.totalFileNumTip": "共{num}份文件", "docBatch.chooseDefaultSignature": "选择默认签名", "docBatch.more": "更多", "docBatch.sign": "签署", "docBatch.file": "文件", "docBatch.signature": "签名", "docBatch.dataErrorTip": "数据出错，请重试！", "docBatch.noCanSignContract": "无可签署合同", "docBatch.noCanApproveContract": "无可审批合同", "docBatch.noReceiver": "未添加任何收件人", "docBatch.label": "标签", "docView.totalPageTip": "第{num}页，共{total}页", "docView.numOfPage": "页数", "docView.page": "页", "docView.canNotCheckContract": "无法查看合同", "docView.privateStoreContractTip": "发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器", "docExport.exportRecord": "导出记录", "docExport.refresh": "刷新", "docExport.every1000ContractWaitingTime": "每1000条合同明细的等待时间约为半分钟", "docExport.fileName": "文件名", "docExport.createTime": "创建时间", "docExport.chooseTimePeriod": "选择时间段", "docExport.to": "至", "docExport.contractStatus": "合同状态", "docExport.operate": "操作", "docExport.download": "下载", "docExport.expired": "已过期", "docExport.waitExport": "等待导出", "batchSearch.add": "批量导入", "batchSearch.batchAddOption": "批量添加选项", "batchSearch.batchAddTip": "默认以行为单位，一行一项，请依次填写。", "batchSearch.batchAddPlaceHolder": "请输入内容", "batchSearch.name": "批量搜索", "batchSearch.viewContract": "查看合同", "batchSearch.selectSearchItem": "选择搜索条件", "batchSearch.itemSelectTip": "请选择搜索条件", "batchSearch.searchContent": "批量输入（每个搜索词占一行，不超过20行）", "batchSearch.searchContentLineLimitTip": "如果搜索条件选中的是合同编号或公司内部编号，最多可支持100行", "batchSearch.searchContentPlaceholder": "请输入搜索词", "batchSearch.searchConditions": "搜索记录：{num}行", "batchSearch.contractsTotal": "合同数：{count}份", "batchSearch.unfindConditions": "未查询到记录：{num}行", "batchSearch.contractNums": "合同数量", "batchSearch.order": "序号", "batchSearch.searchContentLengthLimitTip": "最多支持100个关键词，超出不做处理", "batchSearch.inputCorrectFieldName": "请选择正确的搜索条件（需从下拉框中选择搜索条件）", "docDetail.crossPlatformSign": "跨平台签署", "docDetail.platformSign.ja": "日本签约方", "docDetail.platformSign.zh": "中国签约方", "docDetail.specialContract": "特殊合同", "docDetail.specialContractTip": "跨企业转交的特殊合同，只能查看，不要做接口调用、下载、作废、重新发送、出证，否则可能出现异常", "docDetail.tips": "提示", "docDetail.confirm": "确定", "docDetail.noNeedSign": "你当前身份代表了{name}，但该身份已完成签署或无需签署。建议你退出后以其他身份重新进入重试", "docDetail.signRole": "（签约角色）", "docDetail.signRoleTextOnly": "签约角色：", "docDetail.scanCodeSign": "扫码签字", "docDetail.other": "其他", "docDetail.shareSignLink": "分享签署链接", "docDetail.messageAndFaceVerify": "刷脸+验证码签署", "docDetail.faceSign": "刷脸签署", "docDetail.faceFirstVerifyCodeSecond": "优先刷脸，备用验证码签署", "docDetail.contractRecipient": "合同收件方", "docDetail.inputReceiver": "接收人", "docDetail.inputReceiverTip": "发件人填写的经办人姓名。仅对发件方企业（含集团子企业）可见。", "docDetail.inputReceiverNotInput": "未填写", "docDetail.personalOperateLog": "个人合同操作日志", "docDetail.recordDialog.date": "日期", "docDetail.recordDialog.user": "用户", "docDetail.recordDialog.operate": "操作", "docDetail.recordDialog.view": "查看", "docDetail.recordDialog.download": "下载", "docDetail.remarks": "备注", "docDetail.operateRecords": "操作记录", "docDetail.borrowingRecords": "借阅记录", "docDetail.currentHolder": "当前持有人", "docDetail.currentEnterprise": "当前企业", "docDetail.companyInterOperationLog": "公司内部操作日志", "docDetail.companyInterOperationLogTip": "公司内部操作日志仅对本企业可见。", "docDetail.receiverMap.sender": "合同发件人", "docDetail.receiverMap.signer": "合同接收人", "docDetail.receiverMap.ccUser": "合同抄送人", "docDetail.receiverMap.editor": "合同补全人", "docDetail.noRead": "未读", "docDetail.read": "已读", "docDetail.downloadCode": "合同下载码", "docDetail.noTagToAddHint": "还没有标签，请前往企业控制台添加", "docDetail.noSender": "您不是合同发送方，无法为该合同设置标签", "docDetail.noOpenFeature": "该合同发件方企业尚未开通设置标签功能", "docDetail.requireFieldNotAllowEmpty": "必填项不能为空", "docDetail.modifySuccess": "修改成功", "docDetail.uncategorized": "未分类", "docDetail.notAllowModifyContractType": "{type}中的合同不允许修改合同类型", "docDetail.notAllowModifyContractTypeBackup": "{type}中的合同不允许修改合同类型（备用）", "docDetail.setTag": "设置标签", "docDetail.contractTag": "合同标签", "docDetail.plsInput": "请输入", "docDetail.plsInputCompanyInternalNum": "请输入公司内部编号", "docDetail.companyInternalNum": "公司内部编号", "docDetail.none": "无", "docDetail.plsSelect": "请选择", "docDetail.modify": "修改", "docDetail.contractDetailInfo": "合同详细信息", "docDetail.contractDetailInfoTip": "合同详细信息仅对发件方企业（含集团子企业）可见。", "docDetail.viewDetail": "查看详情", "docDetail.slideContentTip.downloadFile": "下载源文件", "docDetail.slideContentTip.look": "查看", "docDetail.slideContentTip.signNotice": "签约须知", "docDetail.slideContentTip.contractAncillaryInformation": "合同附属资料", "docDetail.slideContentTip.content": "内容", "docDetail.slideContentTip.document": "文档", "docDetail.slideContentTip.compressedFile": "压缩文件", "docDetail.slideContentTip.supplementsTitle": "{account}的补充协议", "docDetail.slideContentTip.fromCurrentSupplements": "来自当前模板的补充协议：", "docDetail.slideContentTip.fromOtherSupplements": "来自其他模板的补充协议：", "docDetail.slideContentTip.supplementsContractId": "合同ID：", "docDetail.slideContentTip.supplementsContractTitle": "合同标题：", "docDetail.slideContentTip.moreSupplementsLine": "查看更多", "docDetail.slideContentTip.updateTime": "更新时间", "docDetail.slideContentTip.updateTimeTip": "合同被签署前，发件人可以更新签约须知", "docDetail.slideContentTip.updatePrivateBtn": "更新", "docDetail.boxCollection": "签约主体资料", "docDetail.downloadDepositConfirmTip.title": "您下载的签约存证页为脱敏版，经办人隐私信息已被隐去，不适用于法庭诉讼。如有诉讼需要，可联系上上签领取完整版签约存证页。", "docDetail.downloadDepositConfirmTip.hint": "提示", "docDetail.downloadDepositConfirmTip.confrim": "继续下载", "docDetail.downloadDepositConfirmTip.cancel": "取消", "docDetail.downloadTip.title": "由于合同尚未完成，您下载到的是未生效的合同预览文件。", "docDetail.downloadTip.normalVersion": "正常展示版", "docDetail.downloadTip.highLightVersion": "审批专用版：将合同中的所有“模板内容字段”高亮，方便抓取关键信息。", "docDetail.downloadTip.versionTip": "请选择需要的版本下载：", "docDetail.downloadTip.invalidTitle": "由于合同已被作废，您下载到的是未生效的合同预览文件。", "docDetail.downloadTip.hint": "提示", "docDetail.downloadTip.confirm": "确定", "docDetail.downloadTip.cancel": "取消", "docDetail.transferFail": "转交失败", "docDetail.transferSuccessGoManagePage": "转交成功，将返回合同管理页面", "docDetail.claimSign": "认领签署", "docDetail.downloadDepositPageTip": "下载签约存证页(脱敏版)", "docDetail.downloadPageTip": "下载签约存证页", "docDetail.resend": "重新发送", "docDetail.claimSuccess": "认领成功，待审批通过后即可签署", "docDetail.approved": "已审批过了", "docDetail.proxySign": "代签署", "docDetail.notPassed": "已驳回", "docDetail.approving": "审批中", "docDetail.signning": "签署中", "docDetail.notarized": "已公正", "docDetail.invalid": "已作废", "docDetail.currentFolder": "当前文件夹", "docDetail.has": "已作废", "docDetail.now": "正在被作废:", "docDetail.for": "正在作废", "docDetail.archive": "归档", "docDetail.moveToSafeBox": "移入保险柜", "docDetail.moveToSafeBoxTip": "合同移入保险柜后，只能由合同持有人（合同参与人）才能查看，主管理员也不能查看。", "docDetail.moveFromSafeBox": "移出保险柜", "docDetail.moveFromSafeBoxTip": "移出保险柜后，合同的管理员能统一查看、管理此合同", "docDetail.moveToSuccess": "移入成功", "docDetail.moveOutSuccess": "移出成功", "docDetail.deadlineForSigning": "截止签约时间", "docDetail.endFinishTime": "签约完成/签约结束时间", "docDetail.contractImportTime": "合同导入时间", "docDetail.contractSendTime": "合同发送时间", "docDetail.back": "返回", "docDetail.contractInfo": "合同信息", "docDetail.basicInfo": "基本信息", "docDetail.contractNum": "编号", "docDetail.downloadVerifyCode": "下载合同查验码", "docDetail.verifyCode": "合同查验码", "docDetail.sender": "发件方", "docDetail.personAccount": "个人账号", "docDetail.entAccount": "企业账号", "docDetail.operator": "经办人", "docDetail.signStartTime": "发起签约时间", "docDetail.signDeadline": "签约截止时间", "docDetail.contractExpireDate": "合同到期时间", "docDetail.viewInvalidContract": "查看《作废申明》", "docDetail.labelLimitEditTip": "贵司管理员已将“{name}”字段设置为不可修改。如需修改该字段值，请前往“控制台 - 业务字段管理 - 合同描述字段”页面，将其调整为“允许修改字段值”。", "docDetail.edit": "修改", "docDetail.settings": "设置", "docDetail.clear": "清除", "docDetail.from": "来源", "docDetail.folder": "文件夹", "docDetail.contractType": "合同类型", "docDetail.contractTypeBackup": "合同类型（备用）", "docDetail.reason": "理由", "docDetail.sign": "签署", "docDetail.approval": "审批", "docDetail.viewAttach": "查看附页", "docDetail.downloadContract": "下载合同", "docDetail.downloadAttach": "下载签约存证", "docDetail.print": "打印", "docDetail.certificatedTooltip": "该合同及相关证据已在杭州互联网法院司法链存证", "docDetail.needMeSign": "需要我签署", "docDetail.needMeApproval": "需要我审批", "docDetail.inApproval": "审批中", "docDetail.needOthersSign": "需要他人签署", "docDetail.signComplete": "签约完成", "docDetail.signOverdue": "逾期未签", "docDetail.rejected": "已拒签", "docDetail.revoked": "已撤回", "docDetail.contractCompleteTime": "签约完成时间", "docDetail.contractEndTime": "签约结束时间", "docDetail.reject": "拒签", "docDetail.revoke": "撤回", "docDetail.download": "下载", "docDetail.viewSignOrders": "查看签署顺序", "docDetail.viewApprovalProcess": "查看审批流程", "docDetail.viewApprovalAnnotate": "查看审批批注", "docDetail.completed": "已完成", "docDetail.cc": "抄送", "docDetail.ccer": "抄送方", "docDetail.signer": "签约方", "docDetail.signSubject": "签约主体", "docDetail.signSubjectTooltip": "发件方填写的签约主体为", "docDetail.user": "用户", "docDetail.IDNumber": "身份证号", "docDetail.state": "状态", "docDetail.time": "时间", "docDetail.notice": "提醒", "docDetail.detail": "详情", "docDetail.RealNameCertificationRequired": "被要求实名认证", "docDetail.RealNameCertificationNotRequired": "不需要实名认证", "docDetail.MustHandwrittenSignature": "必须手写签名", "docDetail.handWritingRecognition": "手写笔迹识别", "docDetail.handWritingRecognitionChangeTip1": "1. 您需拥有“查看合同”以及该合同对应模板的“发送合同”和“调整签约方要求”的权限；", "docDetail.handWritingRecognitionChangeTip2": "2. 只有当前签约主体未完成签署，您才可以更改此配置", "docDetail.privateMessage": "私信", "docDetail.attachment": "资料", "docDetail.rejectReason": "原因", "docDetail.notSigned": "未签署", "docDetail.notViewed": "未查看", "docDetail.viewed": "已查看", "docDetail.signed": "已签署", "docDetail.viewedNotSigned": "已读未签", "docDetail.notApproval": "未审批", "docDetail.remindSucceed": "提醒消息已发送", "docDetail.reviewDetails": "审批详情", "docDetail.close": "关 闭", "docDetail.entInnerOperateDetail": "企业内部操作详情", "docDetail.approve": "同意", "docDetail.disapprove": "驳回", "docDetail.applySeal": "申请用印", "docDetail.applied": "已申请", "docDetail.apply": "申请", "docDetail.toOtherSign": "转给其他人签", "docDetail.handOver": "转交", "docDetail.editor": "补全", "docDetail.approvalOpinions": "审批意见", "docDetail.useSeal": "用印", "docDetail.signature": "签名", "docDetail.use": "使用", "docDetail.date": "日期", "docDetail.fill": "填写", "docDetail.times": "次", "docDetail.place": "处", "docDetail.contractDetail": "合同明细", "docDetail.viewMore": "查看更多", "docDetail.collapse": "收起", "docDetail.WXLinkTitle": "小程序链接：", "docDetail.signLinkTitle": "网页链接：", "docDetail.signLink": "签署链接", "docDetail.saveQRCode": "保存二维码或复制链接，分享给签署方", "docDetail.saveWXCode": "保存小程序码或复制链接，分享给签署方", "docDetail.changeQRCode": "切换二维码", "docDetail.changeWXCode": "切换小程序码", "docDetail.signQRCode": "签署链接二维码", "docDetail.showlinkDec": "{senderName}给您发了{contractName}，打开链接查看 {url}", "docDetail.copyUrl": "复制链接", "docDetail.copy": "复制", "docDetail.copySucc": "复制成功", "docDetail.copyFail": "复制失败", "docDetail.certified": "已认证", "docDetail.unCertified": "未认证", "docDetail.claimed": "已认领", "docDetail.unSort": "未分类", "docDetail.signPage": "页数：{page}页", "docDetail.handWriteNotAllowed": "使用上上签系统签名", "docDetail.entSign": "签字", "docDetail.stamp": "盖章", "docDetail.stampSign": "盖章并签字", "docDetail.requireEnterIdentityAssurance": "启用经办人身份核验", "docDetail.noNeedToSign": "已无需签署", "docDetail.requestSeal": "业务核对章", "docDetail.requestSealAgree": "符合章", "docDetail.requestSealRefuse": "不符合章", "docDetail.requestSealRemark": "不符合章的备注", "docDetail.viewContentInfo": "查看合同内容字段", "docDetail.contractContentInfo": "合同内容字段", "docDetail.contentDate": "日期", "docDetail.combobox": "下拉框", "docDetail.text": "文本", "docDetail.number": "数字", "docDetail.notFilled": "此处未填写", "docDetail.radio": "单选", "docDetail.checkbox": "多选", "docDetail.filledBySender": "由发件方填写", "docDetail.filledBy": "由{writer}填写", "docDetail.empty": "结果为空", "docDetail.changeToPaperSign": "已改用纸质签署", "docDetail.templateNum": "模板编号", "docDetail.QRCode": "合同查验码", "docDetail.templateTip": "若合同没有使用模板，则模板编号默认为0", "docDetail.changeContractStatusForPaperSign": "修改状态", "docDetail.changeContractStatus": "逾期后延期", "docDetail.changeContractStatusConfirm.title": "逾期后延期", "docDetail.changeContractStatusConfirm.tip.0": "将合同状态签署中修改为“签约完成”，未签署签约方将被标注为“改用纸质签”。", "docDetail.changeContractStatusConfirm.tip.1": "合同状态变更后不可还原，请在收到盖章的纸质原件后再点击确定。 ", "docDetail.changeContractStatusConfirm.confirm": "确定", "docDetail.changeContractStatusConfirm.cancel": "取消", "docDetail.hasUsePaperSign": "(已启用纸质签)", "docDetail.signedAging": "签署时效", "docDetail.completeContractInfo": "补全合同信息", "docDetail.day": "天", "docDetail.hour": "小时", "docDetail.minute": "分", "docDetail.customApproval": "需通过自定义审批（调用上上签平台“审批合同”API）", "docDetail.accountChangeTip": "该用户账号由“{previous}”变更为“{current}”", "docDetail.byEncryptionSign": "通过加密签署方式签署", "docDetail.password": "密码", "docDetail.byTwoFactorSign": "通过二要素校验签署", "docDetail.completeByRole": "待签约角色{roleName}补全", "docDetail.completedByRole": "（由签约角色{roleName}补全）", "docDetail.completeSuccess": "补全成功", "docDetail.completeInfoFillErrorTip": "信息填写不规范，请检查页面上的报错说明（一般为红色），按提示修改后重试", "docDetail.viewContractOfSameCode": "按照\"公司内部编号\"快速查找", "docDetail.contractTitle": "合同标题：", "docDetail.contractId": "合同编号：", "docDetail.privateTip": "仅该签约方以及发件方企业可以查看", "shortcut.all": "所有合同", "shortcut.sign": "需要我签署", "shortcut.approve": "需要我审批", "chooseEntFolder.title": "选择企业履约管理系统", "chooseEntFolder.desc": "请选择需要进入的企业履约管理系统：", "syncEntFolder.title": "同步至履约文件夹", "syncEntFolder.createFolder": "新建文件夹", "exportContract.title": "导出范围", "exportContract.content1": "导出的范围为当前搜索结果中的全部合同的明细（明细字段与合同列表的表头一致）。", "exportContract.content2": "导出结果中是否要包含多文档合同中的各个子合同?", "exportContract.radioText.0": "是", "exportContract.radioText.1": "否", "exportContract.cancel": "取消", "exportContract.export": "开始导出", "scanCodeRemind.tip": "提示", "scanCodeRemind.confirm": "我知道了", "scanCodeRemind.content": "该合同含有扫码签字的签署方，请到该合同的详情页下载合同查验码后自行发送进行通知。", "scanCodeRemind.detailContent": "该签约主体是扫码签字的签署方，请您下载合同查验码后自行发送进行通知。", "crossplatformList.notice": "您有{number}份跨平台签署的合同，请点此前往处理", "crossplatformList.breadcrumb": "跨平台签署的合同", "crossplatformList.viewDetail": "查看详情", "consts.shortcutMap.myFolders": "我的履约文件夹", "consts.shortcutMap.allFolders": "所有履约文件夹", "consts.contractStatus.all": "所有状态", "consts.contractStatus.needMeSign": "等待我签署", "consts.contractStatus.needMeApproval": "等待我审批", "consts.contractStatus.inApproval": "审批中", "consts.contractStatus.needOthersSign": "需要他人签署", "consts.contractStatus.signComplete": "签约完成", "consts.contractStatus.signOverdue": "逾期未签", "consts.contractStatus.rejected": "已拒签", "consts.contractStatus.revoked": "已撤回", "consts.contractStatus.invalid": "已作废", "consts.rejectReasonList.signOperateReason": "对签署操作/校验操作有疑问，需要进一步沟通", "consts.rejectReasonList.termReason": "对合同条款/内容有疑议，需要进一步沟通", "consts.rejectReasonList.explainReason": "对合同内容不知情，请提前告知", "consts.rejectReasonList.otherReason": "其他（请填写理由）", "consts.templatePermissionMap.sendContract": "发送合同", "consts.templatePermissionMap.modifyDocument": "调整文档", "consts.templatePermissionMap.modifyReceiver": "调整签约方", "consts.templatePermissionMap.addCCReceiver": "添加抄送方", "consts.templatePermissionMap.modifySignRequirement": "调整签署要求", "consts.templatePermissionMap.dragSignLabel": "移动签署字段（含签署位置）", "consts.templatePermissionMap.modifySignLabel": "新增/删除签署字段（含签署位置）", "consts.templatePermissionMap.editable": "编辑模板", "consts.templatePermissionMap.templateDuplicate": "复制模板", "consts.templatePermissionMap.modifyDocumentFederation": "配置文档组合", "consts.templatePermissionMap.invalidStatement": "设置作废申明", "consts.templatePermissionMap.grantManage": "权限分配", "consts.templatePermissionMap.editCustomScene": "场景定制", "consts.templatePermissionMap.setDefaultValue": "设置默认值", "consts.templatePermissionMap.templateSpecialSeal": "模板专用章", "consts.templatePermissionMap.editSupplyAgree": "补充协议", "consts.templatePermissionMap.contractConfidentiality": "合同保密", "consts.templatePermissionMap.stampRecommend": "AI智能体配置", "consts.templatePermissionDesc.useTmp.name": "使用模板的权限", "consts.templatePermissionDesc.useTmp.tabName": "使用模板", "consts.templatePermissionDesc.useTmp.sendContract": "允许使用当前模板发送合同。在此权限的基础上才能授予其他使用模板权限", "consts.templatePermissionDesc.useTmp.modifyDocument": "使用模板发送合同时允许上传新的合同文档或合同附件，也允许在使用时临时删除模板中某个已上传的文档", "consts.templatePermissionDesc.useTmp.modifyReceiver": "使用模板发送合同时允许修改签约方的账号、名称，新增或删除签约方（需要配合“新增签署字段”权限一起使用）。已自动涵盖“添加抄送方”的权限，无需再授予“添加抄送方”权限。", "consts.templatePermissionDesc.useTmp.modifySignRequirement": "使用模板发送合同时允许在使用时修改签署要求", "consts.templatePermissionDesc.useTmp.dragSignLabel": "使用模板发送合同时，允许调整已设置的盖章处、签名处、业务字段的位置", "consts.templatePermissionDesc.useTmp.modifySignLabel": "使用模板发送合同时，允许为签约方新增盖章处、签名处", "consts.templatePermissionDesc.useTmp.setDefaultValue": "发送合同时，可以为签署方写入字段的初始值", "consts.templatePermissionDesc.useTmp.addCCReceiver": "使用模板发送合同时仅允许添加被抄送者的账号", "consts.templatePermissionDesc.manageTmp.name": "管理模板的权限", "consts.templatePermissionDesc.manageTmp.tabName": "管理模板", "consts.templatePermissionDesc.manageTmp.editable": "允许在合同模板列表页点击\"编辑\"按钮编辑模板，也支持删除模板、启用/停用模板、下载模板", "consts.templatePermissionDesc.manageTmp.templateDuplicate": "允许在合同模板列表页点击“复制”按钮复制模板，动态模板暂不支持", "consts.templatePermissionDesc.manageTmp.modifyDocumentFederation": "允许在模板文档列表页新增、删除、修改文档组合", "consts.templatePermissionDesc.manageTmp.modifySceneConfig": "允许在模板详情页配置场景定制项", "consts.templatePermissionDesc.manageTmp.invalidStatement": "如何废除签署完成的模板合同", "consts.templatePermissionDesc.manageTmp.specialSealConfig": "为模板合同的签署人指定印章图案", "consts.templatePermissionDesc.manageTmp.editSupplyAgree": "设置当前模板的关联模板，可让关联模板发送给相同签约方的合同，成为当前模板的合同的补充协议", "consts.templatePermissionDesc.manageTmp.contractConfidentiality": "可配置本集团/企业的合同参与人（发件、签署、审批、补全）自动转交给某个固定账号", "consts.templatePermissionDesc.manageTmp.stampRecommend": "允许启用AI智能体。可在电子签流程特定环节由某个AI智能体代替人工操作。", "consts.templatePermissionDesc.manageGrant.name": "管理权限的权限", "consts.templatePermissionDesc.manageGrant.tabName": "管理权限", "consts.templatePermissionDesc.manageGrant.grantManage": "允许在合同模板列表页点击“授权成员”或“授权角色”将模板授权给其他人", "consts.contractAlias.doc": "文件", "consts.contractAlias.letter": "询征函", "consts.contractAlias.proof": "授权书", "consts.contractAlias.contract": "合同", "consts.contractAlias.agreement": "同意书", "consts.contractAlias.service_report": "工单", "consts.contractStatusCascader.inApproval": "审批中", "consts.contractStatusCascader.inSigning": "签署中", "consts.contractStatusCascader.isCompleted": "已完成", "consts.contractStatusCascader.isCancelled": "已取消", "consts.contractStatusCascader.revokeCancel": "合同已撤回", "consts.contractStatusCascader.overDue": "逾期未签", "consts.contractStatusCascader.sendApprovalNotPassed": "审批被驳回", "consts.contractStatusCascader.reject": "合同已拒签", "consts.contractStatusCascader.invalid": "已作废", "consts.entFolderPermissionMap.contractBorrow": "合同借阅", "consts.entFolderPermissionMap.borrowApproval": "修改借阅审批人", "consts.entFolderPermissionMap.viewList": "查看列表明细", "consts.entFolderPermissionMap.downloadList": "导出列表明细", "consts.entFolderPermissionMap.viewContract": "查看履约详情页", "consts.entFolderPermissionMap.downloadContract": "下载合同", "consts.entFolderPermissionMap.invalidContract": "作废合同", "consts.entFolderPermissionMap.resendContract": "重新发送合同", "consts.entFolderPermissionMap.setTag": "设置标签", "consts.entFolderPermissionMap.distriButtonPermission": "转交及权限分配", "consts.entFolderPermissionMap.syncHronize": "个人文件夹同步", "consts.entFolderPermissionMap.remove": "删除合同", "consts.entFolderPermissionMap.group": "合同分组", "consts.entFolderPermissionMap.edit": "修改文件夹", "consts.entFolderPermissionMap.folderManageMaterial": "管理履约材料", "consts.entFolderPermissionMap.fulfillMaterialTip": "可在履约详情页设置标签，可上传或删除履约过程中产生的文件", "consts.crossFormHeader.contractTitle": "合同标题", "consts.crossFormHeader.sender": "发送方", "consts.crossFormHeader.receiver": "接收方", "consts.crossFormHeader.contractStatus": "合同状态", "consts.crossFormHeader.contractId": "合同编号", "consts.crossFormHeader.sendTime": "发送日期", "consts.crossFormHeader.expireTime": "合同到期日", "consts.crossFormHeader.signDeadlineTime": "签约截止时间", "consts.crossFormHeader.finishTime": "签约完成时间", "hybridBusiness.isCheckingNet": "正在检查混合云网络环境", "mixin.createSuccessful": "创建成功", "mixin.setLabel": "设置标签", "recoverSpecialSeal.title": "印章无法使用", "recoverSpecialSeal.description1": "发件方要求您需使用该印章才能签署合同，但贵公司已删除该印章。为确保签署顺利进行，请向管理员恢复该印章。", "recoverSpecialSeal.description2": "如果该印章确实不合适被继续使用，可联系发件方修改对印章图案的要求后，再签署合同。", "recoverSpecialSeal.postRecover": "申请恢复印章", "recoverSpecialSeal.note": "点击后管理员将收到恢复印章申请的短信/邮件，同时在印章管理页面时也能看到申请。", "recoverSpecialSeal.requestSend": "恢复申请提交成功", "certificationRenewalDialog.renewalTitle": "数字证书续期", "certificationRenewalDialog.renewalTip": "您的证书已过期，为避免文件签署无法正常进行，请及时续期", "certificationRenewalDialog.previousIdentity": "持有证书的主体：", "certificationRenewalDialog.previousCA": "原证书颁发机构：", "certificationRenewalDialog.previousExpiryDate": "原证书有效期：", "certificationRenewalDialog.previousId": "原证书序列号：", "certificationRenewalDialog.renewal": "同意续期", "pdf.previewFail": "文件预览失败", "pdf.pager": "第{x}页，共{y}页", "pdf.parseFailed": "解析pdf文件失败，请点击“确定”重试", "pdf.confirm": "确定", "tagManage.title": "设置标签", "importOffLineDoc.importDoc": "导入合同", "importOffLineDoc.step0Title": "第一步：确认导入企业名称", "importOffLineDoc.step1Title": "第二步：上传Excel", "importOffLineDoc.step2Title": "第三步：上传合同文件", "importOffLineDoc.step1Info": "请先下载Excel模板，填写完成后再导入,合同数量不超过1000。", "importOffLineDoc.next": "下一步", "importOffLineDoc.entName": "企业名称", "importOffLineDoc.archiveFolder": "归档文件夹", "importOffLineDoc.downloadExcel": "下载Excel", "importOffLineDoc.uploadExcel": "上传Excel", "importOffLineDoc.reUploadExcel": "重新上传", "importOffLineDoc.step2Info.0": "1. 合同文件只能是PDF或图片；", "importOffLineDoc.step2Info.1": "2. 所有合同文件放置在一个文件夹后，将文件夹压缩为zip（不超过150M）；", "importOffLineDoc.step2Info.2": "3. 文件名称包含文件后缀名（如.pdf）需要与第二步中的Excel里填写的文件名称一一对应；", "importOffLineDoc.uploadZip": "点击上传Zip", "importOffLineDoc.reUploadZip": "重新上传Zip", "importOffLineDoc.done": "确定", "importOffLineDoc.back": "返回", "importOffLineDoc.contractTitle": "合同名称", "importOffLineDoc.singerAccount": "签署人账号", "importOffLineDoc.singerName": "签署人名称", "importOffLineDoc.uploadSucTip": "上传成功，点击\"确定\"按钮开始导入", "importOffLineDoc.outbox": "发件箱", "importOffLineDoc.fileLessThan": "请上传小于{num}M的文件", "importOffLineDoc.fileTypeValid": "只能上传{type}格式的文件!", "download.contactGetDownloadCodeTip": "请联系合同发起方获取下载码，或尝试登录本企业业务系统下载。", "download.downloadCode": "下载码", "download.hint": "提示", "download.download": "下载", "download.plsInput": "请输入", "download.plsInputDownloadCode": "请输入下载码", "download.downloadCodeError": "下载码错误", "download.noAuthDownload": "您需要完成个人实名认证或者保持实名信息一致才能下载合同。", "download.noAuthView": "您需要完成个人实名认证或者保持实名信息一致才能查看合同详情。", "download.allFiles": "全部文件", "download.cancel": "取 消", "download.plsSelectFiles": "请先选择文件", "download.publicCloudDownloadTip": "要下载的合同中包含签署人上传的其他资料，是否随合同一起下载？", "download.hybridCloudDownloadTip": "要下载的合同中包含签署人上传的其他资料。", "download.sameTimeDownloadAttachTip": "同时下载合同附属资料", "download.downloadContract": "下载合同", "download.downloadAttach": "下载合同附属资料", "transfer.list1": "列表1", "transfer.list2": "列表2", "transfer.maxSelectNum": "已超出{maxLength}个字段的数量上限，请减少{balance}个字段", "poperCascader.plsSelect": "请选择", "poperCascader.person": "人", "poperCascader.selectNumTip": "已选择{A}/{B}个{C}", "poperCascader.allSelect": "全选", "approvalDetail.submitter": "提交人", "approvalDetail.signatory": "签署人", "approvalDetail.reviewSchedule": "审批进度", "approvalDetail.downloadFile": "下载源文件", "approvalDetail.content": "内容", "approvalDetail.document": "文档", "approvalDialog.stepTip.0": "请为不同的合同类型，设置相应审批流程", "approvalDialog.stepTip.1": "请为各审批流，填写相应的内容", "approvalDialog.back": "返回", "approvalDialog.next.0": "下一步", "approvalDialog.next.1": "提交审批", "approvalDialog.ourApprovalBeforeSign": "我方签署合同前的审批流", "approvalDialog.chooseApprover": "选择审批人：", "approvalDialog.completeApprovalFlow": "您提交的审批流程不完整，请补全后重新提交", "approvalDialog.completeTargetEntApprovalFlow": "您提交的{entName}审批流程不完整，请补全后重新提交", "approvalDialog.viewPrivateLetter": "查看私信", "approvalDialog.addPrivateLetter": "添加私信", "approvalDialog.useEntFlow": "使用{name}的审批流", "approvalDialog.beforeSendApprove": "发送前审批", "approvalDialog.beforeSignApprove": "签署前审批", "approvalDialog.beforeSignApproveTip": "如果签约方不包含本企业/本集团子企业则不触发签署前审批", "approvalDialog.contractTypeFlow": "为合同类型{contractTypeName}设置审批流：", "approvalDialog.errorTip": "请先选择审批流", "approvalDialog.customApproval": "最终生效的{name}为自定义审批，需在其他系统中审批通过后，合同才能发送成功。", "approvalDialog.customApprovalDesc": "注：贵司需对接使用上上签平台API，否则审批状态将无法消除。", "approvalDialog.useGroupApproval": "统一使用集团总公司的审批", "approvalDialog.setApprovalIndividual": "各公司灵活配置审批", "approvalDialog.errorTipWithEntName": "请先选择{entName}的审批流", "approvalDialog.noWorkflow": "该企业发送的合同无需审批", "cancelContract.confirm": "确认", "cancelContract.selectRejectReason": "请选择拒签理由", "cancelContract.reasonWriteTip": "请填写拒签理由", "cancelContract.refuseReasonOther": "更多拒签理由（可不填） | 更多拒签理由（必填）", "cancelContract.inputRejectReason": "填写拒签理由有助于对方了解你的问题，加快合同流程", "cancelContract.reject": "拒签", "cancelContract.pwdWrong": "签约密码错误,你还可以输入2/1次,是否", "cancelContract.forgetPwd": "忘记密码", "cancelContract.pwdLocked": " 签约密码已被锁定，3小时后自动解锁，你也可以通过", "cancelContract.retrievePwd": "找回密码", "cancelContract.unlock": "来立即解锁", "cancelContract.inputReason": "请输入{type}原因(可不填)", "cancelContract.revokeTips": "撤回后合同接收方不能查看合同内容，但可以查看撤回原因。| 如果接收方尚未收到合同（如合同尚未通过发送前审批，或顺序签署但尚未轮到签署）则将完全看不到此合同。", "cancelContract.revokeReasons": "合同内容需做修改 | 接收人账号需做修改 | 签署人无需接收此合同", "cancelContract.revokeOtherReason": "其他", "cancelContract.rejectTips": "拒签后将无法签署此合同", "cancelContract.signPwd": "签约密码", "cancelContract.inputPwd6": "请输入6位签约密码", "cancelContract.inputPwd": "请输入签约密码", "cancelContract.inputNumber6": "请输入6位数字", "cancelContract.mail": "邮箱", "cancelContract.phone": "手机号", "cancelContract.verify": "验证码", "cancelContract.mailVerify": "验证码", "cancelContract.otherNotice.1": "一直收不到短信？试试", "cancelContract.otherNotice.2": "语音验证码", "cancelContract.otherNotice.3": "或", "cancelContract.otherNotice.4": "短信验证码", "cancelContract.otherNotice.5": "邮箱验证码", "cancelContract.sendSucc": "发送成功", "cancelContract.sendInternalErr": "发送时间间隔过短", "cancelContract.getVerifyCode": "请先获取验证码", "cancelContract.succ": "{type}成功", "cancelContract.refuseConfirmTip": "您以\"{reason}\"理由拒绝签署，是否继续？确定后将不可再次签署。", "cancelContract.waitAndThink": "我再想想", "contractMove.newFolder": "新建文件夹", "contractMove.plsInput": "请输入", "contractMove.archive": "归档", "contractMove.noFolder": "尚无文件夹", "contractMove.confirm": "确认", "contractMove.cancel": "取 消", "contractMove.folderPlaceholder": "请输入文件夹名称", "contractMove.fileSucc": "归档成功", "contractMove.noFunctionLimit": "贵司未开通该功能，可联系客服开通。", "contractMove.hasMoveTip": "已归档的合同再次归档，合同将从原文件夹中移出。", "contractMove.howToViewMovedStatus": "如何查看合同是否已归档", "contractsTransferDialog.originOwner": "原持有人", "contractsTransferDialog.contractTransfer": "合同转交", "contractsTransferDialog.newOwner": "新持有人", "contractsTransferDialog.selectedOwner": "已选转交关系", "contractsTransferDialog.searchTip": "支持输入账号/姓名搜索", "contractsTransferDialog.confirm": "确定", "contractsTransferDialog.chooseNewOwner": "请选择新持有人", "contractsTransferDialog.transferRelationship": "请至少添加一个转交关系", "contractsTransferDialog.notCliam": "合同未被认领", "contractsTransferDialog.bizTransfer": "跨业务线转交", "contractsTransferDialog.innerTransfer": "业务线内部转交", "contractsTransferDialog.chooseMultiLine": "选择业务线", "contractsTransferDialog.chooseAccount": "选择账号", "contractsTransferDialog.displayAccount": "仅展示前50个账号", "contractsTransferDialog.transferRoles": "您将转交以下角色的合同：", "contractsTransferDialog.tip": "提示", "contractsTransferDialog.continue": "继续", "contractsTransferDialog.know": "我知道了", "contractsTransferDialog.cancel": "取消", "linkContract.reason.reSend": "重新发送的合同", "linkContract.reason.beReSend": "被重新发送的合同", "linkContract.reason.invalid": "申明作废的合同", "linkContract.reason.beInvalid": "被作废的合同", "linkContract.reason.paperSign": "纸质扫描件对应的原合同", "linkContract.reason.bePaperSign": "纸质扫描件", "linkContract.reason.manualLinked": "手动关联", "linkContract.contractTitle": "合同名称", "linkContract.contractNo": "合同编号", "linkContract.linkedDetail": "关联详情", "linkContract.noPermissionTip": "您无权限查看该关联合同", "linkContract.noCanLinked": "只可以取消手动关联的合同", "linkContract.title": "关联合同", "linkContract.connectMore": "关联到", "linkContract.placeholder": "请输入主合同编号", "linkContract.contractNoLengthTip": "合同编号需为19位数字", "linkContract.revoke": "合同已撤回", "linkContract.overdue": "逾期未签", "linkContract.approvalNotPassed": "审批被驳回", "linkContract.reject": "合同已拒签", "linkContract.invalid": "合同已作废", "linkContract.signing": "签署中", "linkContract.complete": "已完成", "linkContract.approvaling": "审批中", "linkContract.disconnect": "解除关联", "linkContract.connectSuccess": "关联成功", "linkContract.connectExist": "关联已存在", "linkContract.disconnectSuccess": "解除关联成功", "linkContract.connectLimit": "关联合同数量上限为100份", "linkContract.noFunctionLimit": "贵司未开通该功能，可联系客服开通。", "linkContract.disconnectTip.0": "确定解除关联合同", "linkContract.disconnectTip.1": "吗？", "linkContract.submit": "确定", "linkContract.cancel": "取消", "linkContract.entAccount": "企业账号", "linkContract.personAccount": "个人账号", "linkContract.whetherMasterContract": "是否是主合同", "qrCodeTab.pleaseScanToHandleWrite": "请用微信或者手机浏览器扫码，在移动设备上手写签名", "qrCodeTab.save": "保存", "selectSignType.chooseSignType": "选择签约方式", "selectSignType.useTemplate": "使用模板", "selectSignType.useLocalFile": "上传本地文件", "signValidation.VerCodeVerify": "验证码校验", "signValidation.QrCodeVerify": "二维码校验", "signValidation.verifyTip": "上上签正在调用您的安全数字证书，您正在安全签约环境中，请放心签署！", "signValidation.verifyAllTip": "上上签正在调用企业数字证书和您的个人数字证书，您正在安全签约环境中，请放心签署！", "signValidation.appScanVerify": "上上签APP扫码校验", "signValidation.downloadBSApp": "下载上上签APP", "signValidation.scanned": "扫码成功", "signValidation.confirmInBSApp": "请在上上签APP中确认签署", "signValidation.qrCodeExpired": "二维码已失效，请刷新重试", "signValidation.appKey": "APP安全校验", "signValidation.goToScan": "去扫码", "signValidation.signSuc": "签署成功", "signValidation.if": "，是否", "signValidation.forgetPassword": "忘记密码", "signValidation.signPsw": "签约密码", "signValidation.signPwdType": "请输入6位数字", "signValidation.email": "邮箱", "signValidation.phoneNumber": "手机号", "signValidation.setNotificationInUserCenter": "请先到用户中心设置通知方式", "signValidation.mailVerificationCode": "验证码", "signValidation.verificationCode": "验证码", "signValidation.msgTip": "一直收不到短信？试试", "signValidation.voiceVerCode": "语音验证码", "signValidation.or": "或", "signValidation.SMSVerCode": "短信验证码", "signValidation.emailVerCode": "邮箱验证码", "signValidation.doNotWantUseVerCode": "不想用验证码", "signValidation.try": "试试", "signValidation.goToFaceVerify": "去刷脸", "signValidation.submit": "确定", "signValidation.SentSuccessfully": "发送成功！", "signValidation.intervalTip": "发送时间间隔过短", "signValidation.signVerification": "签署", "signValidation.appliedSeal": "用印申请已提交", "signValidation.operationCompleted": "操作完成", "signValidation.firstStepTip1": "第一步：刷脸签署", "signValidation.firstStepTip2": "（用于签署有刷脸签署要求的合同）", "signValidation.secondStepTip1": "第二步：验证码签署", "signValidation.secondStepTip2": "（用于签署有验证码签署要求的合同）", "signValidation.useVerCode": "使用验证码校验", "signValidation.useSignPsw": "使用签约密码校验", "signValidation.setSignPsw": "设置签约密码校验", "signValidation.inputVerifyCodeTip": "请输入验证码", "signValidation.tip": "设置完成后，默认优先签约密码，如需修改可登录上上签电子签约平台在「用户中心」或者登录上上签小程序在「账号管理」进行配置调整。", "signValidation.saveAndReturnSign": "保存并返回签署", "signValidation.signPwdRemind": "经常签署，验证码等待太久，试试签约密码?", "signValidation.toSetSignPwd": "去设置签约密码", "switchSubject.chooseIdentity": "选择身份", "switchSubject.chooseMultiLine": "选择业务线", "switchSubject.confirm": "确认", "switchSubject.cancel": "取 消", "switchSubject.switchIdentity": "切换企业以操作模板", "switchSubject.switchIdentityTips": "以下账号拥有该模板的操作权限，请选择切换：", "unverifyConfirmDialog.tip": "提示", "unverifyConfirmDialog.isGroupProxyAuth": "本企业目前认证状态为集团代认证，合同收件人将无法识别您的身份，建议您先补充实名认证材料。", "unverifyConfirmDialog.unverify": "还未实名认证，合同收件人将无法识别您的身份，建议您先进行实名认证。", "unverifyConfirmDialog.goAuthenticate": "去认证", "unverifyConfirmDialog.enterprise": "企业", "unverifyConfirmDialog.theEnterprise": "该企业", "unverifyConfirmDialog.you": "您", "contractQrCodeDialog.selectVerifyCode": "选择查验码", "contractQrCodeDialog.viewVerifyCode": "查看样式", "contractQrCodeDialog.preview": "预览", "contractQrCodeDialog.msg.selectVerifyCode": "请先选择查验码", "contractQrCodeDialog.msg.success": "更换成功", "addAttachmentConfig.dialogTitle": "配置附件字段", "addAttachmentConfig.fieldInd": "序号", "addAttachmentConfig.fieldName": "字段名称", "addAttachmentConfig.fieldType": "字段内容", "addAttachmentConfig.fieldRequire": "是否必填", "addAttachmentConfig.lengthLimit": "不超过20个字", "addAttachmentConfig.type": "Pdf、Word、Excel及图片格式", "addAttachmentConfig.notRequire": "选填", "addAttachmentConfig.addField": "新增字段", "addAttachmentConfig.submit": "保存", "addAttachmentConfig.cancel": "取消", "addAttachmentConfig.requireName": "还有名称未填写", "addAttachmentConfig.saveSucc": "保存成功", "authInfoChange.title": "实名信息变更检测", "authInfoChange.confirm": "确认", "authInfoChange.changeAuth": "更新实名", "authInfoChange.notifyAdmin": "通知管理员", "authInfoChange.notifySuccess": "通知成功", "authInfoChange.operateSuccess": "操作成功", "authInfoChange.warningTip.tip1": "经审查，贵司“{entName}”在上上签的企业实名信息{oldAuthInfo}与最新的工商备案信息{newAuthInfo}不一致。", "authInfoChange.warningTip.tip2": "为保证您签署的电子合同合规性和效力，请使用最新的企业信息进行重新实名。", "authInfoChange.warningTip.tip3": "此操作不会驳回您当前的企业信息。", "authInfoChange.suggestTip.tip1": "如您企业为集团架构，请联系您的专属CSM、或者拨打上上签署客服热线400-993-6665办助您完成实名认证信息的更新。更新后，方可继续签署。", "authInfoChange.suggestTip.tip2": "点击【通知管理员{adminInfo}】，", "authInfoChange.suggestTip.tip3": "可以立即发送通知给管理员，引导管理员去重新实名。您也可线下通知，及时推动业务开展。", "excelScale.setExcelScale": "设置Excel缩放", "excelScale.info": "开启该功能，Excel所有列会被缩放至一页展示。", "excelScale.on": "开启缩放功能", "excelScale.line.tip1": "开启功能将对模板后续所有上传文档和附件生效；", "excelScale.line.tip2": "使用模板时会继承模板设置", "excelScale.saveSucc": "保存成功", "excelScale.saveFail": "保存失败", "internalSignConfig.configTitle.0": "仅用于对内文件签字场景设置", "internalSignConfig.configTitle.1": "已设置为“仅用于内部文件签字场景”", "internalSignConfig.title": "仅用于对内文件签字场景设置", "internalSignConfig.tip.0": "该功能不适用于正式合同（如劳动合同）开启该功能后，模板的使用方式将受到如下限制：", "internalSignConfig.tip.1": "· 只能添加企业内部的成员账号作为签署人", "internalSignConfig.tip.2": "· 签署人只能使用企业签字的方式", "internalSignConfig.checkboxInfo.0": "允许签字人不实名签署（在法律效力上比不上实名签署）。不勾选则必须实名。", "internalSignConfig.checkboxInfo.1": "允许签字人在签署合同时无需签署校验即可完成签署。不勾选则必须通过验证码（或签约密码、刷脸等等方式）校验才能完成签署。", "internalSignConfig.checkboxInfo.2": "允许签字人在签署合同时无需手写签名（可以使用默认签名）。不勾选则每次签署必须手写签。", "internalSignConfig.setAliasTip": "设置合同对外的“合同别名”", "internalSignConfig.setAliasInfo": "进入模板详情页—场景定制中，可以设置对外的“合同别名”。", "permissionHeader.hi": "你好", "permissionHeader.exit": "退出", "permissionHeader.help": "帮助", "permissionHeader.hotline": "服务热线", "paperSign.title": "使用纸质方式签署", "paperSign.stepText.0": "下一步", "paperSign.stepText.1": "确认纸质签", "paperSign.stepText.2": "确定", "paperSign.needUploadFile": "请先上传扫描件", "paperSign.uploadError": "上传失败", "paperSign.cancel": "取消", "paperSign.downloadPaperFile": "获取纸质签文件", "paperSign.step0.title": "您需要先下载打印合同，加盖物理章后，邮寄给发件方。", "paperSign.step0.address": "邮寄地址：", "paperSign.step0.contactName": "接收人姓名：", "paperSign.step0.contactPhone": "接收人联系方式：", "paperSign.step0.defaultValue": "请通过线下方式向发件方索取", "paperSign.step1.title0": "第一步：下载&打印纸质合同", "paperSign.step1.title0Desc.0": "下载打印的合同应包含已签署的电子章的图案。请", "paperSign.step1.title0Desc.1": "获取纸质签文件。", "paperSign.step1.title1": "第二步：加盖印章", "paperSign.step1.title1Desc": "在纸质合同上加盖合同有效的公司印章。", "paperSign.step1.title2.0": "第三步：", "paperSign.step1.title2.1": "上传扫描件，", "paperSign.step1.title2.2": "填写验证码，完成纸质签", "paperSign.step1.title2Desc.0": "将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，", "paperSign.step1.title2Desc.1": "再填写验证码后，即完成签署。电子合同中不展示您的印章图案，但会记录您此次操作过程。", "paperSign.step2.title.0": "将纸质合同扫描件（PDF格式文件）上传", "paperSign.step2.title.1": "请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。", "paperSign.step2.uploadFile": "上传扫描件", "paperSign.step2.getCodeVerify": "获取合同签署校验", "paperSign.step2.isUploading": "上传中...", "paperSign.operationCompleted": "操作完成", "changeSignDeadline.title": "修改合同状态", "changeSignDeadline.desc": "点击确定后，将有如下变化：", "changeSignDeadline.changeTime": "将合同状态“逾期未签”修改为“签署中”，并且合同签约截止时间更改为： {signDeadline}", "changeSignDeadline.changeTimeTip": "为签署人设置的“签署时效”将不再生效", "changeSignDeadline.choose": "您可选择：", "changeSignDeadline.checkSignNotice": "向未签署合同的账号推送签约提醒", "changeSignDeadline.confirmChange": "是否确认修改合同状态？ ", "changeSignDeadline.cancel": "取消", "changeSignDeadline.confirm": "确定", "changeSignDeadline.signDeadlineTip": "每份合同最多只能延期3次", "addEntFolder.title": "新建履约文件夹", "addEntFolder.ent": "所属企业", "addEntFolder.entHolder": "请选择", "addEntFolder.folderName": "履约文件夹名称", "addEntFolder.folderNameHolder": "不超过20个字，需唯一，必填", "addEntFolder.mark": "备注", "addEntFolder.markHolder": "不超过100字，选填", "addEntFolder.cancel": "取消", "addEntFolder.confirm": "确定", "addEntFolder.folderNameTip": "履约文件夹名称不能为空！", "entContractDownload.failTitle": "以下文档下载失败", "entContractDownload.documentId": "文档ID", "entContractDownload.documentTitle": "文档名称", "entContractDownload.failReason": "失败原因", "fileLimit.fileLessThan": "请上传小于{num}M的文件", "fileLimit.usePdf": "上传时请使用PDF文件或图片", "fileLimit.beExcel": "请上传Excel文件", "fileLimit.beAttachmentFile": "请上传Pdf、Word、Excel或图片", "fileLimit.beZip": "请上传Zip，7z压缩文件", "fileLimit.fileNameMoreThan": "文件名称长度超过{num}，已为您自动截取", "regs.entNameMaxLength": "企业名称不能超过60个字符", "unPermissionRemind.title": "没有权限", "unPermissionRemind.content.noAuth.1": "发件方指定的签署企业名称为：{receiverEntName}", "unPermissionRemind.content.noAuth.2": "与您账号下已实名的企业名称，均不一致：", "unPermissionRemind.content.noAuth.3": "1.请根据页面指引，完成对{receiverEntName}的认证；", "unPermissionRemind.content.noAuth.4": "2.如已认证，请检查企业名称，是否填写有误？若确实不一致，请联系发件方或上上签客服确认。", "unPermissionRemind.content.noJoin": "该文件由{senderEntName}发给{receiverEntName}，需要使用企业电子印章进行签署。查询到您未加入企业系统，无签署权限，需要申请。", "unPermissionRemind.content.noView": "该文件由{senderEntName}发给{receiverEntName}，查询到您无权限查看，需要申请。", "unPermissionRemind.content.noSign": "该文件由{senderEntName}发给{receiverEntName}，需要使用企业电子印章进行签署。查询到您无签署权限，需要申请。", "unPermissionRemind.transferTip": "您也可以转交给具有印章权限的人员进行签署，转交签署成功后我们将会告知给您。", "unPermissionRemind.transferBtn": "去转交签署 >>", "unPermissionRemind.giveOtherAuthTip": "您也可以转交给其他管理人员进行认证。", "unPermissionRemind.giveOtherAuthBtn": "转他人认证 >>", "unPermissionRemind.noAuthPaperSign": "当您无法完成企业认证时，也可选择下载打印，进行纸质签署。", "unPermissionRemind.otherPaperSign": "您也可以选择下载合同文档并打印，交由企业印章负责人线下盖章签署。", "unPermissionRemind.paperSignBtn": "转纸质签署 >>", "unPermissionRemind.applyBtn": "去申请", "unPermissionRemind.authBtn": "去认证", "unPermissionRemind.returnBtn": "返回", "unPermissionRemind.transferContract": "移交", "unPermissionRemind.cantTransferTip": "收件方企业存在多业务线，暂不支持移交", "applyPermission.transferAdmin": "转交主管理员", "applyPermission.transferAdminTip": "转交主管理员之后，将为您保留以下权限，您也可以选择更多权限。", "applyPermission.giveOtherAuth": "转他人认证", "applyPermission.giveOtherAuthTip": "转交他人认证的过程中，同时为您申请以下权限，以便顺利完成合同签署。您也可以根据实际需要选择更多权限。", "applyPermission.rightApply": "权限申请", "applyPermission.adminName": "系统主管理员姓名", "applyPermission.account": "账号", "applyPermission.applyUserName": "申请人姓名", "applyPermission.senderNamePlaceholder": "请输入您的姓名", "applyPermission.senderAccountPlaceholder": "请输入您的账号", "applyPermission.specialSeal": "模板专用章", "applyPermission.electronicSeal": "电子公章", "applyPermission.phoneOrEmail": "手机号/邮箱", "applyPermission.applyRight": "申请权限", "applyPermission.ent": "企业", "applyPermission.receiverAccountPlaceholder": "请输入被转交人账号", "applyPermission.sender": "转交人", "applyPermission.yourName": "您的姓名", "applyPermission.applyName": "申请人姓名", "applyPermission.moreRight": "更多权限", "applyPermission.confirmAdminTip": "我是企业管理人员，我要", "applyPermission.confirmAdminBtn": "申请成为主管理员", "applyPermission.otherWayApply": "其他方式申请", "applyPermission.applyToAdmin": "向主管理员申请", "applyPermission.sendBtn": "发送通知", "applyPermission.applyRightTip": "当您不认识主管理员时，可选择其他方式申请，获取企业其它管理人员或者印章管理人员的授权，即可查看{contractAlias}。", "applyPermissionResult.title": "通知成功，等待处理", "applyPermissionResult.content11": "主管理员（{name}，{account}）审批完成后，您将收到短信通知。", "applyPermissionResult.content12": "主管理员（{account}）审批完成后，您将收到短信通知。", "applyPermissionResult.content2": "被转交人{account}收到通知后，将会对您的转交请求进行处理。处理结果我们将以短信形式告知给您。", "applyPermissionResult.returnBtn": "返回", "applyPermissionResult.bindingError": "账号暂未单点绑定成功，无法跳转，请等待审批完成", "morePermissionDialog.title": "更多权限", "morePermissionDialog.confirmBtn": "确定", "morePermissionDialog.cancel": "取消", "morePermissionDialog.viewContract": "查看企业{contract<PERSON><PERSON><PERSON>}", "morePermissionDialog.nowContract": "当前{contract<PERSON><PERSON><PERSON>}", "morePermissionDialog.nowSenderProxyContract": "当前发件方发送的{contractAlias}", "morePermissionDialog.allSenderProxyContract": "所有{contract<PERSON><PERSON><PERSON>}", "morePermissionDialog.proxyContractTip": "申请查看权限的{contractAlias}包括未指定签署人账号的待认领{contractAlias}或者使用其他账号认领指定签署账号的{contactAlias}。", "morePermissionDialog.signContract": "签署企业{contract<PERSON><PERSON><PERSON>}（电子公章）", "morePermissionDialog.nowSenderContract": "当前发件方发送的{contractAlias}", "morePermissionDialog.allSenderContract": "所有{contract<PERSON><PERSON><PERSON>}", "morePermissionDialog.noApplySignRight": "我不签署，不需要章", "morePermissionDialog.canSignTip": "在可查看范围内允许签署。", "morePermissionDialog.allTip": "说明：当前发件方包含该企业及其集团和子公司、业务线。", "morePermissionDialog.error.1": "请输入被转交人账号", "morePermissionDialog.error.2": "请输入正确的账号！", "morePermissionDialog.error.3": "请输入申请人姓名", "morePermissionDialog.error.4": "请您先向主管理员申请权限，若主管理员未响应，可以使用“其他方式申请”的通道", "applyToBeAdminDialog.title": "请输入正确的账号!", "applyToBeAdminDialog.content1": "您正在申诉成为{entName}企业的系统主管理员，主管理员主要职责与权限有：", "applyToBeAdminDialog.content2": "1、企业印章使用与分配 | 2、企业成员管理 | 3、企业合同管理", "applyToBeAdminDialog.tip": "更多主管理员功能可在申诉成功后，登录电脑端上上签平台 http://ent.bestsign.cn查看。", "applyToBeAdminDialog.footer": "系统主管理员通常由企业法定代表人、财务管理者、法务管理者、IT部门管理者或企业业务负责人等角色担任，以确保职责的有效履行。", "applyToBeAdminDialog.confirm": "去申诉", "applyToBeAdminDialog.cancel": "取消", "infoProtectDialog.userAuth": "使用刷脸服务须同意", "infoProtectDialog.titleWithSeperator": "《上上签如何保护您的个人信息》", "infoProtectDialog.title": "上上签如何保护您的个人信息", "infoProtectDialog.auth": "实名认证", "infoProtectDialog.faceSign": "刷脸签署", "infoProtectDialog.contentDesp": "您提交个人身份等信息（以下简称\"个人信息\"）时已经充分知悉并同意：", "infoProtectDialog.detailTip1": "（1）为了完成您的{title}，您已经授权您的企业自行或委托上上签将您的个人信息提交给为实现电子签约之目的而提供相应服务的其他主体（比如CA机构、公证处等）；", "infoProtectDialog.detailTip2": "（2）除（1）授权内容外，您单独同意提交您的人脸信息用于本次{title}的意愿性认证（即刷脸签署校验），并同意上上签仅为提供电子签约服务以及后续出证的需要，审核、储存、调取、共享等方式处理您的人脸信息。若您不同意本条所述内容，您应立即停止提交您的人脸信息，并选择其它认证方式；", "infoProtectDialog.detailTip3": "（3）除了前述（1）（2）以及法律规定的情形外，上上签未经您的授权不会主动将您的个人信息提交给任何第三人。", "infoProtectDialog.know": "知道了", "faceSign.faceFailed": "非常抱歉，您的人脸比对失败", "faceSign.verifyTry": "请核实身份信息后重试", "faceSign.upSignReq": "今天的人脸比对次数已达到上限，请明天重试或联系合同发起者修改签署要求", "faceSign.reqFace": "发件人要求你进行刷脸校验", "faceSign.signAfterFace": "刷脸通过后即可完成合同签署", "faceSign.qrcodeInvalid": "二维码信息已过期，请刷新", "faceSign.nameIs": "姓名为", "faceSign.IDNumIs": "身份证号为", "faceSign.retry": "重试", "faceSign.pleaseScanToSign": "请用支付宝或微信扫一扫签署", "faceSign.pleaseScanAliPay": "请使用支付宝app扫描二维码签署", "faceSign.pleaseScanWeChat": "请使用微信app扫描二维码签署", "entDocList.title": "企业履约管理", "entDocList.goBackDocList": "返回到我的合同管理", "entDocList.createEntFolder": "创建履约文件夹", "entDocList.columnFolderName": "履约文件夹名称", "entDocList.columnCreateUserName": "创建人", "entDocList.columnRemark": "备注", "entDocList.columnPermission": "权限", "entDocList.permissionManage": "权限管理", "entDocList.emptyText": "暂无数据", "entDocList.operate": "操作", "entDocList.view": "查看", "entDocList.noPermission": "您没有查看列表明细的权限", "entDocList.delete": "删除", "entDocList.tip": "提示", "entDocList.deleteTip": "删除后，文件夹中的履约材料以及各项配置将消失。是否确定删除此文件夹？", "entDocList.deleteSuccess": "删除成功", "entDocList.confirm": "确定", "entDocList.cancel": "取消", "entFolderPermission.title": "企业履约管理", "entFolderPermission.myPermission": "当前账号的文件夹权限包含：", "entFolderPermission.assignPermission": "分配文件夹权限", "entFolderPermission.unAssignPermission": "收回权限", "entFolderPermission.permissionResult": "授权结果", "entFolderPermission.permissionLog": "授权日志", "entFolderAssignPermission.title": "分配文件夹权限 | 收回文件夹权限", "entFolderAssignPermission.next": "下一步", "entFolderAssignPermission.cancel": "取消", "entFolderPermissionPerson.title": "选择授权人员 | 选择收回权限人员", "entFolderPermissionPerson.role": "授权角色", "entFolderPermissionPerson.user": "授权人员", "entFolderPermissionPerson.confirm": "确定", "entFolderEditFolder.title.folderName": "文件夹重命名", "entFolderEditFolder.title.remark": "文件夹备注", "entFolderEditFolder.folderNamePlaceholder": "请输入文件夹名称", "entFolderEditFolder.folderNameEmpty": "履约文件夹名称不能为空！", "entFolderEditFolder.remark": "填写备注", "entFolderEditFolder.remarkPlaceholder": "请输入", "entFolderEditFolder.confirm": "确定", "entFolderEditFolder.cancel": "取消", "transferFolder.title": "履约文件夹转交", "transferFolder.oldOwner": "原持有人：", "transferFolder.newOwner": "新持有人：", "transferFolder.confirm": "确定", "transferFolder.successTip": "转交成功", "entDoLisSlider.back": "返回到我的合同管理", "entDoLisSlider.reName": "重命名", "entDoLisSlider.transfer": "转交", "entDoLisSlider.editPermission": "编辑权限", "entDoLisSlider.editReMark": "备注", "entDoLisSlider.remind": "履约提醒", "entDoLisSlider.creator": "创建人：", "entDoLisSlider.myPermission": "我的权限：", "entDoLisSlider.remark": "备注：", "entDoLisSlider.borrowApprover": "借阅审批人：", "entDoLisSlider.addApprover": " + 新增", "entDoLisSlider.deleteApproverSuc": "删除成功！", "entDoLisSlider.know": "我知道了", "entDoLisSlider.deleteApproverTip": "企业文件夹如果没有借阅审批人，那么“借阅权限”将不可用，即用户不能借阅此文件夹中的合同。所以最后一位借阅审批人会被保留。", "entDoLisSlider.deleteApproverTitle": "提示", "entDocTable.operate": "操作", "entDocTable.download": "下载", "entDocTable.view": "查看", "entDocTable.borrow": "借阅审批", "entSearch.moreBusinessFields": "更多（业务字段）", "entSearch.plsSelect": "请选择", "entSearch.contractInvolveTip": "个人或企业成员参与的合同", "entSearch.searchFor": "搜索", "entSearch.unfold": "展开", "entSearch.putAway": "收起", "entSearch.export": "导出此文件夹明细", "entSearch.listConfig": "配置此文件夹明细", "entSearch.plsInput": "请输入", "entSearch.inputSenderPersonAndCompany": "请输入个人/企业名称", "entSearch.inputReceiverPersonAndCompany": "请输入个人/企业名称", "entSearch.plsSelectData": "请选择日期", "entSearch.confirm": "确认", "entSearch.noPermission": "您没有查看列表明细的权限", "entSearch.notSupported": "本期暂时不支持", "entDocDetail.mainContractDetail": "主合同详情", "entDocDetail.correctorContract": "关联合同", "entDocDetail.contractInfo": "合同信息：", "entDocDetail.contractTitle": "合同标题：", "entDocDetail.contractId": "合同编号：", "entDocDetail.idMainContract": "合同是否是主合同：", "entDocDetail.entFolderName": "履约文件夹名称：", "entDocDetail.yes": "是", "entDocDetail.no": "否", "entDocDetail.tag": "履约标签：", "entDocDetail.loanRecord.title": "借阅记录", "entDocDetail.loanRecord.account": "借阅账号", "entDocDetail.loanRecord.time": "借阅时间", "entDocDetail.loanRecord.duration": "借阅时长", "entDocDetail.loanRecord.timeUnit": "天", "entDocDetail.fulfillMaterial.title": "履约材料", "entDocDetail.fulfillMaterial.download": "下载", "entDocDetail.fulfillMaterial.rename": "重命名", "entDocDetail.fulfillMaterial.delete": "删除", "entDocDetail.fulfillMaterial.upload": "上传", "entDocDetail.fulfillMaterial.fileRename": "文件重命名", "entDocDetail.fulfillMaterial.fileName": "文件名称", "entDocDetail.fulfillMaterial.pleaseEnter": "请输入", "entDocDetail.fulfillMaterial.cancel": "取消", "entDocDetail.fulfillMaterial.confirm": "确认", "entDocDetail.fulfillMaterial.fileLimitTip": "单文件不超过16M", "entDocDetail.fulfillMaterial.emptyFileNameTip": "文件名不能为空", "noReadPermissionTip.title": "提示", "noReadPermissionTip.tip": "在当前企业文件夹中，您没有查看合同和借阅合同的权限，请联系管理员获取相关权限后重试。", "noReadPermissionTip.know": "我知道了", "applyBorrowDialog.title": "申请借阅合同", "applyBorrowDialog.borrowTime": "借阅时长：", "applyBorrowDialog.timePlaceHolder": "请选择", "applyBorrowDialog.timeUnit": "天", "applyBorrowDialog.reasonPlaceHolder": "请填写借阅的理由，方便审批人判断是否允许借阅。选填，不超过255个字", "applyBorrowDialog.approver": "审批人：", "applyBorrowDialog.rejectReason": "驳回原因：", "applyBorrowDialog.cancel": "取消", "applyBorrowDialog.reApply": "重新申请", "applyBorrowDialog.confirm": "确认", "applyBorrowDialog.applyingStatus": "审批中", "applyBorrowDialog.successStatus": "审批已通过", "applyBorrowDialog.rejectedStatus": "审批驳回", "applyBorrowDialog.noTimesTip": "请先选择借阅时长！", "applyBorrowDialog.applySendTip": "申请发送成功！", "addApproverDialog.title": "选择人员", "addApproverDialog.confirm": "确定", "addApproverDialog.maxApproverTip": "借阅审批人不能超过3个!", "addApproverDialog.noApproverTip": "请先选择人员!", "borrowApproval.title": "借阅审批", "borrowApproval.applicant": "借阅人员", "borrowApproval.borrowTime": "借阅时长", "borrowApproval.borrowReason": "借阅理由", "borrowApproval.approvalRes": "借阅结果", "borrowApproval.reject": "驳回", "borrowApproval.agree": "通过", "borrowApproval.timeUnit": "天", "borrowApproval.rejectTitle": "驳回原因", "borrowApproval.rejectPlaceHolder": "请填写驳回原因，选填，不超过255个字", "borrowApproval.cancel": "取消", "borrowApproval.confirm": "确认", "components.sendPointPosition.index-1c6dcb-1": "有盖章处/签字处没能匹配到关键字，暂时放置在合同第一页左下角，需要你一一手动调整。", "components.sendPointPosition.index-1c6dcb-2": "温馨提示", "components.sendPointPosition.index-1c6dcb-3": "确定", "components.sendPointPosition.index-1c6dcb-4": "所在文档：", "operate.certificateReport": "电子签约存证报告", "operate.notarization": "公证书", "operate.selectProject": "请选择出证申请项目", "operate.downloadAttacment": "下载签约存证页", "operate.downloadAttacmentTip": "上上签签约存证页为记录合同基本信息，以及合同从发送开始到签署完毕的过程中的签署者和签署信息。", "special-doc-dialog.tip1": "系统检测到文档存在顺时针旋转【{rotateContent}】，需要预处理后才能继续发起合同，整个过程已由系统自动完成了。", "special-doc-dialog.tip2": "需要预处理的常见原因", "special-doc-dialog.tip3": "在Adobe阅读器（也包括其他PDF阅读器，如福昕阅读器等）查看文件时发现排版存在翻转，曾对文件做过“旋转”处理。", "special-doc-dialog.tip4": "比如：本应是横版的纸质文件，在使用扫描仪扫描时处理成了竖版，在用Adobe阅读PDF扫描件时发现该页出现文字倒置的情况，于是点击来Adobe阅读器工具栏上的旋转按钮，将这一页的PDF重新“旋转”回为横版。", "special-doc-dialog.tip5": "预处理后的效果（文件体积会变大）", "special-doc-dialog.tip6": "1、原文档已添加了CA证书或使用其他加密策略的，在预处理后会失效", "special-doc-dialog.tip7": "2、发出的合同上的文字不能被选中、复制", "special-doc-dialog.tip8": "3、文档清晰度可能会变低，同时文件体积变大", "special-doc-dialog.tip9": "如需继续使用预处理后的文件，请点击\"继续\"按钮，以完成合同发送流程", "special-doc-dialog.tip10": "直接使用原件，跳过系统预处理的方法", "special-doc-dialog.tip11": "方法1：用Adobe阅读器（也包括其他PDF阅读器，如福昕阅读器等）打开PDF找到被旋转的页码，右击页面缩略图执行“旋转”操作或点击来Adobe阅读器工具栏上的旋转按钮，逆时针旋转至最初状态后重新上传（无视文件有翻转、文字倒置直接使用）。", "special-doc-dialog.tip12": "查看示意图", "special-doc-dialog.tip13": "方法2（PDF扫描件可用）：重新扫描纸质件得到正确的版式（扫描时纸张正确横摆和竖摆），然后重新上传文件发送合同。", "SsoConfirm.index-c220bb-1": "合同初稿", "SsoConfirm.index-c220bb-2": "发件方信息", "SsoConfirm.index-c220bb-3": "发件方企业：", "SsoConfirm.index-c220bb-4": "发件方账号：", "SsoConfirm.index-c220bb-5": "待发送合同", "SsoConfirm.index-c220bb-6": "查看并编辑", "SsoConfirm.index-c220bb-7": "签约方", "SsoConfirm.index-c220bb-8": "我已阅读并同意", "SsoConfirm.index-c220bb-9": "《合同发送注意事项》", "SsoConfirm.index-c220bb-10": "确认发送", "SsoConfirm.index-c220bb-11": "取消", "SsoConfirm.index-c220bb-12": "合同发送注意事项", "SsoConfirm.index-c220bb-13": "继续发送", "sealSelectDialog.title1": "申请用印", "sealSelectDialog.title2": "选择盖章执行人", "sealSelectDialog.nowApplySealList": "您正在请求以下印章", "sealSelectDialog.chooseApplyPersonToDeal": "请选择执行盖章操作的人员，您的申请以及合同将会转交给所选人来处理（你仍能继续查看、跟进此合同）", "sealSelectDialog.contactGroupAdminToDistributeSeal": "请联系集团管理员分配印章", "sealSelectDialog.cancel": "取消", "sealSelectDialog.confirm": "确定", "sealSelectDialog.applySealTip": "该合同正在等待{person}（{account}）签署。您可以采取以下两种方式之一来继续流程：", "sealSelectDialog.tipContent1": "1、请{person}（{account}）完成签署：", "sealSelectDialog.tipContent1Desc": "如果希望由{person}（{account}）来签署，请提醒他登录电子签平台并完成签署。", "sealSelectDialog.tipContent2": "2、您自行签署：", "sealSelectDialog.tipContent2Desc": "如果您希望亲自签署合同（您已经拥有印章了），请指导{person}（{account}）先拒签该合同，步骤如下：", "sealSelectDialog.refuseSignTip1": "第一步：登录电子签平台，找到当前合同。", "sealSelectDialog.refuseSignTip2": "第二步：在合同详情页点击“拒签”按钮。", "sealSelectDialog.refuseSignTip3": "第三步：在弹出的对话框中确认拒签操作。", "sealSelectDialog.refuseSignAfter": "拒签后的操作：", "sealSelectDialog.refuseSignAfterDesc": "一旦{person}（{account}）完成拒签，您将看到合同详情页新增“签署”按钮。点击此按钮，按照正常流程完成合同签署。", "sealSelectDialog.specialScene": "特殊情况处理：", "sealSelectDialog.specialSceneOpt": "如果您无法联系到{person}（{account}），您可以请求主管理员介入，通过“合同转交”操作，将合同的签署权从{person}（{account}）转移给其他人，以便他们可以按照上述步骤操作。", "templateReceiverConfig.err.needAddSender": "未设置本企业/本人作为签约方，合同发出后，你方将不会参与签署过程。是否需要将你方加入签署？", "templateReceiverConfig.err.addSender": "添加为签约方", "templateReceiverConfig.err.needSeal": "根据【盖章】的签署要求，需在下列文件中设置【盖章】签署位置", "templateReceiverConfig.err.needSignature": "根据【签字】的签署要求，需在下列文件中设置【签字】签署位置", "templateReceiverConfig.err.needSealSignature": "根据【盖章并签字】的签署要求，同一文件中的签署位置需同时设置【盖章】和【盖章人签字】", "templateReceiverConfig.err.needSealTip": "盖章的签署人在文档上存在其他字段（签署日期或签署人填写字段），唯独缺了印章。你可以删除其他字段，或者设置盖章处", "templateReceiverConfig.err.needSignatureTip": "签字的签署人在文档上存在其他字段（签署日期或签署人填写字段），唯独缺了签名。你可以删除其他字段，或者设置签名处", "labelLackTip.everyDocSign": "签署人需要在每份文件都设置签署位置才能查看、参与签署该文件", "labelNameConflictTip.hasExistName": "文档中已存在{num}处名字为“{name}”的字段", "labelNameConflictTip.nameChangeAdvice": "如果字段“{name}”的内容均需完全一致，您可以继续使用“{name}”作为字段名称。如果内容有所不同，建议选择一个能够准确描述字段内容或用途的名称。", "labelNameConflictTip.showEffect": "效果示意图", "labelNameConflictTip.pleaseChangeName": "您尝试命名的字段名称“{name}”已存在。为避免混淆，请选择一个不同的名称。", "labelNameConflictTip.inputLabelName": "请在下方输入框中输入一个新的字段名称。", "labelNameConflictTip.closeTip": "关闭弹窗则字段将不会被保存并自动删除。", "labelNameConflictTip.closeTipOfEdit": "点击确定，同名字段的属性将同步更新；点击关闭，字段名称修改不会被保存。", "labelEdit.importantNotice": "温馨提示", "labelEdit.tipInfo": "建议将签署人配置成多个签约角色，让每个角色对应一个盖章处。", "labelEdit.tipInfos.title1": "通常情况：", "labelEdit.tipInfos.con1": "同一个“签约角色”在合同上的多个盖章处会默认使用同一个章。虽然签署人可以手动调整每个盖章处的章，但可能会因疏忽大意而使用了同一个章", "labelEdit.tipInfos.title2": "推荐做法：", "labelEdit.tipInfos.con2": "为确保准确性，建议将签署人配置为多个签约角色，每个角色对应一种章。这样，每次签署时，签署人需单独确认此次签署所使用的章，从而减少混淆。", "labelEdit.tipInfos.title3": "特殊情况：", "labelEdit.tipInfos.con3": "如果所有签署位置确实需要使用同一种章，那么维持一个签约角色对应多个盖章处是合适的。", "labelEdit.detailedInstructions": "详细说明", "labelEdit.operationDiagram": "操作示意图", "templateDetail.noEditPermission": "你应先获取“编辑模板”的权限，才能下载模板文件", "templatePermission.table.searchPlaceholder": "输入账号查询模板权限", "permissionRight.dialog.rightName": "权限名称", "permissionRight.dialog.recycleRight": "如何收回权限", "permissionRight.dialog.moreTip": "更多说明", "permissionRight.dialog.recycleByAccount": "按账号{account}取消权限", "permissionRight.dialog.recycleByRole": "从角色「{role}」中移除该账号（需进入控制台操作）", "permissionRight.dialog.sendTip1": "可为以下企业发送合同：", "permissionRight.dialog.sendTip2": "来自模板的直接授权：{ents}", "permissionRight.dialog.sendTip3": "来自集团管理权限的授权：{ents}", "permissionRight.dialog.grantManage": "当一个模板所有人都没有“权限分配”的权限时，系统会自动赋予主管理员该权限并且不可被收回（只有当有更多人持有“权限分配”时，才可以从主管理员收回“权限分配”权限）。", "permissionRight.dialog.roleAffect": "该角色对应了{entName}中的以下账号：", "permissionRight.dialog.noRight": "当前账号未获得此模板的任何权限", "permissionRight.dialog.more": "等{count}家企业", "agent.riskJudgement": "AI律师", "agent.uploadText": "请上传需要进行风险判断的文件", "agent.startTips": "现在我们可以开始判断风险了", "agent.feedback": "问卷反馈", "agent.satisfy": "对分析结果满意，继续下一项分析", "agent.dissatisfy": "对分析结果不满意，重新进行分析", "agent.custom": "请输入自定义审查规则", "agent.submit": "发送", "agent.others": "其他", "agent.autoExtract": "自动进行下一步提取直到提取结束", "agent.autoRisk": "自动进行下一步分析直到分析结束", "agent.aiGenerated": "以上内容为AI生成，不代表上上签立场，请勿删除或修改本标记。", "agent.extractTitle": "信息提取", "agent.riskTitle": "AI律师", "agent.deepInference": "AI律师（深度推理）", "agent.chooseRisk": "请选择需要进行分析的文件", "agent.chooseExtract": "请选择需要进行提取的文件", "agent.analyzing": "内容分析中", "agent.advice": "修改建议生成中", "agent.options": "选项生成中", "agent.selectFunc": "选择功能", "agent.inputTips": "请输入确切内容", "filter.yes": "是", "agent.deepThinking": "深度思考中", "agent.deepThoughtCompleted": "已深度思考", "agent.original": "原文", "agent.revision": "修改建议", "agent.diff": "对比", "charge.packagePurchaseTitle": "【{title}功能】套餐购买", "charge.payOnce": "特惠限购一次", "charge.payNow": "立即购买", "charge.amount": "数目", "charge.unitPrice": "单价", "charge.limitTime": "有效期", "charge.copy": "份", "charge.month": "月", "charge.compareInfo1": "使用说明：", "charge.compareInfo2": "{index}、购买的{type}可用{per}数，对应企业所有成员均可使用，如你仅需个人使用，可在右上角登录主体切换到个人账号；", "charge.compareInfo3": "{index}、按上传的合同{per}数统计用量", "charge.codePay": "请用扫码支付", "charge.aliPay": "支付宝支付", "charge.wxPay": "微信支付", "charge.paySuccess": "购买成功", "charge.payIno": "开通功能 | 购买对象 | 支付金额", "charge.contactUs": "开通功能 | 购买对象 | 支付金额", "copySealDialog.title": "复制盖章处", "copySealDialog.pageDecTop": "在第", "copySealDialog.pageDecMiddle": "页和第", "copySealDialog.pageDecBottom": "页中复制盖章处", "copySealDialog.dec": "快速复制当前页盖章处到其他页面，这些页面盖章处位置与当前页一致。复制后，各页盖章处的位置独立调整，互不影响。", "copySealDialog.moreBtn": "更多说明", "copySealDialog.moreDec": "只有在合同文档的前半部分设置盖章处时，才会显示“复制盖章处”按钮（例如，100页文档的第1-50页中的盖章处才可以一键复制盖章处至其他页面）", "copySealDialog.confirm": "确定", "copySealDialog.cancel": "取消", "labelEdit.friendlyReminder": "温馨说明：", "labelEdit.decorateTipInfos.con1": "一键复制的盖章处中，仅最后一处印章使用了数字证书，这已足以确保整个签署行为的合法合规、真实有效。", "labelEdit.decorateTipInfos.con2": "如果您希望在某一个关键位置必须使用带数字证书的盖章，可重新通过“第二步：拖动签署位置”中“盖章”控件设置盖章处，这样的盖章处会附带数字证书。", "labels.sealCopy": "复制盖章处", "labels.delCopySeal": "仅删除当前盖章处", "labels.delAllCopySeal": "删除所有复制被复制的盖章处", "labelEdit.blankTip": "当你使用合同模板发送合同时，系统生成签署位置 / 字段，无需您额外指定。", "labelEdit.autoPosition": "方案1:按关键字生成", "labelEdit.autoPositionTip": "合同中的签署位置 / 字段的位置，将依照合同中关键字出现的位置来生成。如果关键字不存在，则出现在第一页的左下角。建议手动拖拽签署位置，调整偏移量（签署位置/字段与关键字间的相对距离）", "labelEdit.allPage": "方案2:按预设位置生成", "labelEdit.allPageTip": "合同中的签署位置 / 字段的位置，将依照模板中预设的位置对应的坐标来生成", "labelEdit.staticPosition": "在所有页码相同位置生成签署位置 / 字段", "labelEdit.Projection": "投影功能", "docDetail.dualRequired": "必须双录校验", "authorize.title": "使用须知", "authorize.content": "开启智能合同，AI帮你分析，让你的工作更轻松高效！同意以下内容，即可体验。", "authorize.cancel": "不用了", "authorize.confirm": "同意并使用", "authorize.contract": "查看《哈勃产品使用须知》", "agent.aiInterpret": "AI解读", "agent.reJudge": "重新判断", "agent.tipsContent": "重新判断将会扣除份数，是否继续？", "agent.confirm": "确认", "agent.tip": "提示", "aiAgent.title": "拖章Agent", "aiAgent.description1": "凡是从本地上传的文档，Agent都会自动帮你找到各个签约方的盖章处，以代替人工施动盖章处的繁琐操作", "aiAgent.description2": "*单份文档页数不能超过50页", "aiAgent.description3": "*配合'模板专用章'、'自动盖'等功能，可以实现非标合同盖章的完全自动化", "aiAgent.configTitle": "拖章规则配置", "aiAgent.configTip": "勾选需要使用的规则，并调整各个规则的排序，以明确规则冲突时的优先级。", "aiAgent.rule.needOverlappingSeal": "需要骑缝章", "aiAgent.rule.needOverlappingSealDesc": "*如果不勾选，则不会自动添加骑缝章盖章处", "aiAgent.rule.reciprocalSealing": "对等盖章：按已有盖章处匹配签约方的盖章处", "aiAgent.rule.reciprocalSealingDesc": "*例如，若合同上已有对方的3个印章（分布在不同页面），系统会自动为我方在相应位置也生成3个对应的盖章位置。 注：本功能仅在单方盖章时生效。", "aiAgent.rule.sealOnLastLine": "文件中若没有明确的盖章处，则盖在文件最后一行的文字上", "aiAgent.rule.sealOnLastLineDesc": "*不是传统意义上的合同，没有明确的盖章处指示的文件，如内部单据等需要盖章的文件", "aiAgent.rule.reasonableAreaSealing": "印章应放置在合理的区域（如盖章栏附近、合同末页公司信息处等）", "aiAgent.rule.reasonableAreaSealingDesc": "*可能会与对等盖章、每页盖章等规则有冲突，需要调整优先级，以确保优先需要满足的规则", "aiAgent.rule.sealEachPage": "每页盖章", "aiAgent.rule.sealEachPageDesc": "*如果是对账单、招投标文件，则每页相同位置都需要盖章", "aiAgent.adjustmentTitle": "自助调优", "aiAgent.positionAdjustment": "(1) 盖章位置调优（不含骑缝章）", "aiAgent.moveUp": "系统指定的盖章处需向上移动", "aiAgent.moveLeft": "系统指定的盖章处需向左移动", "aiAgent.centimeter": "厘米", "aiAgent.adjustTip1": "*可填写负数，代表反方向。", "aiAgent.adjustTip2": "*N厘米是打印后的大小，默认情况下一个章的直径约4厘米，可以以此折算", "aiAgent.adjustTip3": "*移动盖章处可能会与已有盖章处重叠，请知悉", "aiAgent.contentAdjustment": "(2) 是否盖章调整", "aiAgent.addSealPrefix": "若文件中出现", "aiAgent.addSealSuffix": "该内容对应的位置需要盖章", "aiAgent.removeSealPrefix": "若文件中出现", "aiAgent.removeSealSuffix": "该内容对应的位置不需要盖章", "aiAgent.adjustTip4": "*此处配置后，规则将变成最高优先级。该配置仅在单方盖章时生效", "aiAgent.saveConfig": "保存配置", "aiAgent.resetConfig": "重置配置", "aiAgent.featureNotAvailable": "需联系上上签支持人员付费开启", "aiAgent.enableSuccess": "已开启拖章Agent", "aiAgent.disableSuccess": "已关闭拖章Agent", "aiAgent.operationFailed": "操作失败，请稍后重试", "aiAgent.templateIdRequired": "模板ID不能为空", "aiAgent.saveSuccess": "保存成功", "aiAgent.saveFailed": "保存失败，请稍后重试", "aiAgent.loadFailed": "加载配置失败，请稍后重试", "autoStamp.buttonText": "自动拖章", "autoStamp.progressTitle": "拖章Agent正依据{documentNames}内容为签署人指定盖章处。拖章完成后，请验收结果。若符合您的预期，请点击'发送'按钮，正式发出此份合同。", "autoStamp.progressText": "进度条{percentage}%", "autoStamp.cancelButton": "取消自动拖章", "autoStamp.taskComplete": "自动拖章完成", "autoStamp.taskFailed": "自动拖章失败，请稍后重试"}