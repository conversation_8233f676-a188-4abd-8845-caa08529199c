{"templateCommon.tempMgmt": "テンプレート管理", "templateCommon.mark": "備考", "templateCommon.set": "設定", "templateCommon.operation": "操作", "templateCommon.selectedFile": "{number}件のファイルを選択しています", "templateCommon.edit": "編集", "templateCommon.use": "使用", "templateCommon.approval": "承認", "templateCommon.add": "追加", "templateCommon.delete": "削除", "templateCommon.deleteSucc": "削除完了", "templateCommon.batchDelete": "一括削除", "templateCommon.copy": "コピー", "templateCommon.group": "ファイリング", "templateCommon.remove": "移動", "templateCommon.confirm": "確定", "templateCommon.singleSendContract": "契約書の単独発送", "templateCommon.batchSendContract": "Excelの一括発送を使用", "templateCommon.cancel": "キャンセル", "templateCommon.save": "保存", "templateCommon.inputPlaceHolder": "入力してください", "templateCommon.selectPlaceHolder": "選択してください", "templateCommon.reset": "リセット", "templateCommon.search": "検索", "templateCommon.searchPlaceholder": "検索内容を入力", "templateCommon.btnCreateTemp": "すぐにテンプレートを新規作成", "templateCommon.next": "次へ", "templateCommon.previous": "前", "templateCommon.rename": "名前の変更", "templateCommon.stick": "ピン留め", "templateCommon.complete": "完了", "templateCommon.tip": "注意", "templateCommon.quit": "終了", "templateCommon.copySucc": "コピー完了", "templateCommon.copyFail": "コピー失敗", "templateCommon.setSuccess": "設定に成功", "templateCommon.understand": "わかりました", "templateCommon.setting": "その他", "templateCommon.moneyUnit.RMB": "人民元", "templateCommon.moneyUnit.EUR": "ユーロ", "templateCommon.moneyUnit.Dollar": "米ドル", "templateCommon.moneyUnit.GBP": "ポンド", "templateCommon.selectAll": "すべて選択", "templateCommon.useTip": "ヒント", "templateCommon.goOn": "続行する", "templateCommon.abandon": "中止する", "templateCommon.tempSave": "一時保存して終了", "templateCommon.allType": "形式制限なし", "templateCommon.specialSealConfirm": "下記署名者がテンプレート専用印章を設定しておらず、<b>任意の印章</b>で署名できます：", "templateCommon.warmTip": "温かいヒント", "templateCommon.usageHelp": "使用方法", "templateConfigGuide.contract": "契約書", "templateConfigGuide.hrContract": "人事契約書", "templateConfigGuide.salesContract": "販売業者契約書", "templateConfigGuide.userGuide": "ショートカット設定ガイド", "templateConfigGuide.title": "テンプレート設定ガイド", "templateConfigGuide.sendContract": "契約書の起稿", "templateConfigGuide.funExplain": "機能説明", "templateConfigGuide.functionDes": "テンプレートは業務中に使われる契約書の初期設定に対して、契約者・契約条件・署名位置などを設定することができます。一度条件を設定すると、次の段階でテンプレートを使用して、異なる企業/個人に契約書を送信する際に直接参照することができます。", "templateConfigGuide.configUse": "設定開始", "templateConfigGuide.selectTemplateSceneType": "お客様の契約書の使用場面に基に、下記のテンプレートの中から選択し、設定を開始します。", "templateConfigGuide.hrScene": "人事場面用テンプレート", "templateConfigGuide.salesScene": "販売業者場面用テンプレート", "templateConfigGuide.commonScene": "その他場面用テンプレート", "templateConfigGuide.sendContractDirect": "テンプレートを新規作成することなく、素早く契約書を起稿", "templateConfigGuide.uploadFile": "ファイルのアップロード（標準契約書ファイルが準備されている）", "templateConfigGuide.uploadTemplateFile": "契約書テンプレートファイルのアップロード", "templateConfigGuide.uploadingTip": "ファイルのアップロードが完了するまで待ってから操作してください", "templateConfigGuide.addContractTip": "「ファイルのアップロード」でファイルをアップロードするか、「ファイルの予約」で空白のファイルスペースを1に設定する必要があります", "templateConfigGuide.uploadFileDesc": "標準契約書ファイル（標準契約契約書テキストを含む）をアップロードします。このファイルは、元の契約内容の空白を保持する必要があります。これらの空白は、将来、送信者または受信者が特定の内容値を持つ別の契約書を生成する際埋めることができます。クリックしてファイルをアップロードしてください。", "templateConfigGuide.sceneDoc.HR": "人事場面には労働契約、機密保持契約があります", "templateConfigGuide.sceneDoc.SALES": "販売業者場面には販売業者代理契約、企業貨物輸送契約などがあります", "templateConfigGuide.sceneDoc.COMMON": "共通場面には主に常用される契約書です", "templateConfigGuide.filePreserve": "ファイル予約（契約書ファイルが準備されていない）", "templateConfigGuide.filePreserveDesc1": "契約書ファイルが準備できていない場合、この手順でファイルスペースを確保できます。ファイルが複数あればそれだけ用意し、後で契約書を使用する際準備できた契約書を追加することができます。", "templateConfigGuide.blankDocNumIs": "アップロードする文書の数を設定する必要があります", "templateConfigGuide.fillDesField": "契約説明フィールドの記入", "templateConfigGuide.setDesFieldTip1": "契約書説明フィールドではお客様が契約書そのものを記録/管理します。ファイルがアップロードしてから、契約書タイプ/企業内部番号などの契約書説明フィールドを入力します。", "templateConfigGuide.setDesFieldTip2": "契約書タイプ、社内管理番号、契約締切日、契約満期日はシステムの初期設定内容フィールドに属しており、お客様は色のある「フィールド設定」をクリックして、これらの細フィールドの表示/非表示を設定できます。", "templateConfigGuide.contractTypeDesp1": "契約書タイプを追加したい場合、「 」に入って", "templateConfigGuide.contractTypeDesp2": "「契約書タイプ」の編集ボタンを選択し、自発的にタイプの説明を追加します。", "templateConfigGuide.consoleRouteText": "企業管理コンソール - 業務フィールド管理 - 契約書説明", "templateConfigGuide.configBusinessFieldInfo": "契約説明フィールドのユーザー設定", "templateConfigGuide.descFieldDesp1": "「契約書タイプ」の契約書説明フィールド以外にも、その他説明フィールドをユーザー設定できます。その際は「」に入り", "templateConfigGuide.descFieldDesp2": "ユーザー設定します。", "templateConfigGuide.backToPage": "ページに戻る", "templateConfigGuide.setContractType": "契約書種類を記入", "templateConfigGuide.setFieldNecessary": "契約書説明項目は必要に応じて設定", "templateConfigGuide.setReceiverRole": "契約上の役割設定", "templateConfigGuide.receiverRoleExplain": "契約上の役割とは契約書内で捺印/自署の必要な企業もしくは個人を指します。", "templateConfigGuide.backToSetTemplateName": "お客様はUIに戻って情報を追加しなければなりません", "templateConfigGuide.hrTemplateRoleDesc": "人事場面では2つの役割を事前設定しています。", "templateConfigGuide.salesRoleDesc": "販売業者場面では2つの役割を事前設定しています。", "templateConfigGuide.commonRoleDesc": "共通場面ではから1つの役割を事前設定しています。", "templateConfigGuide.changeRoleNameTip": "役割名の修正や契約の役割を継続追加することができます", "templateConfigGuide.role": "契約上の役割{num}：", "templateConfigGuide.addPersonRole": "＋　個人の署名者を追加", "templateConfigGuide.addEntRole": "＋　法人の署名者を追加", "templateConfigGuide.entPlaceholder": "例：企業／部門", "templateConfigGuide.personPlaceholder": "例：従業員", "templateConfigGuide.batchImportRoleInfo": "契約上の役割の一括追加", "templateConfigGuide.batchImportRoleInfoDesc": "テンプレートを使用して契約書を発信する際、異なる企業/個人に一括送信が必要な場合、対応する役割で「Excelを使用したアカウントの一括インポートモード」にチェックする必要があります。システムはお客様のExcelファイル内の契約の役割とアカウントを基にして自動で組み合わせます。一括発送の必要な役割を選択してください。：", "templateConfigGuide.needExcelImport": "Excelを使用したアカウントの一括インポートが必要", "templateConfigGuide.person": "個人", "templateConfigGuide.ent": "企業", "templateConfigGuide.setRoleSignConfig": "契約上の役割要件の設定", "templateConfigGuide.setRoleSignConfigDesc": "契約上の役割方法および契約要件を事前に設定できます。明確に直接入力済みです。明確でない場合、テンプレートを使用して契約書の発信する際追加できます。", "templateConfigGuide.pointPosition": "指定署名位置", "templateConfigGuide.pointPositionDesc": "この手順で、ファイルの空欄にプレースホルダーを設定することができます。ページ左側の「臨時フィールド」から合致するタイプを選択してください。ページ右側でプレースホルダーの名称と入力者を指定できます。", "templateConfigGuide.templateConfigDesc": "あなたのテンプレートは基本的な機能を備えています。以下の手順でテンプレートの機能を拡張できます：1. テンプレートの各種権限を適切なメンバーまたはロールに割り当てる。2. テンプレート詳細ページを開き、詳細設定を行う。", "templateConfigGuide.templateConfig": "テンプレート権限の設定", "templateConfigGuide.addContractField": "ファイルの捺印/自署箇所で、各署名者の署名位置を指定することができます。空ファイルを例にすると、下記の通り操作する必要があります", "templateConfigGuide.addSignFieldPosition": "空ファイルであれば、ファイル内の各契約の色捺印/自署箇所に含まれるキーワードを事前設定し、キーワードを通して署名箇所の位置決めを行う必要があります。", "templateConfigGuide.templateConfigAuthTip": "テンプレート権限管理", "templateConfigGuide.templateDetailTip": "テンプレート詳細設定", "templateConfigGuide.toSetting": "設定へ", "templateConfigGuide.employee": "メンバー", "templateConfigGuide.hr": "会社人事部", "templateConfigGuide.dealer": "販売代理店企業", "templateConfigGuide.finance": "会社経理部", "templateConfigGuide.entOwn": "本企業", "templateDetail.basic.docComId": "組み合わせファイル番号：", "templateDetail.basic.docComCreater": "組み合わせファイル作成者：", "templateDetail.basic.docComCreateTime": "組み合わせファイル生成日時：", "templateDetail.basic.docComRemark": "組み合わせファイル備考：", "templateDetail.basic.batchSendContract": "契約書の一括発送", "templateDetail.basic.singleSendContract": "契約書の単独発送", "templateDetail.basic.edit": "編集", "templateDetail.basic.doc": "ファイル：", "templateDetail.basic.sender": "契約書発信先：", "templateDetail.basic.senderAccount": "契約書送信者：", "templateDetail.basic.receiver": "契約書受信先：", "templateDetail.basic.receiverAccount": "契約書受信者：", "templateDetail.basic.contractEndTime": "契約満期日：", "templateDetail.basic.signEndTime": "調印満期日：", "templateDetail.basic.endTimeUnit": "(日)", "templateDetail.basic.contractType": "契約書種類：", "templateDetail.basic.entNo": "社内管理番号：", "templateDetail.basic.area": "所属エリア：", "templateDetail.basic.senderField": "発信者による記入待ちのフィールド：", "templateDetail.basic.signerField": "署名者による記入待ちのフィールド：", "templateDetail.basic.contractInfo": "契約書情報", "templateDetail.basic.pageIndex": "ページ数：{page}ページ", "templateDetail.basic.empty": "記入待ち", "templateDetail.basic.invalidStatementName": "元のテンプレートと一致させること", "templateDetail.msg.editSuccess": "修正成功", "templateDetail.msg.editFail": "修正失敗", "templateDetail.msg.whiteDocument": "ファイル内容は空欄です", "templatePermission.tempMgmt": "テンプレート管理", "templatePermission.permissionMine": "授権方法", "templatePermission.permissionAll": "全権限", "templatePermission.permissionAllTitle": "授権結果", "templatePermission.withdrawPermissionAll": "権限の取り消し", "templatePermission.withdrawPermissionDeny": "操作権限なし", "templatePermission.admin": "メイン管理者", "templatePermission.staff": "従業員", "templatePermission.myTemplatePermissions": "現アカウントのテンプレートには次のものが含まれています：", "templatePermission.permissionLog": "授権ログ", "templatePermission.new": "新規作成", "templatePermission.search.composeName": "権限案名", "templatePermission.search.auth": "権限項目", "templatePermission.search.name": "氏名", "templatePermission.search.account": "アカウント", "templatePermission.search.role": "役割", "templatePermission.table.permissionFederationName": "権限案名", "templatePermission.table.createPermissionFederation": "権限案の新規作成", "templatePermission.table.btnTooltips": "権限案はお客様の一連の権限制御の確率を許可し、権限案をその他のテンプレートに応用します。権限を入手した構成員のみテンプレートの関連操作を行なうことができます。", "templatePermission.table.editPermission": "権限の編集", "templatePermission.table.auth": "権限項目", "templatePermission.table.authTo": "授権をXXに与える", "templatePermission.table.operate": "操作", "templatePermission.table.authToEmployes": "被授権者：", "templatePermission.table.authToRoles": "授権された役割：", "templatePermission.table.operateAuthTo": "授権", "templatePermission.table.time": "日時：", "templatePermission.table.detail": "詳細：", "templatePermission.table.distribute": "分配：", "templatePermission.table.withdraw": "取り下げ：", "templatePermission.table.permission": "権限", "templatePermission.table.deleteSelectTip": "先に削除する必要のある権限案を選択してください：", "templatePermission.table.resetPermission": "再授権", "templatePermission.table.role": "役割", "templatePermission.table.roleName": "役割/構成員名", "templatePermission.table.companyName": "会社", "templatePermission.table.permissionDetail": "権限明細", "templatePermission.table.all": "全て", "templatePermission.dialog.authToRoles": "役割", "templatePermission.dialog.selectEnt": "企業の選択", "templatePermission.dialog.selectRole": "役割の選択", "templatePermission.dialog.selectedRole": "選択済み役割", "templatePermission.dialog.authToEmployes": "ユーザ", "templatePermission.dialog.selectEmployes": "構成員の選択", "templatePermission.dialog.selectedEmployes": "選択済み構成員", "templatePermission.dialog.recoverEmployesPermission": "メンバーの現在の権限を保持する", "templatePermission.dialog.recoverRolesPermission": "役割の現在の権限を保持する", "templatePermission.dialog.keepPermissionTip": "チェックを入れると現在の権限に新しい権限を追加します。チェックを外すと現在の権限が取消され、新しく権限を付与します（選択された役割またはメンバーのみに影響し、選択されていない者は元の権限を保持します）。", "templatePermission.dialog.all": "すべて選択", "templatePermission.dialog.selectPermissionFederation": "授権方法の選択", "templatePermission.dialog.confirm": "確定", "templatePermission.dialog.cancel": "キャンセル", "templatePermission.dialog.deletePermissionTipContent": "このロールの権限を取り消しますか？", "templatePermission.dialog.deletePermissionTipContent1": "このメンバーの権限を取り消しますか？", "templatePermission.dialog.deletePermissionTipContent2": "このアカウント/ロールの権限を削除してよろしいですか", "templatePermission.dialog.tip": "注意", "templatePermission.slide.editComposeName": "権限案名", "templatePermission.slide.editPermission": "権限の編集", "templatePermission.slide.save": "保存", "templatePermission.slide.tip": "権限設定の変更は、前の権限設定に基づいてメンバーに既に割り当てられた権限には影響しません。メンバーは元の権限を保持します。", "templatePermission.slide.nameNotEmpty": "権限案名は空欄にできません", "templatePermission.slide.checkNotEmpty": "権限のチェック項目は空欄にできません", "templatePermission.slide.selectPermissionTitle": "授権権限の選択", "templatePermission.msg.editSuccess": "編集完了", "templatePermission.msg.editFail": "編集失敗", "templatePermission.msg.createSuccess": "新規作成完了", "templatePermission.msg.createFail": "新規作成失敗", "templatePermission.msg.unselectFederation": "先に権限案を一つ選択してください：", "templatePermission.msg.deleteSuccess": "削除完了", "templatePermission.msg.federationEmpty": "権限案がありません。先に権限案を作成してください", "templatePermission.msg.permissionSuccess": "授権完了", "templatePermission.permissonAuthChoose.title": "取り消す権限の選択", "templatePermission.permissonMember": "メンバー/ロール権限", "templatePermission.mainAdminRole": "メイン管理者", "templatePermission.employeeRole": "一般ユーザー", "editCompose.edit": "組み合わせファイルの修正", "editCompose.name": "組み合わせ名称", "editCompose.id": "組み合わせファイル番号", "editCompose.comment": "ファイル備考", "editCompose.selectDoc": "契約書の選択", "editCompose.currentCompose": "現在の組み合わせファイル：", "editCompose.confirm": "確定", "editCompose.errorTip.nameNeed": "組み合わせ名称を入力してください", "editCompose.errorTip.idNeed": "組み合わせファイル番号を入力してください", "editCompose.errorTip.selectedNeed": "少なくとも2項目を選択してください", "templateList.manager": "管理者", "templateList.managerTip": "権限を割り当てる権限を持つ人員", "templateList.createTemp": "テンプレート作成", "templateList.createDynamicTemp": "動態テンプレート作成", "templateList.createOfdTemp": "OFDテンプレートを作成", "templateList.dynamicTemplateEntry": "動態テンプレートポータル", "templateList.myTemp": "マイテンプレート", "templateList.allTemp": "全てのテンプレート", "templateList.myCreateTemp": "自分が作成したもの", "templateList.grantedTemp": "権限を付与されたもの", "templateList.approvalTemp": "私の承認待ち", "templateList.tempName": "テンプレート名", "templateList.tempMark": "備考", "templateList.tempId": "番号", "templateList.star": "お気に入りに追加", "templateList.cancelStar": "お気に入りから削除", "templateList.starSuccess": "お気に入りに追加しました", "temolateList.starFolder": "お気に入り", "templateList.templateIdTip": "テンプレートID形式が正しくありません。数字のみを入力してください。", "templateList.cancelStarSuccess": "お気に入りから削除しました", "templateList.templateCategory": "種類", "templateList.dynamicTemplate": "動態テンプレート", "templateList.staticTemplate": "一般テンプレート", "templateList.hasCacheDraft": "1件の契約書が{time}に一時保存され、送信が完了していません。前回の操作を続行しますか？", "templateList.createUser": "作成者", "templateList.creatingCompany": "テンプレート作成企業", "templateList.folder": "フォルダー", "templateList.tempPermission": "権限", "templateList.permissionMgmt": "権限管理", "templateList.enableStatus": "状態", "templateList.enabled": "有効", "templateList.disabled": "無効", "templateList.batchPermission": "一括授権", "templateList.permissionSuccess": "授権完了", "templateList.selectPermission": "権限グループを一つ選択してください", "templateList.selectFederation": "権限案を選択してください", "templateList.selectRole": "役割の選択", "templateList.selectUser": "構成員の選択", "templateList.enableTemp": "テンプレートの有効化", "templateList.disableTemp": "テンプレートの無効化", "templateList.enableConfirmText": "有効にすると、授権されている使用者はこのテンプレートを使用できるようになります。有効にしますか？", "templateList.disableConfirmText": "無効にすると、このテンプレートが使用できなくなります。無効にしますか？", "templateList.enableSuccess": "有効にしました", "templateList.disableSuccess": "無効にしました", "templateList.deleteTemp": "テンプレートの削除", "templateList.deleteFail": "削除エラー", "templateList.deleteTempConfirmText": "テンプレートを削除すると復元できなくなります。テンプレートを削除しますか？", "templateList.editInfoTip": "このテンプレートは編集中です。使用したい場合テンプレートの作成者に連絡してください", "templateList.enable": "有効", "templateList.disable": "無効", "templateList.switchToReceiver": "お客様は{receiver}に切り替えています ", "templateList.createUserPlaceholder": "输入须包含名称开头部分", "templateList.sendCode": "送信コード", "templateList.moreFeature": "高度な機能", "templateList.downloadSendCode": "ダウンロード", "templateList.generate": "生成", "templateList.view": "表示", "templateList.close": "閉じる", "templateList.alwaysEnable": "長期間有効", "templateList.customDays": "使用可能日数を設定", "templateList.remainDays": "残り{day}日", "templateList.selectValidDate": "有効期限を選択：", "templateList.selectValidDateTip": "有効期限を選択してください", "templateList.selectValidDateErrorTip": "現在の時間より後の時間を選択してください", "templateList.validToDate": "有効期限：", "templateList.inputIntNumberPlaceholder": "999日以内の時間を選択してください", "templateList.setExpireDays": "テンプレート使用可能日数の設定", "templateList.noPermissions": "テンプレート編集権限を取得してから変更できます", "templateList.sendCodeTip": "現在のテンプレート設定が送信コード生成条件を満たしていません。以下の要件を満たしているかどうかチェックしてください。", "templateList.errorOperate": "不正な権限操作", "templateList.sendCodeTipFail.1": "空白の文書は含まれていません", "templateList.sendCodeTipFail.2": "契約者はただ1つの可変者(署名とccを含む)、そして可変者は第1の操作者でなければなりません;署名者は必ず捺印所を設けなければなりません", "templateList.sendCodeTipFail.3": "契約者のうち固定者のアカウントは空ではありません", "templateList.sendCodeTipFail.4": "送信前承認はありません", "templateList.sendCodeTipFail.5": "送信者はフィールドが空にならないように必ず記入します（記述フィールドと契約内容フィールドを含みます）", "templateList.sendCodeTipFail.6": "非テンプレートの組み合わせです", "templateList.sendCodeGuide.title": "送信コード高度な機能説明", "templateList.sendCodeGuide.info": "テンプレート送信コードは、送信者が内容記入不要の文書（退職証明書、秘密保持誓約書、委任状等）に適用し、コードをスキャンする任意の人に提供できます。送信者が内容記入必要な文書にあるか、又はコードをスキャンして文書を取得する相手方を限定している場合は、ファイルキャビネットの拡張機能を使用できます。ファイルキャビネットのQRコードをスキャンすることで、指定した人の契約書自動送信ができ、かつ送信者が記入必要な内容を記入し、自動送信の時刻を設定することもできます。QRコードをスキャンした相手方の情報をファイルキャビネットに保存し、後から参照することができます。使用方法の詳細は次の通りです。", "templateList.sendCodeGuide.tip1.main": "1、ベストサイン", "templateList.sendCodeGuide.tip1.sub": "", "templateList.sendCodeGuide.tip1.line1": "ファイルキャビネット、契約情報自動入力、QRコードによる契約書作成機能の開通をベストサインに申し込み", "templateList.sendCodeGuide.tip1.line2": "機能を開通したら該当メニューから操作できます。", "templateList.sendCodeGuide.tip2.main": "2、ファイルキャビネット管理者", "templateList.sendCodeGuide.tip2.sub": "ファイルキャビネットを作成し、QRコードによる契約書作成の設定を行う", "templateList.sendCodeGuide.tip2.line1": "", "templateList.sendCodeGuide.tip2.line2": "契約書テンプレートに記入必要なフィールドと同じ項目をファイルキャビネットに設定し、ファイルキャビネットと契約書テンプレートを紐付け、契約情報自動入力の設定を行って、QRコードスキャンによる契約書送信の条件を設定してから、ファイルキャビネットのQRコードを契約の相手方に提供します。", "templateList.sendCodeGuide.tip3.main": "3、契約者", "templateList.sendCodeGuide.tip3.sub": "QRコードを読取り、必要な内容を記入して、契約書を取得", "templateList.sendCodeGuide.tip3.line1": "", "templateList.sendCodeGuide.tip3.line2": "契約者は、送信者から取得したファイルキャビネットのQRコードを読取り、必要な情報を記入します。記入した情報はファイルキャビネットに保存されると共に、契約書にも反映されます。そして契約書を受け取って、署名をするだけで契約締結が完了します。", "templateList.sendCodeGuide.tip4.main": "4、ファイルキャビネット管理者", "templateList.sendCodeGuide.tip4.sub": "", "templateList.sendCodeGuide.tip4.line1": "契約の相手方及び送信した契約書の状況を確認", "templateList.sendCodeGuide.tip4.line2": "送信側の管理者は、ファイルキャビネットで、QRコードを読み取った相手方の情報、契約書送信の状況、署名状態等を確認することができます。", "templateList.linkBoxTip": "関連ファイルキャビネットID：", "templateApproval.approvalProcess": "承認フロー", "templateApproval.approvalStatus": "承認状態", "templateApproval.approving": "承認中", "templateApproval.toBeApproval": "承認必要", "templateApproval.approved": "承認済み", "templateApproval.approver": "承認者", "templateApproval.reporter": "申請者", "templateApproval.reject": "却下", "templateApproval.agree": "同意", "templateApproval.backList": "戻る", "templateApproval.signerInfo": "署名者情報", "templateApproval.rejectSuccess": "却下成功", "templateApproval.approvalSuccess": "承認完了", "templateApproval.approvalStopTip": "承認者または承認待ちの内容が変更されました。「承認待ち」に戻り、テンプレート承認ページに再度アクセスしてください。", "templateApproval.approvalAttention": "新たに提出した審査は、数秒の遅延がある可能性があります。しばらく経ってから更新してください", "templateApproval.approvalMessage": "承認コメント：", "templateApproval.approvalAgree": "承認", "templateApproval.approvalReject": "承認却下", "templateApproval.templateDocumentsInfo": "テンプレートドキュメント情報", "templateApproval.templateDocumentName": "テンプレートドキュメント名称", "templateApproval.templateDocumentId": "テンプレート番号(documentId)", "templateApproval.templateApprovingTip": "承認中のテンプレートではこの操作を実行できません。", "templateListEntDialog.title": "テンプレートの一括授権", "templateListEntDialog.entGroupNums.1": "選択しているテンプレートの分類", "templateListEntDialog.entGroupNums.2": "の異なる企業主体（主体授権を超えることのない）、異なる主体に基づき順次授権してください", "templateListEntDialog.authed": "授権済み", "templateListEntDialog.next": "次へ", "templateListEntDialog.confirm": "確定", "templateFolder.add": "フォルダーの新規作成", "templateFolder.edit": "フォルダーの編集", "templateFolder.folder": "フォルダー名", "templateFolder.nameEmptyTip": "フォルダー名を入力してください", "templateFolder.nameInvalidTip": "中国語、英語もしくは数字を組み合わせて入力してください ", "templateFolder.selectFolderTip": "フォルダーを選択してください", "templateFolder.groupSuccess": "ファイリング完了", "templateFolder.deleteFolderTip": "削除した後、フォルダー内のテンプレートは解放されます。削除しますか？", "templateFolder.deleteSucc": "削除完了", "templateSubList.tempDocument": "テンプレートファイル", "templateSubList.tempFederation": "テンプレートファイル組み合わせ", "templateSubList.templateSpecialSeal": "専用印章のテンプレート", "templateSubList.tempSceneConfig": "場面設定", "templateSubList.tempFederationTip": "クリックして含まれるファイル組み合わせを確認する", "templateSubList.tempInvalidStatement": "無効宣言", "templateSubList.tempSupplement": "補足契約", "templateSubList.documentName": "ファイル名", "templateSubList.federationId": "組み合わせ番号", "templateSubList.noFederationData": "テンプレートのファイルユニットがまだ設定されていません", "templateSubList.federationOptTip1": "「テンプレートファイル」タブでファイルユニットを設定します", "templateSubList.federationOptTip2": "契約書を送信すると同時に、迅速に契約書ユニットを送信します", "templateSubList.federationName": "組み合わせ名", "templateSubList.useFederation": "組み合わせ使用", "templateSubList.createFederation": "組み合わせファイルの構成", "templateSubList.federationMark": "組み合わせ備考", "templateSubList.inputFederationName": "組み合わせ名を入力してください", "templateSubList.addTempFederation": "組み合わせファイルの新規作成", "templateSubList.addTempIsAutoJoin": "新規アップロードファイルを個のファイル組み合わせに自動で追加しますか", "templateSubList.delete": "削除", "templateSubList.deleteDocument": "ファイルの削除", "templateSubList.deleteDocumentTipDesc": "このファイルを削除しますか？削除すると復元できません", "templateSubList.confirmTitle": "注意", "templateSubList.deleteSuc": "削除完了", "templateSubList.deleteFederationTipDesc": "この組み合わせを削除しますか？削除すると復元できません", "templateSubList.sceneConfig.saveConfig": "設定の保存", "templateSubList.sceneConfig.saveSuccess": "保存完了", "templateSubList.sceneConfig.limitedTitle": "制限条件：", "templateSubList.sceneConfig.manageVerifyCode.showCodeInContract": "契約書内に確認コードを表示", "templateSubList.sceneConfig.manageVerifyCode.selectCodeStyle": "チェックコードのタイプをを選択してください", "templateSubList.sceneConfig.manageVerifyCode.preView": "プレビュー", "templateSubList.sceneConfig.manageVerifyCode.limitOtherSignatureTip": "契約書に表示される認証コードはクロスプラットフォーム署名には適用されません、「クロスプラットフォーム署名」を無効にしてからやり直してください", "templateSubList.sceneConfig.manageVerifyCode.selectCodePage": "请选择查验码展示的页数", "templateSubList.sceneConfig.manageVerifyCode.lastPage": "合同最后一页", "templateSubList.sceneConfig.manageVerifyCode.allPage": "合同全部页", "templateSubList.sceneConfig.manageVerifyCode.allPageTip": "请确保合同每页大小保持一致，方可展示全部查验码。推荐使用竖版A4文档。", "templateSubList.sceneConfig.INNER_RESOLUTION.title": "内部決議場面", "templateSubList.sceneConfig.INNER_RESOLUTION.description": "機能効果：企業の契約者のために多くの契約役割に企業の署名+1の契約役割大行企業印を設定する。当該企業が捺印した後、当該企業の未署名の契約書には引き続き署名が必要でなくなります(すでに自署されたものはそのまま有効です）", "templateSubList.sceneConfig.INNER_RESOLUTION.limitItems.0": "署名拒否ボタンを有効にしなければなりません", "templateSubList.sceneConfig.INNER_RESOLUTION.limitItems.1": "「順次署名」場面には適用されません", "templateSubList.sceneConfig.INNER_RESOLUTION.limitItems.2": "「紙ベース署名への変更を許可する」場面には適用されません", "templateSubList.sceneConfig.INNER_RESOLUTION.hideRefuseBtnConfigTip": "内部決議を有効にするには、先に署名拒否ボタンを有効にしなければなりません", "templateSubList.sceneConfig.INNER_RESOLUTION.limitPageSignTip": "内部決議は紙ベース署名場面で適していません。「紙ベース署名への変更を許可する」設定を無効にした後再操作してください", "templateSubList.sceneConfig.INNER_RESOLUTION.limitHideRefuseTip": "紙の署名は内部決議場面には適用されません、「内部決議場面」を無効にしてからやり直してください", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.title": "クロスボーダープラットフォーム署名", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.description": "テンプレートを使用するを許可している際デジタル証明書の付いたPDFファイル（つまりその他の契約プラットフォームで署名した契約書）をアップロード", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.0": "「契約書にチェックコードを表示する」場面には適用されません", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.1": "「契約書装飾」応用場面には適用されません。すかし、画像フィールドを含みます", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.2": "「内容フィールド（送信者または署名者）を記入する」場面には適用されません", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.3": "「契約付属ファイル機能」には適用されません", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.note": "使用上の注意事項：「テンプレート専用印」機能と合わせた後、「クロスボータープラットフォーム署名」契約書起稿後の署名行為を制御することができます。", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.conflictVerifyCode": "クロスボータープラットフォーム署名はチェックコード場面には適しておらず、「契約書内でチェックコードを表示」の設定を無効にしてから再操作してください", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.updateHybridJarTip": "圧縮ファイル（JAR）のバージョンが低すぎます。アップグレードするようベストサインに連絡してください", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent1": "デジタル証明書を含んだPDFファイルはテンプレートファイルとして使用できません。| クロスプラットフォーム電子署名が有効になっていないテンプレートでは、電子証明書付きのファイルを使用できません。", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent2": "推奨手順：", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent3": "ステップ1：テンプレートを作成し、「詳細設定-シナリオカスタマイズ」で「クロスプラットフォーム電子署名」を有効にします（この設定がない場合は、有料版にアップグレードする必要があります）；", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent4": "ステップ2：このテンプレートを使用して契約を送信する際、電子証明書を含むPDFをアップロードし、クロスプラットフォーム電子署名を実現します。", "templateSubList.sceneConfig.paperSign.changeToPaperSign": "紙媒体署名の変更を許可", "templateSubList.sceneConfig.paperSign.hideOtherAccountLogin": "その他アカウントを使用した署名を表示しない：ポータルを非表示にすると、この機能は対面での署名や双方署名にのみ対応しているため、署名をする際の障害になる可能性があります。", "templateSubList.sceneConfig.paperSign.signTitle": "企業の署名者は電子署名を使用することなく、紙媒体書類への捺印をしてから郵送で契約書を署名することもできます。", "templateSubList.sceneConfig.paperSign.signTip": "紙媒体署名の法律有効性は従来通り保証されてるものではありますが、本プラットフォームでは保障しかねます。証拠となる資料の収集や保存には十分注意してください。", "templateSubList.sceneConfig.paperSign.postAddress": "郵送住所：", "templateSubList.sceneConfig.paperSign.receiverName": "郵送受領者氏名：", "templateSubList.sceneConfig.paperSign.receiverContract": "郵送受領者連絡方法：", "templateSubList.sceneConfig.paperSign.postAddressFill": "発信者企業の具体的な住所（オプション）", "templateSubList.sceneConfig.paperSign.receiverNameFill": "発信者企業の紙媒体契約書の受領者氏名（オプション）", "templateSubList.sceneConfig.paperSign.receiverContractFill": "発信者企業の紙媒体契約書の受領社携帯電話（オプション）", "templateSubList.sceneConfig.paperSign.preview": "プレビュー", "templateSubList.sceneConfig.paperSign.pdfRequired": "署名者は必ず紙媒体契約書のスキャンデータ（PDF）を提供しなければなりません", "templateSubList.sceneConfig.paperSign.paperErrorTip.receiverName": "正確な受領者氏名を入力してください", "templateSubList.sceneConfig.paperSign.paperErrorTip.receiverInfo": "連絡方法に誤りがあります。検査した後再入力してください", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.notice": "注意事項：", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.tip1": "契約書に先に署名する契約者が紙媒体への署名方法を使用すると、契約者の署名していないもう一方も必ず紙媒体への署名方法を使用しなければなりません。", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.tip2": "順序通りに署名するようにしてください。", "templateSubList.sceneConfig.paperSign.saveConfig": "紙媒体署名の設定を保存", "templateSubList.sceneConfig.paperSign.advance": "紙媒体署名の詳細要件", "templateSubList.sceneConfig.paperSign.advanceRadioText.freeChoice": "自由選択：契約者により自由に紙媒体署名にするか電子署名にするか選択します", "templateSubList.sceneConfig.paperSign.advanceRadioText.mustUsePaperSign": "紙媒体署名が必須：署名者すべてが紙媒体署名方法を使用しなければならず、電子署名方法を使用することはできません", "templateSubList.sceneConfig.paperSign.advanceRadioText.separateConfig": "紙媒体署名の単独設定：初期設定では電子署名を使用し、契約書単独で「紙媒体署名が必要」を設定することができます。つまり、発信者がある契約書の詳細ページの中で「紙媒体署名が必要」を有効にすると、この契約署名者は必ず紙媒体署名を使用しなければならず、その他設定していない契約書は必ず電子署名を使用なければならないということです。", "templateSubList.sceneConfig.paperSign.openSequence": "順序署名を有効", "templateSubList.sceneConfig.paperSign.cancel": "キャンセル", "templateSubList.sceneConfig.paperSign.confirm": "確定", "templateSubList.sceneConfig.limitInnerResolutionTip": "内部決議場面は署名拒否ボタンの非表示に依存します、「内部決議場面」を無効にしてからやり直してください", "templateSubList.sceneConfig.limitSettingTip": "先に右サイドバーの設定説明をお読みください", "templateSubList.sceneConfig.openSuccess": "オンに成功しました", "templateSubList.sceneConfig.closeSuccess": "オフに成功しました", "templateSubList.sceneConfig.hideRefuseSignButton": "署名拒否ボタンを非表示", "templateSubList.sceneConfig.viewCase": "例を確認します", "templateSubList.sceneConfig.enableContractAlias": "「契約書」の別名を有効", "templateSubList.sceneConfig.contractAliasDesc": "契約受信者がこのテンプレート契約書に署名する過程で見る「契約書」の文字は、以下のテキストに置き換わります。", "templateSubList.sceneConfig.hideSenderInfoInPreviewPage": "プレビューページの発信者情報を非表示", "templateSubList.sceneConfig.isAutoSignConfigOn": "契約者の「自動署名」機能の有効を許可して、このテンプレートが発信する契約書に署名しますか", "templateSubList.sceneConfig.autoSign": "契約者の自動署名管理", "templateSubList.sceneConfig.selfEntAccessOnly": "当社／グループのみ自動捺印の使用を許可（企業に対して発行された自署名を含み、個人の署名には使用できません）、他の契約当事者は自動捺印を使用できません", "templateSubList.sceneConfig.close": "閉じる", "templateSubList.sceneConfig.save": "保存", "templateSubList.sceneConfig.signRoleEnable": "下記契約の役割も自動署名を使用可能にする", "templateSubList.sceneConfig.allSignerNoAccess": "すべての契約書当事者に自動署名をできなくする", "templateSubList.sceneConfig.autoSignNotAllowTip.notice": "お客様が契約当事者の自動署名条件を設定しても、署名当事者が自発的に印章もしくは署名を基本とした自動署名機能を有効にしないと、契約書の自動署名が最終的に完了できません", "templateSubList.sceneConfig.autoSignNotAllowTip.title": "以下の署名要件を設定する契約当事者は自動署名機能を使用して、このテンプレートが発信する契約書に署名できません：", "templateSubList.sceneConfig.autoSignNotAllowTip.tip1": "手書き署名が必須です", "templateSubList.sceneConfig.autoSignNotAllowTip.tip2": "顔認証署名が必須です", "templateSubList.sceneConfig.autoSignNotAllowTip.tip3": "受信先が支払う", "templateSubList.sceneConfig.autoSignNotAllowTip.tip4": "署名者がフィールドに入力する", "templateSubList.sceneConfig.autoSignNotAllowTip.tip5": "署名者が資料を提供する", "templateSubList.sceneConfig.autoSignNotAllowTip.tip6": "手書きメモ識別", "templateSubList.sceneConfig.autoSignNotAllowTip.tip7": "閲読完了後再署名する", "templateSubList.sceneConfig.autoSignNotAllowTip.tip8": "担当者の実名情報が一致しないか実名認証していないため、担当者の実名情報を検査する必要があります", "templateSubList.sceneConfig.autoSignNotAllowTip.tip9": "業務照合印", "templateSubList.sceneConfig.autoSignNotAllowTip.tip10": "企業の複数人自署", "templateSubList.sceneConfig.autoSignNotAllowTip.tip11": "FDA署名", "templateSubList.sceneConfig.specificationBusinessFields": "契約業務フィールドの規範", "templateSubList.sceneConfig.descriptionField": "契約説明フィールド", "templateSubList.sceneConfig.contentField": "契約書内容フィールド", "templateSubList.sceneConfig.addField": "フィールドの追加", "templateSubList.sceneConfig.tip": "注意", "templateSubList.sceneConfig.deleteConfirmTip.0": "削除すると、設定されたフィールドははテンプレートから消えてしまいます。フィールドの名前を直接変更することができ、テンプレートに設定されているフィールドの属性は変わりませんが、フィールドの名前には変更後の名前が使用されます。", "templateSubList.sceneConfig.deleteConfirmTip.1": "削除を続けますか？", "templateSubList.sceneConfig.deleteSuc": "削除完了", "templateSubList.sceneConfig.editName": "名称の修正", "templateSubList.sceneConfig.fda.enableFdaSignature": "FDA署名タイプを有効", "templateSubList.sceneConfig.fda.description": "チェックを入れると署名がFDA 21 CFR Part 11（アメリカ連邦規則集21巻11章）に適合するものとなり、企業の自署や個人の署名に対して効力を発します", "templateSubList.sceneConfig.fda.addOption": "追加オプション", "templateSubList.sceneConfig.fda.allowInput": "署名者自身の入力を許可", "templateSubList.sceneConfig.fda.allowInputTip": "チェックを入れると署名者が自身で理由を編集することができるようになります", "templateSubList.sceneConfig.fda.inputOption": "オプションを入力してください", "templateSubList.sceneConfig.fda.inputOptionLimit": "{limit}文字以内の内容で入力してください", "templateSubList.sceneConfig.customsCheck": "税関署名ファイル検査、ファイルが税関の署名基準に適合しているか補助検査を行います（ファイル名の長さ、文字フォント、空白ページが含まれていないか検査を行います）", "templateSubList.sceneConfig.enterpriseSignatureOnly": "企業の署名を単独で使用することを許可する", "templateSubList.sceneConfig.isSwitch": "切替", "templateSubList.sceneConfig.featureDescription": "機能説明", "templateSubList.sceneConfig.featureConfiguration": "機能設定", "templateSubList.specialSeal.templateRole": "テンプレートの役割", "templateSubList.specialSeal.isLimitEnable": "専用印を有効にしますか", "templateSubList.specialSeal.configLimitInfo": "専用印の設定", "templateSubList.specialSeal.configLimitInfoOwn": "自分側の専用印章を設定", "templateSubList.specialSeal.configLimitInfoOther": "相手側の専用印章を設定", "templateSubList.specialSeal.settingTip": "注意", "templateSubList.specialSeal.companySealSetTip": "この企業にはすでに専用印章が設定されています、置き換えますか？", "templateSubList.specialSeal.companySealSetTip1": "以下の企業は専用印章が設定されています：", "templateSubList.specialSeal.companySealSetTip2": "今回設定した専用印章に更新しますか", "templateSubList.specialSeal.deleteTip": "専用印章のテンプレートの注意を削除", "templateSubList.specialSeal.confirmTip": "専用印章のテンプレートを削除しますか？", "templateSubList.specialSeal.enable": "有効", "templateSubList.specialSeal.disable": "無効", "templateSubList.specialSeal.config": "編集の確認", "templateSubList.specialSeal.sealImgSource": "専用印の引用元", "templateSubList.specialSeal.fromTemplate": "テンプレートからの印章", "templateSubList.specialSeal.fromContract": "契約書からの印章", "templateSubList.specialSeal.sourceIdInputTip": "テンプレート番号もしくは契約書番号を入力してください", "templateSubList.specialSeal.inputIdText": "{text}を入力してください", "templateSubList.specialSeal.templateNo": "テンプレート番号", "templateSubList.specialSeal.contractNo": "契約番号", "templateSubList.specialSeal.limitedSealsList": "現在の専用印リスト", "templateSubList.specialSeal.newLimitSealsList": "今回追加した専用印", "templateSubList.specialSeal.limitSealTip": "すでに存在している専用印図案は更新されません。更新が必要であれば、この専用印を削除した後再操作してください", "templateSubList.specialSeal.noSealTip": "このテンプレートが発信した契約書関連の印章図案はありません | 契約書関連の印章図案はありません", "templateSubList.specialSeal.correctNoTip": "正しい{text}を入力してください", "templateSubList.specialSeal.confirm": "確認", "templateSubList.specialSeal.cancel": "キャンセル", "templateSubList.specialSeal.save": "保存", "templateSubList.specialSeal.saveSuccess": "保存完了", "templateSubList.specialSeal.removeSuccess": "削除に成功", "templateSubList.specialSeal.sameSealTip": "この印章が設定されました", "templateSubList.specialSeal.companyName": "企業", "templateSubList.specialSeal.plsInputEnterpriseName": "企業名を入力してください", "templateSubList.invalidStatement.description.0": "『無効宣言』により、契約の状態を「無効」に変更することができます。『無効宣言』は、以下の方法により、すべての人（契約に関与していない人を含む）に公表されます。", "templateSubList.invalidStatement.description.1": "(1）無効となった契約書は、ベストサイン公式サイトによる検査後の結果表示ページに表示されます。", "templateSubList.invalidStatement.description.2": "(2)無効となった契約書に記載されている契約書チェックコードは、スキャンして開いた契約書チェックページに表示されます。", "templateSubList.invalidStatement.reset": "『無効宣言』のリセット", "templateSubList.invalidStatement.delete": "削除", "templateSubList.invalidStatement.page": "ページ", "templateSubList.invalidStatement.create": "『無効宣言』の設定", "templateSubList.invalidStatement.deleteTip": "削除完了", "templateSubList.invalidStatement.deleteConfirm": "削除するとベストサインのシステムを通して契約書の無効処理ができません。削除しますか？", "templateSubList.invalidStatement.deleteConfirmTip": "注意", "templateSubList.invalidStatementTabTip": "先に契約書テンプレートの契約当事者を設定してください", "templateSubList.supplement.add": "補足契約の追加", "templateSubList.supplement.inputTemplateId": "補足する必要のあるテンプレート番号を入力してください", "templateSubList.supplement.inputCorrectTip": "正確なテンプレート番号を入力してください", "templateSubList.contractConfidentiality.name": "契約の秘密保持", "templateSubList.contractConfidentiality.autoTransferDesc": "当社グループ/当社の契約参加者は契約操作任務を完了した後、自動的に契約を新しいアカウントに渡して保有し、元の契約参加者は契約を保有していない。", "templateSubList.contractConfidentiality.viewTemplate": "見取り図の表示", "templateSubList.contractConfidentiality.holderDesc": "注：新しい所有者アカウントは、元の契約所有者と同じ企業でなければ有効になりません。", "templateSubList.contractConfidentiality.holderTip": "（契約所有者：契約に参加し、送信、承認、署名、補完及びCCされた当社/グループメンバーアカウントを含む）", "templateSubList.contractConfidentiality.participantsType": "参加方法", "templateSubList.contractConfidentiality.newHolderAccount": "新規所有者アカウント", "templateSubList.contractConfidentiality.accountPlaceholder": "携帯電話/メールアドレス、記入しなければ転送しない", "templateSubList.contractConfidentiality.sendContract": "契約の送信", "templateSubList.contractConfidentiality.approvalContract": "契約の承認", "templateSubList.contractConfidentiality.signContract": "契約にサインする", "templateSubList.contractConfidentiality.editContract": "補完契約", "templateSubList.contractConfidentiality.editContractTip": "契約にフィールドを予約し、契約を発行してからフィールドの内容を補完する。ほとんどのビジネスではこのような参加方法は使用されておらず、一般的に構成する必要はありません。", "templateSubList.contractConfidentiality.hideContractDesc": "自動金庫への移動：契約は「契約金庫」に統合され、契約の詳細は所有者のみが表示でき、契約を所有していない管理者も表示できません。契約自体に秘密保持が必要でない限り、この機能を有効にすることはお勧めしません。契約の正常な統計と検索に影響するため、API呼び出しにも影響します。", "sendContract.tempSaveTip.title": "「ステージと終了」ボタン", "sendContract.tempSaveTip.content1": "契約書はまだ発送されていません。 このテンプレートを再度選択して契約書を送信すると、この契約書を引き続き送信できます。", "sendContract.tempSaveTip.content2": "テンプレートは最大で一時的に保存でき、7 日以内であれば引き続き送信できます。", "choseBoxForReceiver.dataNeedForReceiver": "契約主体が提出しなければならない資料", "choseBoxForReceiver.dataFromDataBox": "契約主体が提出しなければならない資料はアーカイブスの資料を通じて採集入手しなければなりません", "choseBoxForReceiver.searchTp": "アーカイブス名または番号を入力してください", "choseBoxForReceiver.search": "検索", "choseBoxForReceiver.boxNotFound": "アーカイブスが見つかりません", "choseBoxForReceiver.cancel": "キャンセル", "choseBoxForReceiver.confirm": "確　認", "contractInfo.attachmentInfo": "添付ファイル情報", "contractInfo.contractDescInfo": "契約書詳細情報", "contractInfo.batchImportLabelsTip": "以下のフィールドは、Excelで記入できず、このページでの設定に従う", "contractInfo.contractName": "契約書タイトル", "contractInfo.fieldConfig": "フィールド設定", "contractInfo.fieldConfigDesp": "フィールドの非表示設定", "contractInfo.fieldConfigTooltip": "このフィールドは第1部目のファイルのフィールド設定と強制的に同期して設定します。単独で修正する必要がある場合、先に第1部目のファイルの「テンプレートのその他ファイルと同期する」を無効にしてください。参考図：", "contractInfo.confirmTitle": "確認", "contractInfo.fieldConfigConfirmTip": "「フィールドの同期」を有効にすると、最初のドキュメントへの設定内容が、すべてのドキュメントに反映されます。「フィールドの同期」を有効にしますか？", "contractInfo.contractNameRequire": "契約書タイトルを入力してください", "contractInfo.contractNameTooltip": "契約表題には特殊文字を含めず、100文字以内にしてください", "contractInfo.customNumber": "社内管理番号", "contractInfo.contractType": "契約書種類", "contractInfo.contractTypeIdsForApprove": "契約書種類（予備）", "contractInfo.signValidateTerm": "署名有効期間(日)", "contractInfo.signDeadLine": "締結期限", "contractInfo.signDeadLineTooltip": "この日付までに署名しない場合、この契約書に署名出来なくなります。", "contractInfo.selectDate": "日時の選択", "contractInfo.contractExpireDate": "契約満了日", "contractInfo.contractExpireDays": "契約書有効期間(日)", "contractInfo.expireDateTooltip": "契約内容の有効期間は後で契約書を管理するのに役立ちます", "contractInfo.notNecessary": "任意", "contractInfo.dateTips": "お客様のために契約書有効期日を自動識別しました。確認してください", "contractInfo.contractTitleErr": "契約名には特殊文字を含めないでください", "contractInfo.contractTitleLengthErr": "契約名には100字を超えないでください", "contractInfo.contractTitleEmptyErr": "契約書名は空欄にできません", "contractInfo.inputFieldTip": "フィールド情報を記入してください", "contractInfo.signExpireDaysLimitTip": "契約有効期間は設定された{limit}日のみサポートします", "contractInfo.lockMustHasValue": "ロックするには内容を記入する必要があります", "contractInfo.lockOpenTip": "有効にすると、現在のフィールド内容がロックされ、契約書送信時にフィールドの内容を変更できません。", "contractInfo.lockLimitTip": "テンプレート管理者はこのフィールドの内容変更を禁止しました", "contractInfo.excelImportTip": "以下の項目は、Excelテーブルでフィールドをインポートします", "contractInfo.contractContentInfo": "契約書内容フィールド", "contractInfo.contractContentPicture": "契約書装飾図", "contractInfo.contractContentAttachment": "契約付属ファイル", "contractInfo.justImg": "画像フォーマットのみアップロード", "contractInfo.lessThan5": "アップロードする画像は5MBを超えないでください", "contractInfo.importSuccess": "インポート成功", "contractInfo.clickToView": "確認をクリック", "contractInfo.hasImported": "一括インポート済み", "contractInfo.readyToImport": "一括インポート待ち", "contractInfo.noticeContractName": "契約書表題の複数ファイル", "contractInfo.noticeContractNameTooltip1": "例として署名のリマインド、審査、副本、契約の完了など、今回発信する契約書において、署名過程の各種通知で対応する契約書名はこの名前が用いられます。", "contractInfo.noticeContractNameTooltip2": "契約書管理ページで、契約書の複数ファイルの統一名称としてこの名前を使用し、下部契約の独立名称としてファイルの「契約書表題」を使用します。", "contractInfo.msgExample": "例（SMS)：", "contractInfo.noticeContractNameExample1": "****有限公司がお客様のアカウントXXXXに『 ", "contractInfo.noticeContractNameExample2": "』を発信しました。契約期限は@@@@です。 ", "contractInfo.example": "例", "contractInfo.view": "確認", "contractInfo.replace": "交換", "contractInfo.delete": "消去", "contractInfo.attachmentType": "PDF、Word、Excel及び画像フォーマットのデータをアップロードしてください", "contractInfo.attachmentUploadError": "アップロードエラーです", "contractInfo.attachmentDocumentTip": "{type}フォーマットのファイルをローカルにダウンロードして確認します。すぐにダウンロードしますか？", "configTemplate.contractListAbove30": "契約書の総数は30を超えてはいけません", "configTemplate.crossPlatformContract": "越境契約", "configTemplate.config": "テンプレートの設定", "configTemplate.use": "テンプレートの使用", "configTemplate.save": "テンプレートの保存", "configTemplate.justSave": "保存", "configTemplate.nextStep": "次へ", "configTemplate.uploadContract": "ファイルのアップロード", "configTemplate.confirmContract": "ファイルのチェック", "configTemplate.configContract": "ファイルの設定", "configTemplate.prepareReceiver": "契約者の事前設定", "configTemplate.configReceiver": "契約者の設定", "configTemplate.pointPosition": "署名位置の指定", "configTemplate.batchSend": "テンプレートを使用して契約書を一括送信", "configTemplate.batchImportInfo": "契約情報と契約者情報を一括インポート", "configTemplate.templateName": "テンプレート名", "configTemplate.templateNameRequired": "テンプレート名を入力してください", "configTemplate.templateNameFormatTip": "テンプレート名には括弧を含むことができません", "configTemplate.templateNote": "備考", "configTemplate.selectApproval": "テンプレート承認フロー", "configTemplate.selectedApprover": "テンプレート承認者", "configTemplate.customizedApprove": "カスタム承認フロー", "configTemplate.addContract": "契約の追加", "configTemplate.contractInfoError": "契約書基本情報に誤りがあります。検査した後提出してください", "configTemplate.uploadContractTip": "契約書ファイルをアップロードしてください", "configTemplate.existEmptyContractTip": "空白ファイルがあります。契約内容を追加してアップロードしてください", "configTemplate.accountPlaceholder": "携帯電話またはメールアドレスを入力してください", "configTemplate.accountPlaceholderJa": "メールアドレスを入力して", "configTemplate.noSendContract": "ユーザーに契約権限が発信されていません", "configTemplate.defaultTemplateName": "無効宣言", "configTemplate.configSenderField.toContractFill": "契約書上で入力する", "configTemplate.configSenderField.pageName": "入力待ちフィールド", "configTemplate.configSenderField.backTip": "リターンするとデータが保存されません、リターンしますか？", "configTemplate.configSenderField.fieldValidateTip": "フィールド情報の認証に失敗しました、確認してください", "downloadPwdDialog.copy": "コピー", "downloadPwdDialog.copySucc": "コピー完了", "downloadPwdDialog.copyFailed": "コピー失敗", "downloadPwdDialog.downloadTip": "ダウンロード注意", "downloadPwdDialog.downloadContentTip": "お客様のダウンロードする契約書ファイル《{fileName}.zip》はシステムで暗号化されています、解凍パスワードは次のとおりです：", "downloadPwdDialog.downloadCodeTip": "注意：ダウンロードパスワードはその都度発信します。", "batchImport.optTip": "記入を終えると直接アップロード（アップロードされたファイル名はテンプレートsheet2/sheet3と表示され、テンプレートをコピー記入することで可能）します。", "batchImport.optDecTip": "圧縮ファイルをアップロードします。圧縮ファイルのアップロードには、契約書内に画像などの添付ファイルが追加される必要があります。", "batchImport.excelTemplate": "Excelテンプレート", "batchImport.downloadExcelTemplate": "Excelテンプレートをダウンロードします。", "batchImport.contentFileImportSuccess": "下記のファイル内容と署名者により一括インポートが終了しています", "batchImport.batchImport": "一括インポート", "batchImport.reBatchImport": "再一括インポート", "batchImport.batchImportTip": "批量导入提示", "batchImport.iKnow": "理解", "batchImport.longTimeLoadingTip": "契約書の数が多く、解析中ですので、お待ちください。", "batchImport.zipImport": "圧縮ファイルのアップロード", "batchImport.zipReimport": "圧縮ファイルの再アップロード", "batchImport.importView": "インポートの確認", "batchImport.documentsImport": "ドキュメント圧縮ファイルのアップロード", "batchImport.documentsReimport": "ドキュメント圧縮ファイルの再アップロード", "batchImport.msg.success": "インポートに成功しました。", "batchImport.msg.fail": "インポートに失敗しました", "batchImport.msg.confirm": "確定", "batchImport.msg.templateDisabled": "このテンプレートは使用できません", "batchImport.msg.authRestored": "このテンプレートの使用権限はテンプレート作成者によって取り消されました", "batchImport.msg.hasDel": "このテンプレートは作成者によって削除されました", "batchImport.msg.listBack": "テンプレートリストに戻る", "batchImport.selectedBlank": "文書アップロード案内", "batchImport.selectedBlankTip2Title": "2.「文書アップロード」ボタンをクリックしてください", "batchImport.selectedBlankTip2Content": "アップロードするファイルが全ての契約当事者に適用されることを確認してください（つまり、全ての当事者が同じ契約内容を閲覧できるようにします）。", "batchImport.selectedBlankTip1Title": "1.「文書圧縮ファイルアップロード」ボタンをクリックしてください", "batchImport.selectedBlankTip1Content": "異なる契約当事者が異なるファイルを閲覧する必要がある場合は、これらのファイルを圧縮ファイルにまとめてください。「文書圧縮ファイルアップロード」ボタンをクリックし、圧縮パッケージを選択してアップロードしてください。", "batchImport.checkBlankDocsTitle": "圧縮パッケージとしてアップロードする必要がある文書にチェックを入れてください。", "batchImport.selectedBlankMsg": "先に空白ドキュメントを選択してください", "batchImport.confirm": "確定", "batchImport.blankDecTip": "ドキュメント圧縮ファイルをアップロードします（上限50MB）。圧縮ファイルのアップロードには、契約書内にドキュメントが追加される必要があります。", "batchImport.useZip": "圧縮してファイルをアップロードしてください", "batchImport.step": "手順 {step} ：", "addressBook.searchAll": "すべて選択", "addressBook.innerMember.title": "企業内部メンバー", "addressBook.innerMember.tips": "送信者が素早く内部の担当者を探せるように企業メンバー情報を調整します", "addressBook.innerMember.operation": "管理コンソールに入る", "addressBook.outerContacts.title": "外部企業担当者", "addressBook.outerContacts.tips": "業務の展開がうまくいくよう、お客様の協力パートナーを招待し、事前に実名登録をしてください", "addressBook.outerContacts.operation": "協力パートナーを招待する", "addressBook.myContacts.title": "私の担当者", "addressBook.myContacts.tips": "署名者の情報が正確かつ間違いがないように、担当者を修正します", "addressBook.myContacts.operation": "ユーザーセンターに入る", "addressBook.myContacts.toConsole": "管理コンソールに入る", "addressBook.selected": "すでにアカウント選択済み", "addressBook.search": "検索", "addressBook.all": "全部", "addressBook.loadMore": "もっと読む", "addressBook.end": "全ロード完了", "addressBook.resigned": "退職済", "addressBook.editOutContact": "外部連絡先の情報を修正", "addressBook.noEditPermission": "権限がありません，メイン管理者に連絡してください", "addReceiver.prepareReceiver": "契約者の設定", "addReceiver.viewSignOrders": "署名順序の確認", "addReceiver.sender": "送信者", "addReceiver.proxyOuterSend": "外部企業に代わって契約を送信する", "addReceiver.proxyOuterSendTip": "確認後、エージェントの送信権限を承認した外部会社に契約を送信することを選択できます", "addReceiver.noOuterSenderTip": "送信する承認されたエージェントを持たない外部企業は、ファイルキャビネットに移動して、相手方に承認を完了するように通知することができます", "addReceiver.waitSenderInfoInit": "送信者データを初期化中です。しばらくしてから再試行してください。", "addReceiver.signInOrder": "順序に従って署名", "addReceiver.signInOrderTip": "チェックすると、署名者は設定された順序に従って署名します。前の署名者が署名完了した後に、次の署名者が署名可能になります。", "addReceiver.innerSigner": "中国側署名者", "addReceiver.internalSigner": "国際側署名者", "addReceiver.configForReviewerBehind": "", "addReceiver.receiver": "携帯電話/メールアドレスで受信 | （最多で5個、セミコロンで区切ることができます）", "addReceiver.orderSignLabel": "署名の順序", "addReceiver.contactAddress": "アドレス帳", "addReceiver.signOrder": "順序で署名する", "addReceiver.account": "アカウント", "addReceiver.accountPlaceholder": "携帯電話/メールアドレス（必須項目）", "addReceiver.accountReceptionCollection": "受付での受け取り", "addReceiver.accountReceptionCollectionTip1": "相手方の具体的なアカウントを知らないか相手方がアカウントを持っていません", "addReceiver.accountReceptionCollectionTip2": "受付での受け取りを選択してください", "addReceiver.signSubjectPerson": "契約主体：個人", "addReceiver.nameTips": "氏名（オプション、契約身分の確認で用います）", "addReceiver.requiredNameTips": "氏名（必須項目、契約身分の確認で用います）", "addReceiver.entOperatorNameTips": "氏名（オプション）", "addReceiver.needAuth": "実名が必要です", "addReceiver.signSubjectEnt": "契約主体：企業", "addReceiver.entNameTips": "登録法人名", "addReceiver.operator": "担当者", "addReceiver.sign": "署名", "addReceiver.done": "完了", "addReceiver.more": "アドレス帳から追加", "addReceiver.messageAndFaceVerify": "顔認証＋認証コードによる検証", "addReceiver.messageAndFaceVerifyTips": "顔認証と認証コードの検証が終わらないと契約書に署名できません。顔認証署名を完了するには、実名認証が必要です。中国大陸の住民のみ利用可能です", "addReceiver.faceFirst": "顔認証が優先で、予備で認証コードの署名になります", "addReceiver.faceFirstTips": "署名する際システムは初期設定で顔認証による検証を採用しています。顔認証が通らない回数が1日の上限を超えた場合自動で認証コードによる検証に切り替わります", "addReceiver.mustFace": "顔認証署名が必須です", "addReceiver.handWriteNotAllowed": "ベストサイン署名システムを使用", "addReceiver.mustHandWrite": "手書き署名が必須です", "addReceiver.fillIDNumber": "身分証明書番号", "addReceiver.fillNoticeCall": "携帯電話で通知", "addReceiver.fillNoticeCallTips": "通知携帯電話を記入してください", "addReceiver.addNotice": "メッセージの追加", "addReceiver.attachTips": "契約書付属資料の追加", "addReceiver.faceSign": "顔認証署名が必須です", "addReceiver.faceSignTips": "この使用者が署名を完了するには、顔認証の検証が必要です（顔認証署名は当面の間、中国大陸の住民のみ利用可能です）", "addReceiver.handWriteNotAllowedTips": "この使用者はベストサインのシステムでの署名だけが選択できます", "addReceiver.handWriteTips": "この使用者は手書きサインで署名を完了する必要があります", "addReceiver.idNumberTips": "契約身分の確認で用います", "addReceiver.verifyBefore": "文書を確認する前に身分を認証", "addReceiver.verify": "身分の認証", "addReceiver.verifyTips": "最多20文字", "addReceiver.verifyTips2": "お客様はこの認証情報をこの使用者に提供しなければなりません", "addReceiver.sendToThirdPlatform": "第三者プラットフォームに送信", "addReceiver.platFormName": "プラットフォーム名", "addReceiver.fillThirdPlatFormName": "プラットフォーム名を入力してください", "addReceiver.attach": "資料", "addReceiver.attachName": "資材名", "addReceiver.exampleID": "例：身分証写真", "addReceiver.attachInfo": "備考", "addReceiver.attachInfoTips": "例：本人の身分証写真をアップロードしてください", "addReceiver.addAttachRequire": "資料の追加", "addReceiver.addSignEnt": "契約者（法人）の追加", "addReceiver.addSignPerson": "契約者（個人）の追加", "addReceiver.addCC": "CCパーティーを追加", "addReceiver.addCCEnt": "法人会員へのCC", "addReceiver.addCCPerson": "個々のユーザーへの CC", "addReceiver.addCCUser": "副本", "addReceiver.addSignUser": "自署", "addReceiver.addFromAddressBook": "アドレス帳から追加", "addReceiver.selectContact": "担当者の選択", "addReceiver.save": "保存", "addReceiver.searchVerify": "認証の確認", "addReceiver.fillImageContentTips": "画像の内容を入力してください", "addReceiver.ok": "確定", "addReceiver.findContact": "契約書の中から下記の契約者を探します", "addReceiver.signer": "契約者", "addReceiver.signerTips": "ヒント：契約者の選択後、プラットフォームは自署及び捺印の位置決めを補助します。", "addReceiver.add": "追加", "addReceiver.notAdd": "追加なし", "addReceiver.cc": "副本", "addReceiver.notNeedAuth": "実名は必要ありません", "addReceiver.extracting": "抽出中", "addReceiver.autoFill": "署名者の自動記入", "addReceiver.failExtracting": "契約者が抽出できませんした", "addReceiver.idNumberForVerifyErr": "正確な身分証を入力してください", "addReceiver.noAccountErr": "アカウントは空欄に出来ません", "addReceiver.ccError": "副本発信者は携帯電話もしくは電子メールだけ受け取れます", "addReceiver.editorError": "补全人只能有一个接收手机或邮箱", "addReceiver.noUserNameErr": "氏名は空欄に出来ません", "addReceiver.noIDNumberErr": "身分証番号は空欄にできません", "addReceiver.noEntNameErr": "企業名は空欄にできません", "addReceiver.accountFormatErr": "正しいメールアドレスを入力してください", "addReceiver.emailFormatErr": "正しいメールアドレスを入力してください", "addReceiver.enterpriseNameErr": "正確な会社名を入力してください", "addReceiver.userNameFormatErr": "正確な氏名を入力してください", "addReceiver.riskCues": "リスクに関して", "addReceiver.riskCuesMsg": "もしも契約者が実名署名をせず、文書において紛糾が発生した場合、署名者の身分を証明する証拠を自分で用意する必要があります。リスクを避ける必要がある場合、「実名が必要です」を選択してください。", "addReceiver.confirmBtnText": "「実名が必要です」を選択", "addReceiver.cancelBtnText": "「実名は必要ありません」を選択", "addReceiver.attachLengthErr": "お客様は1人の署名者に対し、最大50件の添付ファイル要求しか追加できません", "addReceiver.collapse": "折りたたむ", "addReceiver.expand": "展開する", "addReceiver.delete": "削除", "addReceiver.saySomething": "入力してください", "addReceiver.addImage": "ファイルを追加", "addReceiver.addImageTips": "word、excel、pdf、画像に対応しています。署名前にプレビューできます。最大{num}件までです。", "addReceiver.addSourceFile": "ソースファイルを追加", "addReceiver.addSourceFileTips": "word、excel、pdfに対応しています。プレビューできないため、閲覧するにはダウンロードする必要があります。word、excel、pdfの形式のままでダウンロードするので、ダウンロードしたファイルを編集できます。最大{num}件までです。", "addReceiver.addFile": "圧縮ファイルを追加", "addReceiver.addFileTips": "zipファイルに対応しています。圧縮ファイルのハッシュ値は契約締結証明書に記録されます。サイズは{size}MB以内、1件まで。", "addReceiver.addFileTipsApproval": "zipファイルに対応しています。サイズは{size}MB以内、1件まで。", "addReceiver.canDownload": "ダウンロードを許可", "addReceiver.shouEntry": "入り口を表示", "addReceiver.shouEntryTip": "非表示にすると、送信者がテンプレートを使用して契約書を送信する際に、誤操作を避けるために、ファイル追加のエントリが表示されなくなります。", "addReceiver.itemRequire.1": "オプション項目", "addReceiver.itemRequire.2": "必須項目", "addReceiver.uploadFile": "ファイルをアップロード", "addReceiver.selectFile": "ファイルを選択", "addReceiver.confirm": "確定", "addReceiver.emptyFile": "選択できるファイルがありません、ファイルをアップロードしてください", "addReceiver.pleaseSelectOne": "ファイルを選択してください", "addReceiver.keepSourceFiles": "ソースファイルを保持", "addReceiver.keepSourceTip": "初期設定ではPDFに変換されてプレビューできます。ソースファイルを保持を選択すると、その後アップロードしたファイルは変換されず、審査者はダウンロードしないとファイルを見ることができません", "addReceiver.give": "先", "addReceiver.fileMax": "アップロード数量が上限を超えています", "addReceiver.noReceivers": "契約の署名者が指定されていません。企業または個人の署名者をこのページで設定してください。", "addReceiver.needStamp": "企業署名要件：企業署名を設定後、契約企業{entName}は押印方式の署名役割も設定する必要があります。", "addReceiver.role": "役割：", "addReceiver.skip": "わかりました", "addReceiver.toSetting": "設定へ", "addReceiver.signerLimit": "現在のバージョンは{limit}個を超える相対的署名/副本発信先をサポートしていません。", "addReceiverGuide.usageGuide": "ガイドを使用する", "addReceiverGuide.guideTitle": "新しい署名者を追加する方法", "addReceiverGuide.receiverType": "署名者が契約に参加する方法を選択する必要があります (6 つのうちの 1 つを選択してください)：", "addReceiverGuide.asEntSign": "企業を代表してサインオンします：", "addReceiverGuide.sealSub": "署名者は、契約書に公印または契約書用特別印鑑等を押印する必要があります", "addReceiverGuide.signatureSub": "法人または役員が、企業に代わって契約に署名します。企業は、署名者が契約を閲覧できないように契約を譲渡する権利を有します", "addReceiverGuide.vipOnly": "プレミアムバージョンが利用可能", "addReceiverGuide.stampSub": "署名者は、印鑑を押すだけでなく、企業を代表して署名する必要があります", "addReceiverGuide.confirmSeal": "企業を代表して業務用チェックスタンプを使用する", "addReceiverGuide.confirmSealSub": "財務諸表や確認書などの書類は、最初に確認されてから押印されます", "addReceiverGuide.asPersonSign": "個人に代わって署名するには:", "addReceiverGuide.asPersonSignTip": "ビジネスを代表するものではなく、個人のみを代表して署名されています", "addReceiverGuide.asPersonSignDesc": "ローン契約、参入および退出などの署名者の私的な契約", "addReceiverGuide.scanSign": "コードをスキャンして署名する", "addReceiverGuide.scanSignDesc": "契約書を発行する際に署名者を書く必要はありません. 契約書が発行された後、誰でもコードをスキャンするか、検査ページのリンクをクリックして署名することができます. 物流書類の受領シナリオに適用できます", "addReceiverGuide.selectSignTypeTip": "最初に署名者が契約に参加する方法を選択してください", "addReceiverGuide.howToConfigSignType": "より多くの署名方法を設定するには?", "addReceiverGuide.entConfigSignType": "契約者が企業の場合、以下の方法を設定できます：", "addReceiverGuide.personConfigSignType": "契約者が個人の場合、以下の方法が設定できます：", "addReceiverGuide.howConfig": "設定方法:", "addReceiverGuide.howConfigTip1": "1. まず、契約者ページの「＋契約企業追加」または「契約個人追加」をクリックします。", "addReceiverGuide.howConfigTip2": "2. 右上隅にある [スタンプ] または [署名] をクリックすると、ポップアップ ウィンドウでさらに方法を選択できます。", "addReceiverGuide.signRoleNote": "署名の役割を設定する署名者の役割は何ですか?", "addReceiverGuide.baseFunction": "基本能力：", "addReceiverGuide.baseUsage1": "契約で署名者の役割を見つける方が便利です。連絡先情報を入力し、役割名に従って署名位置を見つけることができます。", "addReceiverGuide.baseUsage2": "異なる署名役割は異なるシールを使用し、契約は複数のシールを指定できます", "addReceiverGuide.baseUsage3": "署名役割に応じて契約送信料金の支払者を選択することができます", "addReceiverGuide.advanceFunction": "高度な使い方:", "addReceiverGuide.advanceUsage1": "署名ロールのステータス (既読だが署名されていないなど) に応じて、契約の進行状況を追跡するためのクイック エントリを設定します。", "addReceiverGuide.advanceUsage2": "開発者は、コントラクト ロール フィールドを使用して API を呼び出す必要があります。", "addReceiverGuide.advanceUsage3": "契約のキャンセルなどの高度な機能には、契約の役割フィールドが必要です。", "addReceiverGuide.advanceUsage4": "署名者の役割に応じてシールパターンを指定できます。", "addReceiverGuide.advanceUsage5": "署名ロールに応じて自動署名を許可するかどうかを設定できます。", "addReceiverGuide.stepTip1": "クリックした後、ドロップダウン ボックスで、契約当事者が契約に参加する方法を調整することもできます。", "addReceiverGuide.stepTip2": "ここでは、契約における契約締結者の「署名ロール」を設定してください。", "addReceiverGuide.stepTip3": "この契約当事者を追加する必要がない場合は、ここをクリックして削除してください。", "templateReceiverConfig.err.setCurrentSender": "現在の発信者を指定してください", "templateReceiverConfig.err.atLeastOneReceiver": "少なくとも契約者1つを追加", "templateReceiverConfig.err.atLeastOneValidReceiver": "少なくとも使用可能な契約者1名を追加", "templateReceiverConfig.err.atLeastOneZhReceiver": "中国のプラットフォームに追加されていない署名者 ", "templateReceiverConfig.err.atLeastOneJaReceiver": "国際的なプラットフォームに追加されていない署名者 ", "templateReceiverConfig.err.atLeastOneNecessary": "担当者の氏名と身分証番号のどちらか一つを必ず記入しなければなりません", "templateReceiverConfig.err.signaturesCountLarge": "担当者の身分チェック数は少なくとも企業自署の署名位置の数量よりも少なくてはいけません", "templateReceiverConfig.err.signaturesCountLess": "担当者の身分チェック数は多くても受け取るアカウント総数よりも多くてはいけません", "templateReceiverConfig.err.batchMultiUseConflict": "契約当事者の一括追加の場合、企業の複数自署を同時に使用できません", "templateReceiverConfig.err.idNumber": "正確な身分証番号を入力してください", "templateReceiverConfig.err.idNumberNotEmpty": "身分証番号は空欄にできません", "templateReceiverConfig.err.idNumberWrong": "正確な身分証番号を入力してください", "templateReceiverConfig.err.accountNotEmpty": "アカウントは空欄に出来ません", "templateReceiverConfig.err.accountWrong": "正しいメールアドレスを入力してください", "templateReceiverConfig.err.userNameNotEmpty": "氏名は空欄に出来ません", "templateReceiverConfig.err.userNameWrong": "正確な氏名を入力してください", "templateReceiverConfig.err.receiverWrong": "署名者の情報が正しくありません。エラーメッセージ（通常は赤色の箇所）を確認し、指示に従って修正して再度実行してください。", "templateReceiverConfig.err.atLeastOneSigner": "少なくとも署名者1名を追加", "templateReceiverConfig.err.atLeastOtherOpertar": "跨境合同须设置跨平台的签约方", "templateReceiverConfig.err.attachNotEmpty": "付属書類名を入力してください", "templateReceiverConfig.err.onlyOnProxyOrMultiAccounts": "同一个签署顺序中的企业，最多只有一个签约角色能使用前台代收或多账号接收合同的功能。您需要勾选“按顺序签署”，并将他们设置为不同的签署顺序。", "templateReceiverConfig.err.saveEditWrong": "アカウント情報の誤り、フィールド内容の空欄もしくはExcelフォーマットの誤りがあるため、続けることができません", "templateReceiverConfig.err.roleNameNotEmpty": "契約の役割は空欄にできません", "templateReceiverConfig.err.roleNameNotRepeat": "契約の役割は重複できません", "templateReceiverConfig.err.importSignerTip": "契約者をインポートしていません", "templateReceiverConfig.err.importPicTip": "画像をインポートしていません", "templateReceiverConfig.err.importFail": "インポートに失敗しました", "templateReceiverConfig.err.errorInfoNote": "(下の表のスクロール バーをドラッグすると、赤でマークされたエラーの説明が表示されます)", "templateReceiverConfig.err.lost": "不備：{name};", "templateReceiverConfig.err.ddlTooLittle": "署名の時効は15分以内です", "templateReceiverConfig.err.encryptionSignPasswordLimit": "署名コードは4～8桁の数字です", "templateReceiverConfig.tip.moreThanTipPre": "页面仅展示Excel表中前500条，更多合同格式检查结果需", "templateReceiverConfig.tip.download": "下载文件", "templateReceiverConfig.tip.moreThanTipEnd": "至本地查看", "receiverItem.setNoticelang": "署名通知の言語設定", "receiverItem.limitFaceConfigTip": "契約単価が低いため、この機能は利用できません。ベストサインにご連絡ください", "receiverItem.mutexMsg": "「{msg}」を設定済みです。先に「{msg}」の設定を削除した後再選択してください", "receiverItem.batchImported": "一括インポート済み", "receiverItem.batchNotImported": "一括インポート待ち", "receiverItem.batchCheckbox": "一括送信可能", "receiverItem.importedNum": "{batchNum}人/社の契約者のインポート完了", "receiverItem.checkTooltip": "図解を表示", "receiverItem.authDropdownTooltip": "署名者に対する身分の検証", "receiverItem.signTypeTooltip": "押印または共有", "receiverItem.moreConfigTooltip": "署名の詳細を設定", "receiverItem.caseDlgTitle": "一括インポート機能使用時の署名者設定", "receiverItem.clickView": "確認をクリック", "receiverItem.entName": "法人名", "receiverItem.entNameTips": "*登録済み法人名と完全に一致している法人名", "receiverItem.userName": "担当者", "receiverItem.userNamePlace": "氏名（{necessary}）", "receiverItem.userAccount": "携帯番号/メールアドレス", "receiverItem.userAccountJa": "メールアドレス", "receiverItem.userAccountDemand": "（最大10個、セミコロンで区切る）", "receiverItem.proxy": "相手側受付での受け取り", "receiverItem.addressBookTooltip": "アドレス帳", "receiverItem.proxyTips": "相手方の具体的なアカウントを知らないか相手方がアカウントを持っていません。受付での代理受け取りを選択してください", "receiverItem.dai": "代", "receiverItem.name": "氏名", "receiverItem.IDCard": "身分証明書番号", "receiverItem.IDCardPlace": "契約身分の確認で用います（{necessary}）", "receiverItem.addressBook": "アドレス帳", "receiverItem.role": "契約上の役割", "receiverItem.rolePlace": "例：従業員/販売業者であれば", "receiverItem.roleTooltip": "契約者の押印場所や入力フィールドの設定に使用します。", "receiverItem.byAddressBook": "お客様の「連絡者アドレス帳」から持ち出し", "receiverItem.error.userAccountLessThan": "携帯電話/受信メールアドレスは{num}箇所を超えてはなりません", "receiverItem.error.userAccountLessThanJa": "受信メールアドレスは{num}箇所を超えてはなりません", "receiverItem.error.userAccountNotRepeat": "携帯電話/受信メールアドレスは重複できません", "receiverItem.error.userAccountNotRepeatJa": "受信メールアドレスは重複できません", "receiverItem.error.entNameLessThan": "企業名は{num}文字以下にしてください", "receiverItem.error.signerInContract": "この契約者を契約書に入れる", "receiverItem.error.signerNotInContract": "この契約者を契約書に入れない", "receiverItem.error.userInSameCompany": "同じ企業の担当者を選択してください", "receiverItem.invite": "協力パートナーを招待する", "receiverItem.addFromBook": "アドレス帳から選択", "receiverItem.userNameToolTip.0": "经办人姓名仅用于您后期的管理和统计，不会用于校验签署经办人的姓名是否一致。", "receiverItem.userNameToolTip.1": "如需校验， 您需要启用“ 经办人身份核验” 功能。", "receiverItemHeader.editor": "補完する", "receiverItemHeader.contractDownloadControl": "契約書ダウンロードコード", "receiverItemHeader.signerPerson": "契約者（個人）", "receiverItemHeader.signerEnt": "契約者（法人）", "receiverItemHeader.needAuth": "実名が必要です", "receiverItemHeader.notNeedAuth": "実名は必要ありません", "receiverItemHeader.needAuthEnt": "担当者は実名が必要です", "receiverItemHeader.notNeedAuthEnt": "担当者の実名は必要ありません", "receiverItemHeader.sign": "押印", "receiverItemHeader.entSign": "企業の自署", "receiverItemHeader.stamp": "押印", "receiverItemHeader.stampSign": "捺印及び自署", "receiverItemHeader.requestSeal": "業務照合印", "receiverItemHeader.cc": "共有", "receiverItemHeader.scanSign": "自署の読み取り", "receiverItemHeader.signCheck": "署名検証", "receiverItemHeader.messageAndFaceVerify": "顔認証＋認証コードによる検証", "receiverItemHeader.faceFirst": "顔認証が優先で、予備で認証コードの署名になります", "receiverItemHeader.faceMust": "顔認証署名が必須です", "receiverItemHeader.noHand": "ベストサイン署名システムを使用", "receiverItemHeader.mustHand": "手書き署名が必須です", "receiverItemHeader.notify": "契約注意事項", "receiverItemHeader.handwritingRec": "手書きにして手書き筆跡識別を有効にしなければなりません", "receiverItemHeader.readAll": "閲読完了後再署名する", "receiverItemHeader.dataCollect": "資料の収集", "receiverItemHeader.attachDoc": "契約書付属資料の追加", "receiverItemHeader.mainDoc": "契約主体資料を提出", "receiverItemHeader.attachDocTips": "署名時に付属資料の提出が必須となります。", "receiverItemHeader.mainDocTips": "署名時に、署名者の主体資質に関する資料の提出が必須となります。提出された資料は、ファイルキャビネットに保存されます。既に提出済みの場合は、再提出の必要はありません。", "receiverItemHeader.scanSignTip": "发合同时不需要写签署人，发出后任何人扫码/点查验页链接都可签署，适用物流单据收货场景。", "receiverItemHeader.other": "その他", "receiverItemHeader.notifyOff": "SMS/メールでの通知を行わない", "receiverItemHeader.notifyOffJa": "メールでの通知を行わない", "receiverItemHeader.notifyForeign": "外国語による通知を使用", "receiverItemHeader.notifyForeignTips": "外国語の通知はSMSに対応しておらず、メールのみ対応しています", "receiverItemHeader.signerPay": "支払い者", "receiverItemHeader.signerPayDesc": "この機能は自動署名をサポートせず、紙媒体での場面", "receiverItemHeader.more": "詳細設定", "receiverItemHeader.ddl": "署名期間", "receiverItemHeader.encryptionSign": "署名コードを使用して署名", "receiverItemHeader.signerAuthCheck": "经办人身份核验", "receiverItemHeader.twoFactorAuthentication": "二要素認証を利用", "sendedEdit.editorNameTip": "与发件方企业保持一致", "sendedEdit.completeDescription.0": "契約の一部の内容を保留にしておき、契約の送信後に現在のアカウントで追加することができます。契約情報追加の実行者は捺印や署名を行わず、送信者の企業のメンバーである必要があります。関与する人数が多い業務シーンでの使用に適しています。", "sendedEdit.completeDescription.1": "查看设置方法", "sendedEdit.completeAttachment": "添付文書の追加：契約情報追加の実行者が必ず先頭であり、テンプレートに事前に契約添付文書フィールドが設定されている必要があります。", "sendedEdit.completeOther": "その他の追加：契約締結の注意事項、契約内容フィールド、契約者個人のアカウント等、追加する内容にチェックを入れ選択してください。設定方法を表示。", "sendedEdit.completeMustSignOrderTip": "请先勾选“顺序签署”，且合同补全执行人的顺序不能是最后一位", "sendedEdit.sendedEditMustSignByOrder": "当前存在补全角色，补全功能要求必须启用“顺序签署”", "sendedEdit.name": "发送后补全签署人", "sendedEdit.sendedEditDesc": "发件人在发送合同时可以不填写该签约角色的账号信息，如果填写了账号信息，也需由补全合同信息的执行人账号确认后才生效。", "sendedEdit.howToPrepare": "设置方法", "sendedEdit.prepareContentTip": "签约角色A作为合同补全执行人不需要补齐任何字段或账号，不符合“发出合同再补全”功能的用法。您可以直接删除签约角色A。", "sendedEdit.configGuideTip": "可在合同上预留合同内容字段或签约个人的账号，在合同发出后再由签约角色{role}补全。", "sendedEdit.viewConfigGuide": "查看设置方法", "sendedEdit.prepareLabels": "合同预留内容字段：", "sendedEdit.prepareLabelsDesc": "编辑模板时，将内容填写人设置为发件人，并选择由哪位签约角色补全。", "sendedEdit.prepareRoles": "合同预留签约个人：", "sendedEdit.prepareSignerDesc": "1. 在设置签约方页面，添加签约个人后勾选发送后补全签署人。", "sendedEdit.prepareCompletorDesc": "2. 选择该签约方信息由哪位签约角色补全。", "sendedEdit.batchImportConflictEditorTip": "执行补全操作的签约角色不支持批量添加。将其切换为“盖章”等其他合同参与方式后才可勾选“使用时可批量添加”", "sendedEdit.batchImportConflictEmptyRoleTip": "待发送后补全的账号不支持批量添加。取消“发送后补全签署人”后，才可勾选“使用时可批量添加”", "sendedEdit.editorConfigFirstTip": "在待补全信息的签约个人签署顺序前，需存在一个补全合同的执行人", "sendedEdit.senderFill": "发送时填写", "sendedEdit.fillByRole": "由签约角色\"{role}\"填写", "sendedEdit.editorCompleteInfo": "请补全以下信息", "sendedEdit.completeBy": "由签约角色", "sendedEdit.complete": "补全", "receiverItemExtends.twoFactorAuthenticationTips": "当該署名者は、署名するために二要素認証を完了する必要があります", "receiverItemExtends.encryptionSignTips": "署名コードを利用します(署名時に署名コードを入力して署名することになります。)", "receiverItemExtends.encryptionSignCode": "署名コード", "receiverItemExtends.pleaseInput": "4～8桁の数字を入力してください", "receiverItemExtends.contractDownloadControl": "ダウンロードコードを有効にする", "receiverItemExtends.contractDownloadControlTips": "契約者が契約書をダウンロードする際、ダウンロードコードを入力しなければなりません。発信者は契約詳細ページの中でダウンロードコードを確認することができます。", "receiverItemExtends.messageAndFaceVerify": "顔認証＋認証コードによる検証", "receiverItemExtends.messageAndFaceVerifyTips": "顔認証と認証コードの検証が終わらないと契約書に署名できません。顔認証署名を完了するには、実名認証が必要です。中国大陸の住民のみ利用可能です", "receiverItemExtends.faceFirst": "顔認証が優先で、予備で認証コードの署名になります", "receiverItemExtends.faceFirstTips": "署名の際にシステムは顔スキャン認証を優先的に推奨し、顔スキャン認証に失敗すると認証コードによる認証を行います", "receiverItemExtends.faceMust": "顔認証署名", "receiverItemExtends.faceMustTips": "この使用者が署名を完了するには、顔認証の検証が必要です", "receiverItemExtends.faceApplicable": "适用人群", "receiverItemExtends.faceMustTooltip.1": "刷脸签署只支持：", "receiverItemExtends.faceMustTooltip.2": "1、大陆居民（身份证）", "receiverItemExtends.faceMustTooltip.3": "2、持有港澳居民通行证的港澳居民", "receiverItemExtends.faceMustTooltip.4": "3、持有外国人永久居留身份证的外国居民", "receiverItemExtends.faceMustTooltip.5": "4、定居国外的中国公民（使用国内护照）", "receiverItemExtends.faceMustTooltip.6": "其他用户暂不支持，将默认进行验证码校验签署。", "receiverItemExtends.faceMustTooltip.7": "上述1证件以外的刷脸方式只支持微信H5刷脸，", "receiverItemExtends.faceMustTooltip.8": "如您配置了必须支付宝刷脸，则2、3、4证件用户将默认验证码校验签署。", "receiverItemExtends.noHand": "ベストサイン署名システムを使用", "receiverItemExtends.noHandTips": "この使用者はベストサインのシステムでの署名だけが選択できます", "receiverItemExtends.mustHand": "手書き署名が必須です", "receiverItemExtends.mustHandTips": "この使用者は手書きサインで署名を完了する必要があります", "receiverItemExtends.useScanCodeClaim": "二次元コードでの受領を有効にする", "receiverItemExtends.scanCodeClaimTip": "ここをチェックをすると、契約書の受付での受け取りに対して、契約者は二次元コードでのみ受領でき、その他の方法では契約書を受領できなくなります。チェックコードは契約書を発信してから、チェックコードインターフェイスもしくは契約詳細ページを通じてチェックコードをダウンロードし、対応する受領者に自発的に通知します。（受信者は契約詳細もしくは契約書内容ページでチェックコードを確認でき、ご自身で読み取ると受領が完了します。）", "receiverItemExtends.notifyLabel.1": "契約注意事項", "receiverItemExtends.notifyLabel.2": "（255文字以内）", "receiverItemExtends.notifyLabel.3": "送信後に契約注意事項を補足", "receiverItemExtends.handwritingRec": "手書きにして手書き筆跡識別を有効にしなければなりません", "receiverItemExtends.handwritingRecTips": "この使用者の手書きの氏名と発信者指定もしくは実名情報の中の氏名と比較し、一致した段階で署名が完了します", "receiverItemExtends.readAll": "閲読完了後再署名する", "receiverItemExtends.signerPay": "支払い者", "receiverItemExtends.signerPayTip": "本契約は当該受信者が支払います", "receiverItemExtends.readAllTips": "閲読完了後再署名する", "receiverItemExtends.attachDoc": "契約書付属資料の追加", "receiverItemExtends.attachDocTip": "（各ファイルデータは10M以内とします）", "receiverItemExtends.mainDoc": "契約主体資料", "receiverItemExtends.notifyOff": "SMS通知をしない", "receiverItemExtends.notifyOffTips": "有効にすると、この契約者は契約通知を受け取れなくなります（無効にしたときに初期設定で送信）", "receiverItemExtends.notifyForeign": "外国語による通知を使用", "receiverItemExtends.notifyForeignTips": "すべての契約書先に外国語で通知を送信します,", "receiverItemExtends.English": "英語", "receiverItemExtends.Japanese": "日本語", "receiverItemExtends.Chinese": "中国語", "receiverItemExtends.Arabic": "アラビア語", "receiverItemExtends.dataName": "資材名", "receiverItemExtends.dataType": "資料の種類", "receiverItemExtends.remarks": "備考", "receiverItemExtends.itemRequire.1": "オプション項目", "receiverItemExtends.itemRequire.2": "必須項目", "receiverItemExtends.addressLine": "収集された住所の詳細レベル（必ず記入項目）", "receiverItemExtends.require": "必須項目", "receiverItemExtends.notRequire": "オプション項目", "receiverItemExtends.addressCheckbox.province": "都道府", "receiverItemExtends.addressCheckbox.city": "辖区", "receiverItemExtends.addressCheckbox.area": "区", "receiverItemExtends.addressCheckbox.detail": "詳細な住所（例：通り名、番地など）", "receiverItemExtends.storeTypeList.0": "文字資料", "receiverItemExtends.storeTypeList.1": "画像資料", "receiverItemExtends.storeTypeList.2": "一選択資料", "receiverItemExtends.storeTypeList.3": "複数選択資料", "receiverItemExtends.storeTypeList.4": "ドキュメント資料", "receiverItemExtends.storeTypeList.5": "日付資料", "receiverItemExtends.storeTypeList.6": "住所資料", "receiverItemExtends.storeTypeList.7": "", "receiverItemExtends.storeTypeList.8": "数値資料", "receiverItemExtends.storeTypeList.9": "金額資料", "receiverItemExtends.storeTypeList.10": "表单资料", "receiverItemExtends.boxContent.dataBox": "アーカイブス", "receiverItemExtends.boxContent.basicDataCollection": "基本データの収集", "receiverItemExtends.boxContent.customDataCollection": "資料収集のユーザー設定", "receiverItemExtends.boxContent.personalRealName": "個人実名認証", "receiverItemExtends.boxContent.selectRequire": "（必須項目）", "receiverItemExtends.boxContent.applyForAuthorization": "授権の取得申請", "receiverItemExtends.boxContent.entAuthorizationList.entAuth": "企業実名認証", "receiverItemExtends.boxContent.entAuthorizationList.seal": "社印（代表者署名用）", "receiverItemExtends.boxContent.entAuthorizationList.send": "契約代行", "receiverItemExtends.notify.title": "有効にすると、この契約者は契約通知を受け取れなくなります（無効にしたときに初期設定で送信）", "receiverItemExtends.notify.explain.1": "固定した契約者に送信せず、契約者が変更しても影響を受けません", "receiverItemExtends.notify.explain.2": "すべての契約書先に発信しません", "receiverItemExtends.attach.dataName": "資材名", "receiverItemExtends.attach.dataType": "資料の種類", "receiverItemExtends.attach.imgFile.name": "画像資料", "receiverItemExtends.attach.imgFile.support": "png/jpg/jpegをサポート", "receiverItemExtends.attach.imgFile.eg": "例：身分証写真", "receiverItemExtends.attach.imgFile.holderText": "例：本人の身分証写真をアップロードしてください（オプション）", "receiverItemExtends.attach.docFile.name": "ドキュメント資料", "receiverItemExtends.attach.docFile.support": "pdf/excel/word/txt/zip/xmlをサポート", "receiverItemExtends.attach.docFile.eg": "例：健康診断書", "receiverItemExtends.attach.docFile.holderText": "例：健康診断書をアップロードしてください（オプション）", "receiverItemExtends.attach.remake": "備考", "receiverItemExtends.attach.addData": "資料の追加", "receiverItemExtends.attach.error.dataNotEmpty": "資料名は空欄にできません。", "receiverItemExtends.attach.error.attachNameNotSame": "契約付属資料名称は同じものにできません", "receiverItemExtends.attach.collapse": "折りたたむ", "receiverItemExtends.attach.expand": "展開する", "receiverItemExtends.attach.delete": "削除", "receiverItemExtends.auth.new": "追加", "receiverItemExtends.auth.newLimit": "（最多5個）", "receiverItemExtends.auth.field.name": "担当者名", "receiverItemExtends.auth.field.id": "担当者身分証番号", "receiverItemExtends.auth.placeholder.name": "氏名が一致してからでない代表企業署名ができません（オプション）", "receiverItemExtends.auth.placeholder.nameRequire": "氏名が一致してからでない代表企業署名ができません（必須）", "receiverItemExtends.auth.placeholder.id": "身分証が一致してからでない代表企業署名ができません（オプション）", "receiverItemExtends.auth.placeholder.idRequire": "身分証が一致してからでない代表企業署名ができません（必須）", "receiverItemExtends.auth.placeholder.nameRule": "氏名が一致しない場合は、企業の名義として署名することができません(署名者の身分証明書の通りに氏名を記入する必要があります)。", "receiverItemExtends.auth.tips.entSignature": "この担当者は個人署名（対応する個人CA証明書）を使用して署名を完了しており、捺印する必要はありません。ただしこの企業は追加で捺印者を指定する必要があります。", "receiverItemExtends.auth.tips.stampSignature": "企業の印章を使用して署名する際、同時に追加する個人サインで署名を完了する必要があります。署名前に個人実名認証を完了する必要があります", "receiverItemExtends.auth.checkboxLabel.onlyStamp": "手続き担当者の身分認証を有効有効にすると、担当者は実名署名が必要です", "receiverItemExtends.auth.checkboxLabel.onlyStampTip": "氏名も身分証明書も記入していない場合は、担当者に実名認証が求められるが、担当者がどなたかチェックしません。", "receiverItemExtends.auth.checkboxLabel.withSignature": "担当者の身分認証を有効にする（企業自署は元々担当者の実名が必要ですが、氏名もしくは身分証にチェックして入力すると、署名時効時に氏名もしくは身分証が完全に一致しているかどうかチェックします）", "receiverItemExtends.auth.checkboxLabel.useMulti": "企業の複数人自署を有効にする：", "receiverItemExtends.auth.checkboxLabel.sigNum.0": "企業署名の署名位置を", "receiverItemExtends.auth.checkboxLabel.sigNum.1": "設定します", "receiverItemExtends.auth.checkboxLabel.showHideTip": "非表示にすると、送信者がテンプレートを使用して契約を送信する時に、この設定項目が表示されなくなり、誤操作を避けることができます。", "receiverItemExtends.auth.checkboxLabel.showThis": "显示此项", "receiverItemExtends.auth.checkboxLabel.hideThis": "隐藏此项", "receiverItemExtends.auth.error.atLeast": "担当者の氏名と身分証番号のどちらか一つを必ず記入しなければなりません", "receiverItemExtends.ddl": "署名期間", "receiverItemExtends.ddlDesc.0": "契約を受け取った後、", "receiverItemExtends.ddlDesc.1": "天", "receiverItemExtends.ddlDesc.2": "時間", "receiverItemExtends.ddlDesc.3": "分以内にご署名ください。署名いただけない場合は、契約のステータスが「期限切れ」に変更されます。署名に期限が設けられている署名者は、期限が迫っていることを自動的に通知されなくなります", "receiverItemExtends.scanSign.tip": "发合同时不需要写签署人，发出后任何人扫码/点查验页链接都可签署，适用物流单据收货场景", "receiverItemExtends.scanSign.notValidateSignVerificationCode": "只在登录时校验验证码，无需签署校验", "receiverItemExtends.authCheckMove.tip": "注意：原先此处\"经办人身份核验\"功能已移动至右侧更多菜单中，请在更多菜单中进行操作。", "dataBoxInvite.title": "協力パートナーを招待する", "dataBoxInvite.step1": "お客様の協力パートナーとのリンクを共有し、事前に企業を構築", "dataBoxInvite.step2": "リンク/二次元コードの授権後の協力パートナーがアドレス帳に表示されます", "dataBoxInvite.step3": "「ファイル＋」の中で協力パートナーをさらに管理します", "dataBoxInvite.imgName": "採集した二次元コードを共有", "dataBoxInvite.saveQrcode": "二次元コードをローカルに保存", "dataBoxInvite.copy": "コピー", "dataBoxInvite.copySuccess": "コピー完了", "dataBoxInvite.copyFailed": "コピー失敗", "selectSender.addReceiver": "契約者を追加してください", "selectSender.currentSender": "送信者：", "selectSender.toBeDetermined": "【確定待ち】", "selectSender.tips.0": "このテンプレートは代行授権が承認されており、、発行された各契約書は権限を持つ契約署名者の企業名で表示されます。", "selectSender.tips.1": "同一契約で複数の権限のある署名企業がある場合、代行契約として扱われません。", "selectSender.multiSenderSelectTip": "複数の業務もしくは企業をお持ちの場合、ここで異なる身分に切り替えて契約書を発信できます", "selectSender.sendEnt": "送信者企業", "selectSender.sendEntTip": "選択可能な送信者企業の範囲：（1）テンプレートの「契約送信」権限がアカウントに付与された企業。（2）アカウントが「グループ管理コンソール-グループ管理権限」で管理されている企業。", "selectSender.sendContractNoPermissionTip": "このテンプレートを使用して、{entName}会社として契約書を送信するには、このテンプレートの「契約送信」権限が必要です。テンプレートの作成者またはテンプレートを割り当てる権限を持つメンバーに連絡してください。", "selectSender.viewTempaltePermission": "このテンプレートでの私の権限を表示する", "selectSender.noSendPermission": "まだこの企業に送信する権限がありません", "upload.addBlank": "アップロード待ちファイルの追加", "upload.addBill": "証票契約の追加", "upload.addWordBill": "Word契約書を追加", "upload.exampleAsFollows": "以下例を参照：", "upload.wordBillTip1": "Word形式の文書をサポートし、入力内容に基づいて新しい書類を動的に生成します。|送信者または署名者の入力フィールドは、文書内で@があるかどうかで定義を区別します", "upload.wordBillTip2": "アップロードするWordファイルには、事前に位置決めフィールド（フィールド名はカスタマイズ可能）を追加する必要があります", "upload.billTip1": "インターフェイスのインポート内容に基づいて動的に新しい証票を構成するエクセル型ファイルをサポートしています。| アップロードしたExcelは事前にフィールドの位置決め（フィールド名はユーザー設定）を追加し、契約書インターフェイスのインポートパラメータ名とテンプレートのフィールド名は一致していなければなりません。", "upload.billTip2": "1.基本フィールド、二重括弧のマーカーである{text}を使ってセル（水平方向に入力）にデータを入力します。|　2.詳細フィールド、すなわち#マークの付いた一重括弧である{text}を使用して、マークを使って新しいデータ（新しい行を下に）を自動挿入します。| 3.例：アップロード後、インターフェイスを通じて契約書を発信します。", "upload.newBillTip1": "1. 基本フィールド、データは一つのセルに入力されます（横方向に入力）", "upload.newBillTip2": "2. 明細フィールド、新しいデータを自動的に挿入するために使用されます（下方向に新しい行を追加）", "upload.newBillTip3": "送信者：基本フィールドは{{フィールド名}}で表示；明細フィールドは[フィールド名]で表示", "upload.newBillTip4": "署名者：基本フィールドは{{@フィールド名}}で表示；明細フィールドは[@フィールド名]で表示", "upload.addLocal": "ローカルファイルのアップロード", "upload.wordBillfield": "詳細フィールド：ユーザーが指定した位置に新しいデータを自動挿入（下方向に行追加）、[フィールド名]（角括弧）で指定", "upload.uploadDoc": "ファイルのアップロード", "upload.typeTips": "拡張子.pdf/.doc/.docx/.xls/.xlsx/.jpg/.pngに対応", "upload.dynamicTypeTips": ".doc/.docxフォーマットのアップロードのみサポート", "upload.gamaTypeTips": "サポートしているファイル：PDFと画像。Word、Excel はサポートしていません", "upload.newBlank": "アップロード待ちファイルの新規作成", "upload.modifyTItle": "契約タイトルを変更", "upload.needUpdateTitle": "ドキュメント名を契約タイトルとして使用します。チェックを入れると、新しくアップロードされたドキュメント名が契約タイトルとして使用されます。チェックを入れない場合、設定された「契約タイトル」が使用されます。", "upload.docTitle": "契約タイトル", "upload.inputDocTitle": "契約タイトルを入力してください", "upload.fileLessThan": "{num}M以下のファイルをアップロードしてください", "upload.usePdf": "アップロードする際PDFファイルもしくは画像を使用してください", "upload.useDoc": "アップロードする際DOCファイルを使用してください", "upload.useExcel": "アップロードする際はXLSXもしくはXLSファイルを使用してください", "upload.useOfd": "アップロードする時OFDを使用してください", "upload.fileNameMoreThan": "ファイル名の長さが{num}を超えると、自動で切り取ります", "upload.blankTips": "テンプレート設定時にファイルをアップロードせず、契約書送信時にファイルをアップロードして頂きます。", "upload.createContract": "ファイルを追加", "upload.fileNameTips.0": "ここで設定された「契約タイトル」には2つの役割があります。", "upload.fileNameTips.1": "1. 複数のドキュメントがある場合、各ドキュメントのラベルとして使用でき、ドキュメントを見つけやすくします。", "upload.fileNameTips.2": "2. 契約書を送信する際のデフォルトの契約タイトルとして使用できます。", "upload.fileNameReplace": "ファイルをアップロードしてから、契約書ファイルの表題としてファイル名を付けます。", "localCommon.download": "ダウンロード", "localCommon.cancel": "キャンセル", "localCommon.confirm": "確認", "localCommon.toSelect": "選択してください", "localCommon.seal": "捺印", "localCommon.signature": "署名", "localCommon.signDate": "署名日付", "localCommon.text": "テキスト", "localCommon.date": "日付", "localCommon.datetime": "時刻", "localCommon.qrCode": "二次元コード", "localCommon.number": "デジタル", "localCommon.dynamicTable": "動態テンプレート", "localCommon.terms": "契約条項", "localCommon.checkBox": "チェックボックス", "localCommon.radioBox": "ラジオボタン", "localCommon.image": "画像", "localCommon.confirmSeal": "業務照合印", "localCommon.confirmRemark": "印章不適合の備考", "localCommon.optional": "任意項目", "localCommon.require": "必須項目", "localCommon.tip": "注意", "localCommon.comboBox": "検索候補", "localCommon.yes": "はい", "localCommon.no": "いいえ", "field.approval": "発信前審査", "field.send": "送信", "field.contractDispatchApply": "契約書の送信申請", "field.contractNeedYouSign": "このファイルはお客様の署名が必要です", "field.ifSignRightNow": "すぐに署名しますか", "field.signRightNow": "すぐに署名します", "field.signLater": "後で署名します", "field.signaturePositionErr": "各署名者に署名位置を指定してください", "field.sendSucceed": "送信完了", "field.confirm": "確定", "field.cancel": "キャンセル", "field.qrCodeTips": "署名後読み取り、すぐに署名詳細・署名の有効性及びこの契約書が改ざんされているかどうかの検証を確認できます", "field.pagesField": "第{currentPage}ページ目、計{totalPages}}ページ", "field.suitableWidth": "幅の調整", "field.signCheck": "署名の確認", "field.locateSignaturePosition": "署名位置指定", "field.append": "追加", "field.privateLetter": "メッセージ", "field.signNeedKnow": "契約注意事項", "field.maximum5M": "5MB以下のファイルをアップロードしてください", "field.maximumSize": "{size}<PERSON><PERSON><PERSON>小さい圧縮ファイルをアップロードしてください", "field.uploadServerFailure": "サーバーへのアップロードに失敗しました", "field.uploadFailure": "アップロードエラーです", "field.uploadRepeatFileTip": "同じ名前のファイルは繰り返しアップロードできません", "labels.signTime": "署名日付", "labels.optionLimitTip": "セレクタの数が上限に達しています", "labels.pageLimitTip": "ページ境界の外にあるため、追加できません", "labels.optionName": "{count}オプション", "labels.singerName": "署名者", "labels.fdaDate": "署名時間", "labels.fdaReason": "選択待ち", "labels.sealArea": "捺印所", "labels.designateSeal": "印章を選択する", "labels.cancelSeal": "清除", "labels.changeSeal": "印章を切り替える", "labels.selectableSeal": "選択可能な印章", "labels.noSealList": "署名者には利用可能な印鑑がありません。指定する前に、管理者が印鑑を割り当てる必要があります。", "customLabelEdit.labelName": "名称", "customLabelEdit.require": "必須項目", "customLabelEdit.format": "フォーマット", "customLabelEdit.equalWidth": "リスト等幅", "customLabelEdit.adjustWidth": "幅の自動設定", "customLabelEdit.contentFiller": "内容記入者", "customLabelEdit.senderOnly": "この内容は発信者だけが記入できます", "customLabelEdit.sender": "送信者", "customLabelEdit.senderTip": "契約内容が発信する前に発信者が記入するため、「発信者」を選択します", "customLabelEdit.signer": "署名者", "customLabelEdit.signerTip": "署名者がこの契約内容で署名するときに記入するため、「署名者」を選択します", "customLabelEdit.signatureSize": "署名のサイズ", "customLabelEdit.default": "デフォルト", "customLabelEdit.custom": "カスタマイズ", "customLabelEdit.labelSize": "モニターサイズ", "customLabelEdit.labelWidth": "幅", "customLabelEdit.labelWidthPlaceHolder": "幅を入力してください", "customLabelEdit.labelHeight": "高さ", "customLabelEdit.labelHeightPlaceHolder": "高さを入力してください", "customLabelEdit.autoSystemFill": "システム自動生成", "customLabelEdit.alternativeItem": "オプション", "customLabelEdit.dateFormat": "パターン", "customLabelEdit.labelFontSize": "フォントサイズ", "customLabelEdit.labelFontSizePlaceHolder": "フォントサイズを選択してください", "customLabelEdit.labelAlign": "アライメント方法", "customLabelEdit.labelDescribe": "記載事項説明", "customLabelEdit.labelDescribeTip": "オプション、20文字以下とします", "customLabelEdit.labelRequire": "要件の記入", "customLabelEdit.labelRequireTip": "必須項目です。記入されていない場合発信も署名もできません", "customLabelEdit.labelFillAllRegion": "画像がエリア全体を埋めます", "customLabelEdit.labelFillAllRegionTip": "画像エリアのサイズに応じて自動調整し、画像エリア全体を埋めます", "customLabelEdit.confirm": "確定", "customLabelEdit.cancel": "キャンセル", "customLabelEdit.defaultValue": "デフォルト値として設定", "customLabelEdit.selectDateDefaultValue": "日付を選択します", "customLabelEdit.messageTip.nameError": "名称を入力してください", "customLabelEdit.messageTip.itemError": "オプションを入力してください", "customLabelEdit.messageTip.itemSameError": "似ているオプションは選択できません", "customLabelEdit.messageTip.itemRegError": "オプション名は中国語/英語/数字の組み合わせのみです", "customLabelEdit.messageTip.widthError": "幅は28以上の値で入力してください", "customLabelEdit.messageTip.widthMaxError": "幅は{width}以内の値で入力してください", "customLabelEdit.messageTip.heightError": "高さは20以上の値で入力してください", "customLabelEdit.messageTip.heightMaxError": "高さは{height}以内の値で入力してください", "customLabelEdit.messageTip.markOptionValueTip": "オプション名には特殊文字\\/#@()を含むことができません", "customLabelEdit.messageTip.hasEnComma": "選択肢の名称に英語のカンマを入力することはできません", "customLabelEdit.messageTip.cannotHasEnComma": "The name of the optional item cannot contain English commas.You need to re-edit the template and remove the English commas", "customLabelEdit.messageTip.wordNumError": "フィールド内容字数を入力してください", "customLabelEdit.messageTip.overCountLimit": "500件までのオプションに対応しています", "customLabelEdit.messageTip.numberDefaultInteger": "整数を入力してください", "customLabelEdit.messageTip.numberDefaultError": "数字を入力してください。{decimalPlace}位の小数点が入力できます", "customLabelEdit.defaultValueTip": "先にあるチェックボックスをオンにすると、初期値として設定されます", "customLabelEdit.addOption": "オプションの追加", "customLabelEdit.batchAddOption": "一括追加オプション", "customLabelEdit.selectTermType": "条項タイプを選択してください", "customLabelEdit.wordNum": "内容字数", "customLabelEdit.wordNumTip": "フィールド内容の事前設定幅を計算するのに用いられ、フィールド内容字数には制限がなく初期設定は5です。UI部分からはみ出したものは切り詰められます", "customLabelEdit.location": "座標位置", "customLabelEdit.xLocation": "左側から", "customLabelEdit.yLocation": "上部から", "customLabelEdit.integer": "整数", "customLabelEdit.decimalLimit": "限制", "customLabelEdit.decimal": "位小数", "customLabelEdit.numberFormat": "格式", "customLabelEdit.formatValid": "書式検証", "customLabelEdit.noFormat": "書式検証なし", "customLabelEdit.idCard": "中国本土居民身分証", "customLabelEdit.phoneNumber": "11桁の携帯電話番号", "customLabelEdit.formatValidTip": "入力内容が書式要件を満たしていない場合、署名者は提出できません。", "customLabelEdit.labelDescribeTooltip": "契約書に署名する際、署名者が「記入説明」の内容を確認して、記入すべき内容を理解することができます。", "labelEdit.ridingSealSetTip": "設定後はすべての割印に適用されます", "labelEdit.ridingConfig": "各ページに割印を押す。片面印刷に適しています。ドキュメントのページ数は2ページ以上必要、さもなければ署名完了後に割印が表示されません。 | 奇数ページのみに割印を押す。両面印刷に適しています。ドキュメントのページ数は3ページ以上必要、さもなければ署名完了後に割印が表示されません。", "labelEdit.advancedSettings": "詳細設定", "labelEdit.receiver": "受信先", "labelEdit.info.0": "ご注意ください：", "labelEdit.info.1": " 1)テンプレート内の複数の場所に同じ業務フィールドが追加されている場合、記入者は一度だけ記入し、同じ値として保存すればよいです。", "labelEdit.info.2": " 2)属性設定は、現在のテンプレート内の同じ名前を持つすべての業務フィールドを同期的に更新します", "labelEdit.info.3": "注：署名者は印章不適合を選択した後にのみ記入する必要があります", "labelEdit.wordbillLabel.selectType": "フィールドタイプを選択", "labelEdit.sealSyncPosition.title": "シール同期位置", "labelEdit.sealSyncPosition.description": "説明", "labelEdit.sealSyncPosition.closeTip": "「署名位置同期」をオフにしてよいですか？", "labelEdit.sealSyncPosition.tipContent": "オフにすると、今回の契約書送信では、「署名位置同期」を再びオンにすることができなくなります（「署名位置同期」を再びオンにするには、契約書の送信をやり直す必要があります）。", "labelEdit.sealSyncPosition.noteText": "注：それぞれの署名位置は独立した設定で、互いに影響を与えません。", "labelEdit.sealSyncPosition.funDescTitle": "署名位置同期機能の説明", "labelEdit.sealSyncPosition.prepareSendNContract": "{num}種類の異なる契約を送信準備中です。", "labelEdit.sealSyncPosition.funDescTip.open": "「署名位置同期」をオンにすると、現在の契約書で調整した署名位置・フィールドの座標を、その他{num}種類の契約書にも適用されます。", "labelEdit.sealSyncPosition.funDescTip.close": "「署名位置同期」をオフにすると、現在の契約書のみに適用され、その他{num}種類の契約書は元の状態のままとなります。", "labelEdit.sealSyncPosition.funDescTip.note": "注：現在の契約書においての署名位置の追加または削除は、その他{num}種類の契約書に自動的に適用されます。", "labelEdit.sealSyncPosition.funDescTip.keyPositionTip": "キーワードで位置自動決めを利用する場合は、キーワードに対応する署名位置においては、「署名位置同期」がオフになります。", "labelEdit.sealSyncPosition.switchContractTip": "契約書を切り換えて調整後の効果を確認することができます。契約切り換えの方法：", "labelEdit.sealSyncPosition.switchContactFun": "画面右上のデータエントリー数をクリックし、表示したい契約書を選択して切り換えます。", "labelEdit.sealSyncPosition.reopenDisabledTip": "「署名位置同期」を一旦オフにすると、今回の契約書送信で再びオンにすることはできません。", "labelEdit.keywordPosition": "フィールド座標位置", "labelEdit.keywordMatch": "キーワードに基づく組み合わせ（位置の手動調整をサポート）", "labelEdit.keyword": "契約書内のキーワード", "labelEdit.keywordPlaceHolder": "例えば「捺印」など", "labelEdit.keywordNum": "第＊番目のキーワード", "labelEdit.keywordNumPlaceHolder": "50以内の数字", "labelEdit.nameError": "名称を記入してください", "labelEdit.keywordMove": "オフセット(用紙サイズに対して)", "labelEdit.keywordMoveX": "横向き移動", "labelEdit.keywordMoveY": "縦向き移動", "labelEdit.excelHeaderPosition.title": "Excel表ヘッダー位置決め", "labelEdit.excelHeaderPosition.keyword": "ヘッダーキーワード", "labelEdit.excelHeaderPosition.keywordPlaceHolder": "受け取った実数量", "labelEdit.excelHeaderPosition.keywordNum": "第＊番目のキーワード", "labelEdit.excelHeaderPosition.referenceCol": "参照列名", "labelEdit.excelHeaderPosition.referenceColPlaceHolder": "貨物名など", "labelEdit.excelHeaderPosition.headerKeyword": "Excelヘッダーキーワード", "labelEdit.excelHeaderPosition.result": "効果は下記のとおり：", "labelEdit.excelHeaderPosition.headerKeywordTipsList.0": " 1. Excel表（領収書など）をアップロードする場合、ヘッダーキーワードの下にある各セルのフィールドをシステムが自動的に設定します", "labelEdit.excelHeaderPosition.headerKeywordTipsList.1": " 2.フィールド：名前はデフォルトで自己増加します。設定されたフィールド名が受け取った実数量の場合、その後の自己増加するフィールド名は受け取った実数量_1，受け取った実数量_2，受け取った実数量_3，...", "labelEdit.excelHeaderPosition.headerKeywordTipsList.2": "3. 何番目のキーワード：フィールドに同じキーワードが複数ある場合、N番目のキーワードの位置決めを行います（他の位置にあるキーワードのフィールドは設定されない）", "labelEdit.excelHeaderPosition.setReferenceCol": " 参照列の設定", "labelEdit.excelHeaderPosition.setReferenceColTips.0": "1. 必須項目です。記入しなければ、この機能は有効になりません。", "labelEdit.excelHeaderPosition.setReferenceColTips.1": "2.キーワードを記入すると、その列のデータにフィールドが揃い、空の行に遭遇すると自動的に終了します。", "labelEdit.excelHeaderPosition.setReferenceColTips.2": " 3.参照列は最大100行まで可能で、それ以上は処理されません。", "labelEdit.sealSet": "印鑑を設定します", "labelEdit.slant": "左に傾けます", "pointPositionDoc.pageTip": "{pageNum}/{pageSize}ページ", "pointPositionDoc.nextDoc": "次のファイルに入る　", "pointPositionDoc.checkboxName": "{count}オプション", "pointPositionDoc.confirmSeal": "印章適合", "pointPositionDoc.notConfirmSeal": "印章不適合", "pointPositionDoc.deleteTip": "削除完了", "pointPositionDoc.viewHighDpiImg": "HD画像の確認 | 原図の確認", "pointPositionDoc.boxSelect.name": "ボックスチェック", "pointPositionDoc.boxSelect.selectLabelNum": "{x}個のフィールドを選択", "pointPositionDoc.boxSelect.alignType": "フィールドのアライメント方法", "pointPositionDoc.boxSelect.revoke": "抹消", "pointPositionDoc.boxSelect.leftAlign": "左アライメント", "pointPositionDoc.boxSelect.bottomAlign": "下アライメント", "pointPositionDoc.boxSelect.tipAccess": "フィールド整列アドバイザ", "pointPositionDoc.boxSelect.selectGuideTip": "マウスボックスの選択フィールドを押したまま、右側の枠線で位置合わせ操作を選択できます。", "pointPositionMiniDoc.superTotalContract": "共{num}条数据，仅展示500条，请选择", "pointPositionMiniDoc.totalContract": "全{num}本のデータ、選択してください", "pointPositionMiniDoc.totalContractAfter": "本表示", "pointPositionMiniDoc.contractSwitchTip": "レコードは、一括インポートされたExcelテーブルのデータ行に対応します", "pointPositionMiniDoc.document": "ファイル", "pointPositionMiniDoc.documentsLength": "{documentsLength}部", "pointPositionMiniDoc.pager": "ページ番号", "pointPositionMiniDoc.page": "ページ", "pointPositionMiniDoc.totalPages": "ページ数", "pointPositionMiniDoc.goToPage": "第XXにジャンプ", "pointPositionMiniDoc.skipRiskTip": "当前发送的合同中包含的企业签约方已超过500家，不再进行年审风险提示。您可在档案柜中查看相对方的年审情况，确认无误后再进行发送。", "pointPositionMiniDoc.findNextSignPosition": "署名位置を検索", "pointPositionSite.step1": "手順1：", "pointPositionSite.selectSigner": "契約者を選択", "pointPositionSite.step2": "手順2：", "pointPositionSite.step3": "手順3：", "pointPositionSite.moreConfig": "詳細", "pointPositionSite.multipleSigners": "この契約者が複数自署機能を有効にする", "pointPositionSite.dragSignaturePosition": "署名位置をドラッグ", "pointPositionSite.insertSignaturePosition": "署名位置を挿入", "pointPositionSite.signField": "署名フィールド", "pointPositionSite.tempField": "内容フィールド", "pointPositionSite.businessField": "共通内容フィールド", "pointPositionSite.addBusinessField": "業務フィールドの追加", "pointPositionSite.manageBusinessField": "業務フィールドの管理", "pointPositionSite.tempFieldFillTip": "設定されたフィールドは署名者によって記入します", "pointPositionSite.searchBusinessField": "入力してください", "pointPositionSite.edit": "編集", "pointPositionSite.decorateField": "契約書装飾", "pointPositionSite.optional": "（任意）", "pointPositionSite.wordBillLabelConfig": "以下字段需要配置：", "pointPositionSite.whatTempField": "臨時フィールドとは？", "pointPositionSite.whatTempTip.0": "臨時フィールドはテンプレート変数を設定するために使用でき、設定後はそのテンプレートでのみ有効となり、他のテンプレートで再利用することはできません", "pointPositionSite.whatTempTip.1": "テンプレート起動後に仮フィールド内に記入された契約内容は、一時的に検索できなくなります。", "pointPositionSite.know": "わかりました", "pointPositionSite.whatBusinessField": "業務フィールド/契約書内容フィールドとは？", "pointPositionSite.whatBusinessTip.0": "業務フィールドは、テンプレートの変数を設定するために使用することができ、一度設定すると、すべての企業メンバーは、テンプレートを設定するときに再利用することができます。", "pointPositionSite.whatBusinessTip.1": "テンプレート起稿後に業務欄に記入された契約内容は、契約書管理の「一覧構成」機能と組み合わせて閲覧・検索が可能。", "pointPositionSite.groupSignMOpenTip": "グループ管理コンソール - グループ管理権限 - 詳細設定の権限を取得した後、このボタンをクリックすると、契約書フィールド管理画面に進みます。", "pointPositionSite.groupSignMJumpTip": "権限を取得した後、このボタンをクリックして契約内容フィールドを追加することができます。設定後、すべてのメンバーがテンプレートを設定する時に利用できます。", "pointPositionSite.groupSignMJumpDesc": "権限を取得するには、下記どちらかの方法で対応してください。：", "pointPositionSite.groupSignMJumpOpt1": "1. グループ管理権限で「詳細設定」権限を取得する。", "pointPositionSite.groupSignMJumpOpt2": "2. ロールの「詳細設定」権限を取得する。", "pointPositionSite.CommonSignMOpenTip": "企業コンソールの「詳細設定」権限を取得した後、契約内容フィールドを追加することができます。設定後、すべてのメンバーがテンプレートを設定する時に利用できます。", "pointPositionSite.CommonSignMJumTip": "企業管理コンソールの「詳細設定」の権限を取得した後、このボタンをクリックすると、契約書フィールド管理画面に進みます。", "pointPositionSite.seal": "捺印", "pointPositionSite.entSignature": "捺印者の自署", "pointPositionSite.operatorSignature": "担当者の自署", "pointPositionSite.scanSignature": "自署の読み取り", "pointPositionSite.signature": "署名", "pointPositionSite.confirmSeal": "業務照合印", "pointPositionSite.confirmRemark": "印章不適合の備考", "pointPositionSite.signDate": "署名日付", "pointPositionSite.text": "テキスト", "pointPositionSite.singleBox": "ラジオボタン", "pointPositionSite.multipleBox": "チェックボックス", "pointPositionSite.comboBox": "検索候補", "pointPositionSite.watermark": "すかし", "pointPositionSite.decorataRidingSeal": "割り印", "pointPositionSite.picture": "画像", "pointPositionSite.watermarkTip": "契約書の発信後真実の情報に自動で切り替わります", "pointPositionSite.innerSignComment": "コメント", "pointPositionSite.singlePageRidingSealTip": "単一ファイルには割り印の追加ができません", "pointPositionSite.goDecorateField": "契約書の装飾に入る", "pointPositionSite.decorateFieldTip": "注意：すかしと割り印は契約書の装飾で入れます", "pointPositionSite.table": "動態テンプレート", "pointPositionSite.term": "契約条項", "pointPositionSite.addContentFieldSteps.0": "エンタープライズ コンソール - ビジネス フィールド管理契約コンテンツ フィールド ページで、「xxx」という名前のフィールドを構成します。", "pointPositionSite.addContentFieldSteps.1": "15分ほど待つと、契約管理一覧設定非表示欄に「xxx」欄が表示されますので、設定を行ってください。", "pointPositionSite.number": "数字", "pointPositionSite.bizDate": "日付", "pointPositionSite.ridingSealTip": "初期設定ではすべてのファイルが対象です。契約書を送信する時、契約者がファイル捺印に参加しない場合、対応するファイルの割り印は発効しません。", "pointPositionSite.ridingSealSendTip": "契約者がファイル捺印に参加しない場合、対応するファイルの割り印は発効しません。", "pointPositionSite.ridingSealDocConfig": "以下のドキュメントに割印を設定しますか", "pointPosition.saveTemplateTip": "重要なお知らせ", "pointPosition.confirm": "確定", "pointPosition.save": "続けて保存", "pointPosition.send": "続けて送信", "pointPosition.hasSameLabelTip": "異なるタイプの同名フィールドがあります", "pointPosition.needChangeNameTip": "同名フィールドがあります。名称を修正してください", "pointPosition.synLabel": "{num}個の同名フィールド属性が現在のフィールド属性値に更新されました", "pointPosition.saveSuc": "保存完了", "pointPosition.isToPermissions": "使用車の操作行為を規定するため、テンプレートの使用者に権限を設定するようにしてください（契約書を発信する前に契約書の権限を修正するかどうか）", "pointPosition.remind": "注意", "pointPosition.goSet": "設定へ", "pointPosition.notGoSet": "設定しない", "pointPosition.nowSignText": "このファイルはお客様の署名が必要です。すぐに署名しますか？", "pointPosition.nowSignTip": "確認", "pointPosition.nowSign": "すぐに署名します", "pointPosition.laterSign": "後で署名します", "pointPosition.contractDispatchApply": "審査フローの設定", "pointPosition.riskTips": "年度審査のリスクに関して", "pointPosition.riskTipsCancel": "送信の取り消し", "pointPosition.riskTipsConfirm": "全部送信", "pointPosition.realNameAnnualVerify": "実名年次審査", "pointPosition.realNameAnnualVerifyRecords": "年次審査記録", "pointPosition.customInfoAnnualVerify": "ユーザー設定資料の年次審査", "pointPosition.viewAnnualVerifyINfo": "資料の詳細を見ます", "pointPosition.entName": "企業名", "pointPosition.operation": "操作", "pointPosition.annualVerifyTime": "年次審査の時間", "pointPosition.annualVerifyStatus": "年次審査の状態", "pointPosition.annualVerifyCondition": "年次審査の状況", "pointPosition.noDataTips": "なし", "pointPosition.noRealNameAnnualVerify": "年次審査なし", "pointPosition.realNameAnnualVerifying": "年次審査中", "pointPosition.realNameAnnualVerified": "年次審査済み", "pointPosition.noCustomInfoAnnualVerify": "年次審査なし", "pointPosition.customInfoAnnualVerifyingNo": "年次審査中（未提出）", "pointPosition.customInfoAnnualVerifyingYes": "年次審査中（提出済み）", "pointPosition.customInfoAnnualVerified": "年次審査済み", "pointPosition.setApprovalTitle": "设置审批流", "pointPosition.setApprovalTitleSelect": "发送合同时，您需要选择一个审批流，但管理员尚未为您分配任何审批流，您无法发送合同。", "pointPosition.setApprovalTitleSuggest": "建议您：", "pointPosition.setApprovalTitleSuggestContent": "联系管理员在企业控制台审批管理页面为您添加可用的审批流程，或关闭“审批流程严格管控”这个配置项。", "pointPosition.setApprovalTitleSuggestSender": "在企业控制台-审批管理-审批流管理，点击审批流中的“合同发件人 修改”即可添加编辑审批流触发人员。", "pointPosition.setApprovalTitleSuggestStrict": "在企业控制台-审批管理-标题栏右侧，点击“审批流严格管控”。在弹窗中即可开启关闭该项。", "pointPosition.setApprovalTitleSuggestCompany": "此次发送涉及到的所有子企业的审批流配置情况都需要检查。", "pointPosition.deleteRidingSeal": "ドキュメントのスリット章を削除するには", "pointPosition.deleteEntRidingSealDes": "\"{showName}\"の割印捺印箇所を削除する：", "pointPosition.deleteCurrentRidingSeal": "現在の1件のドキュメントの割印捺印箇所のみ削除する", "pointPosition.deleteAllRidingSeal": "すべてのドキュメントの割印捺印箇所を削除する", "labelLackTip.document": "ファイル", "labelLackTip.signer": "署名者", "labelLackTip.lackLabelTips": "以下の契約書に署名位置が設定されていません。署名位置を設定する必要があるか確認してください。", "labelLackTip.allLackLabelTips": "すべての契約書類に署名位置が指定されていないため、契約を送信できません。署名位置を調整するか、署名者を削除してください。", "labelLackTip.goEdit": "修正へ", "labelLackTip.operationTip": "見るだけで署名せずをどう実現するか？", "labelLackTip.caseTip.1": "案1：xxx署名役割に契約書類付きの契約注意事項を追加する。", "labelLackTip.caseTip.2": "案2：xxx署名役割を共有先として設定すると、契約書閲覧可能になる。この共有先受信者の通知をオフにすることをお勧めします。", "labelLackTip.caseTip.3": "案3：xxx署名役割は、送信者、承認者、または企業契約閲覧権限を持つ場合、契約書を閲覧可能。", "labelLackTip.ridingSealInvalid": "以下の契約文書の中で乗馬の章を展示しません:", "labelLackTip.onePageLimint": "契約書は1ページだけで,印鑑は不要です", "labelLackTip.noSealLimit": "{name}契約締結には関与しません", "signCharge.deductPublicNotice": "個人向け契約書の使用可能部数が不足している際は法人向け契約書から差し引きます", "signCharge.isReceivePayer": "当該契約書はお客様が指定した署名者が支払います", "signCharge.isCCReceiverPayer": "当該契約書はお客様が指定した副本者が支払います", "signCharge.CCReceiverPayerFailTip": "副本者の残高が不足しているか、お客様の指定した副本者の支払いをサポートしていません", "signCharge.charge": "", "signCharge.units": "{num}部", "signCharge.contractToPrivate": "個人向け契約書", "signCharge.contractToPublic": "法人向け契約書", "signCharge.costTips.1": "法人向け契約書：署名者（送信者を含まず）の中に企業アカウントがある契約書", "signCharge.costTips.2": "個人向け契約書：署名者（送信者を含まず）の中に企業アカウントがない契約書", "signCharge.costTips.3": "課金部数はファイル数に基づき計算します", "signCharge.costTips.4": "課金部数 = ファイル部数 X ユーザーグループ（列）の一括インポート", "signCharge.confirm": "確定", "signCharge.cancel": "キャンセル", "signCharge.toCharge": "リチャージする", "signCharge.contractNeedCharge.1": "使用可能な契約部数が不足しているため、送信できません", "signCharge.contractNeedCharge.2": "使用可能な契約部数が不足しています。管理者主任にリチャージするよう連絡してください", "signCharge.contractNeedCharge.3": "専用コースの残高が不足していますので、下のチャージボタンをクリックしてチャージを完了してください", "signCharge.accountCharge.notice": "この契約書は参加アカウント数に基づき課金します", "signCharge.accountCharge.able": "正常に送信できます", "signCharge.accountCharge.unable": "使用アカウント数が不足しています。ベストサインのカスタマーサービスに連絡してください", "signCharge.signRole": "契約書の送信と自動署名：", "signCharge.signRoleNone": "実行できません", "signCharge.signProxy": "現在のテンプレートは「業務協力」で使用されているため、発信や署名機能を使用することができません。", "signCharge.cannotSign": "＊お客様は下記条件を満たしていないため「契約書を送信＆署名」を実行できません。", "signCharge.signRoleTerm": "送信時に自動署名できる署名者の条件：", "signCharge.signRoleTermBtn": "閉じる", "signCharge.signRoleTermOpenBtn": "展開する", "signCharge.sendAndSignTerms.0": "(1) 専用印章がテンプレートに設定されている。", "signCharge.sendAndSignTerms.1": "(2) 現在ログインしているアカウントが契約書の署名者となっていて、かつ「専用印章」を取得済みである。", "signCharge.sendAndSignTerms.2": "(3) 現在ログインしているアカウントが最初の署名者に設定されている。（または契約書に署名順序が設定されていない）", "signCharge.sendAndSignTerms.3": "(4) 署名前の承認フローが設定されていない。（または契約書送信前の承認フローが設定されていない）", "signCharge.sendOnly": "契約書を送信", "signCharge.sendAndSign": "契約書を送信＆署名", "signCharge.verCodeInputErr": "先に認証コードを取得してください", "signCharge.lackVerCode": "先に認証コードを入力してください", "signCharge.timingSend": "契約書の送信時刻を指定", "signCharge.sender": "送信元企業", "signCharge.switchPayerTip": "注意：契約プロセスの停滞を避けるため、事前に支払い署名者と合意を取り付けてください。", "signCharge.payer": "支払者", "signCharge.payerChargeUsageDesc": "受取人払い機能の紹介", "signCharge.switchPayer": "1. 受取人払い", "signCharge.feePayer": "2. 料金支払い：", "signCharge.keyAdv": "効果：", "signCharge.decreaseCost": "1. コスト削減：", "signCharge.optMulti": "2. 柔軟な運用：", "signCharge.connectToOpen": "ベストサインに連絡して支払者選択機能を有効にします", "signCharge.connectAdminToOpen": "操作権限がありません。管理者{adminAccount}にお問い合わせください", "signCharge.decreaseCostDesc": "他の署名者が料金を支払うことで、貴社の契約書送信料金を軽減します。", "signCharge.optMultiDesc": "商慣習や双方の合意に基づいて、支払者を選択できます。", "signCharge.payerChargeUsageTip1": "選択された署名者が契約書の送信料金を支払います。貴社および他の契約者には料金が発生しません。", "signCharge.payerChargeUsageTip2": "支払者の送信可能件数の残高に応じて、契約署名時に自動的に料金が引落されるか、またはチャージの催促が表示されます。", "signCharge.noSignInAllTip": "契約者：{roleName}がすべてのドキュメントの署名に参加していないため、契約支払者として設定することができません。支払者の設定をキャンセルしますか", "sendPrepare.selectTemplate": "テンプレートを選択してください", "sendPrepare.selectTemplateFileTip": "先にテンプレートファイルを選択してください", "sendPrepare.batchImportExcel": "Excelによる一括送信", "sendPrepare.batchReplaceExcel": "異なる契約書を一括発信", "sendPrepare.sendContract": "契約書を作成", "sendPrepare.allTemplate": "全てのテンプレート", "sendPrepare.selectModeTitle": "テンプレートのビューです", "sendPrepare.selectDocument": "ファイル", "sendPrepare.viewDetail": "詳細を表示にチェックを入れてください", "sendPrepare.sendGuideStep1": "テンプレートの選択方法？", "sendPrepare.sendGuideStep2": "契約書一括送信の方法?", "sendPrepare.sendGuideStep3": "テンプレート契約書を使用したくない場合", "sendPrepare.sendGuideDialogText1": "シーンに対応するテンプレートを探し、その中から一つまたは複数のファイルにチェックを入れて選択すると、これらのファイルが一度に契約者へ送信されます。 | よく同時に送信する複数のファイルは、「テンプレートファイルユニット」として設定しておくと、スピーディーに選択することができます。", "sendPrepare.sendGuideDialogText2": "単独の契約書の送信/開始：1回に1つのファイルまたはファイルユニットのみ送信します。 | Excelを使用した契約書の一括送信：1回で複数の相手へ複数の契約書を送信することができ、これらの契約書はいずれもテンプレート契約書です。 | 異なる契約書の一括送信：複数の相手へ複数の契約書を送信することができ、これらの契約書は圧縮ファイルで合理的にローカルからアプロードされ、各契約書の内容がそれぞれ異なります。", "sendPrepare.sendGuideDialogText3": "「＋」のついたファイルを選択し（または「アップロード待ちファイル」にチェックを入れて選択し）、「ファイルのアップロード」ボタンをクリックしてローカルファイルを追加します（このようなファイルがない場合、テンプレート管理ページで作成してください）", "sendPrepare.allMode": "全部を表示", "sendPrepare.federationMode": "テンプレートファイルユニットのみ表示", "sendPrepare.documentMode": "テンプレートファイルのみ表示", "sendPrepare.emptyContract": "空白契約書", "sendPrepare.ofdNotSupport": "OFDテンプレートは一斉送信をサポートしません", "sendPrepare.noTemplate": "あなたがまだいかなるテンプレート", "sendPrepare.sendQuickly": "テンプレート作成不要で迅速に契約を開始", "sendPrepare.allocateTemplate": "企業管理者に連絡して、テンプレートを割り当てて貰ってください", "sendPrepare.allocateTemplatePermission": "或いは：管理者に企業管理コンソールの「役割管理」にて「テンプレートの新規作成」権限を割り当ててもらってください。そして、契約書を直接送信するか、テンプレートを作成して契約書を送信することができます。", "sendPrepare.batchSend": "一斉送信", "sendPrepare.normalSend": "個別送信", "contractItem.emptyContract": "ファイルのアップロード", "contractItem.uploadDoc": "契約書のアップロード", "contractItem.replaceDoc": "ファイルの差し替え", "contractItem.addAttachment": "契約付属書類の追加", "contractItem.uploading": "アップロード中...", "contractItem.pageCount": "{num}ページ", "contractItem.more": "もっと...", "contractItem.configAttachment": "付属資料フィールドの設定", "contractItem.federationTip": "（テンプレートを使って複数ファイルの契約書を発信します。仮にファイル名をa/b/c、場面1でa/bを発信し、場面2でa/b/cを発信する必要があれば、事前にファイルを組み合わせておき、発進時に直接対応する組み合わせを選択することで可能となります。）", "contractItem.federations": "テンプレートファイルの組み合わせ", "contractItem.fillDocument": "補足ファイル", "contractItem.uploadDocument": "文書をアップロードする", "contractItem.uploadDocumentContinue": "追加の文書をアップロード", "contractItem.temporarySaveTip": "現在の契約書はまだ送信されていません。 | 7日以内にこのテンプレートを再度選択して契約書を送信する場合は、「一時的に保存」されているこの契約書の送信を続けることができます。 | 未送信の下書き契約を見つけるには、テンプレートをクリックしてください。", "contractItem.notRemind": "次回はお知らせしないでください", "contractItem.interanlSignTip": "現在のテンプレートは「内部文書への署名のみに使用」に設定されています、正式な契約書（労働契約など）に使用しないでください", "contractItem.attachedToBoxTip": "このテンプレートはファイルキャビネットの代理送信代理署名に設定されているので、一時ドキュメントと添付ファイルフィールドを作成できません。", "contractItem.replaceTip1": "置き換えに使用する新しい文書は{page}ページのみですが、置き換え前の元の文書の{pageStr}ページ目に、押印、署名、署名日付、契約内容などのフィールド情報が存在する可能性があります。\nこれらのフィールドを削除してから再度置き換える必要があります。手順は以下の通りです：", "contractItem.replaceTip2": "1. ページ右上の「次へ」ボタンをクリックし、「署名位置の指定」ページに進みます。", "contractItem.replaceTip3": "2. {pageStr}ページ目を確認し、上記のフィールド情報を手動で削除します。", "contractItem.replaceTip4": "3. 現在の「文書のアップロード」ページに戻り、再度置き換え操作を行います。", "contractItem.replaceTip5": "4. 置き換えが成功したら、再度「署名位置の指定」ページに入り、削除されたフィールドを新しい文書に手動で追加し、ビジネスに影響が出ないようにします。", "descriptionFields.newField": "フィールドの追加", "descriptionFields.title": "フィールド設定", "descriptionFields.syncDoc": "テンプレートのその他ファイルと同期", "descriptionFields.placeholder": "フィールドを入力してください", "descriptionFields.cancel": "キャンセル", "descriptionFields.confirm": "確定", "descriptionFields.saveSuc": "保存完了", "getSeal.selectSeal": "印章の選択", "getSeal.chooseApplyPerson": "申請者の選択", "getSeal.getSealBtn": "印章の取得", "getSeal.nowApplySealList": "以下の印章を要求しています", "getSeal.chooseApplyPersonToDeal": "申請者を選択してください。お客様の申請及び契約書は選択した人に渡され処理されます（この契約を引き続き閲覧し、フォローすることができます）", "getSeal.chooseApplyPersonToMandate": "印章所持者を選択してください。選択した人は通知を受け取り、審査が通った後当該印章の使用権限を入手すると、いつでもこの印章を使用して契約書に捺印署名することができます", "getSeal.cancel": "キャンセル", "getSeal.confirm": "確定", "getSeal.sealApplySentPleaseWait": "印章の分配申請を送信しました。審査が通るまでしばらくお待ちください。もしくはその他捺印の方法を選択することができます", "getSeal.entNoSeal": "お客様の企業は印章をアップロードしていません", "getSeal.contactGroupAdminToDistributeSeal": "集団の管理者に印章を分配するよう連絡してください", "getSeal.getSealTip": "お客様は先に企業印章を取得しないとこれが確認できません。 | お客様は先に下記の企業印章を取得しないとこれが確認できません。", "authIntercept.title": "お客様の要求は：", "authIntercept.name": "氏名は：", "authIntercept.id": "身分証明書番号は：", "authIntercept.descNoAuth1": "上記身分情報はお客様本人かどうか確認し、ここで実名認証を行ってください。", "authIntercept.descNoAuth2": "実名認証の承認後、契約書を閲覧し、署名ができるようになります。", "authIntercept.descNoSame1": " ～の身分で契約書を署名", "authIntercept.descNoSame2": "これとお客様が現在ログインしているアカウントと完了した実名情報と一致していません。", "authIntercept.tips": "注意：身分情報が'完全一致しないと契約書の署名はできません", "authIntercept.goOn": "本人です。認証を開始します", "authIntercept.goMore": "追加認証へ", "authIntercept.authTip": "実名認証を実行します。", "authIntercept.viewAndSign": "認証が完了した後契約書の閲覧及び署名ができるようになります", "authIntercept.tips2": "注意：企業名が完全一致しないと契約書の署名はできません。", "authIntercept.requestOtherAnth": "他人の認証を要求する", "authIntercept.goAuth": "実名認証へ", "authIntercept.requestSomeoneList": "以下のスタッフに実名認証を要求してください。", "authIntercept.ent": "企業", "authIntercept.entName": "企業名", "authIntercept.account": "アカウント", "authIntercept.accountPH": "携帯電話またはメールアドレス", "authIntercept.send": "発信", "authIntercept.lackEntName": "企業名を入力してください", "authIntercept.errAccount": "正確なメールアドレスもしくは携帯番号を記入してください", "authIntercept.successfulSent": "送信完了", "authIntercept.me": "私", "authIntercept.myself": "本人", "authIntercept.reAuthBtnTip": "私が現在の携帯番号の実質使用者です、", "authIntercept.reAuthBtnContent": "再実名登録後、このアカウントの元の実名での利用は却下されますので、ご確認ください。", "authIntercept.cancel": "キャンセル", "authIntercept.confirmOk": "確認", "authIntercept.goHome": "契約書リストページに戻る>>", "authIntercept.authInfo": "現在検測しているアカウントの実名IDは ", "authIntercept.in": "に", "authIntercept.finishAuth": "実名が完了しています。コンプライアンスに則した契約書の署名に用いられます", "authIntercept.ask": "現在のアカウントで署名を続けますか？", "authIntercept.reAuthBtnText": "はい。このアカウントで再度実名署名を行います", "authIntercept.changePhoneText": "いいえ。発送者に連絡して署名の携帯番号を変更します", "authIntercept.changePhoneTip1": "発信者の要求で、連絡してください", "authIntercept.changePhoneTip2": "署名情報（携帯番号/氏名）を変更し、お客様による署名が指示されています。", "applyJoinEnt.beenAuthenticated": "実名登録されています", "applyJoinEnt.assignedIdentity": "発信者の記入した契約主体：", "applyJoinEnt.entBeenAuthenticated": "当該企業は実名登録されています。管理者主任の情報は次のとおりです：", "applyJoinEnt.entAdminName": "管理者氏名：", "applyJoinEnt.entAdminAccount": "管理者アカウント：", "applyJoinEnt.applyToBeAdmin": "管理者主任として申請します", "applyJoinEnt.contactToJoin": "管理者に連絡して企業に参加", "applyJoinEnt.applicant": "申請者", "applyJoinEnt.inputYourName": "お客様の氏名を入力してください", "applyJoinEnt.account": "アカウント", "applyJoinEnt.send": "発信", "applyJoinEnt.contract": "契約書", "applyJoinEnt.sendWishToJoin": "アカウントを通して管理者として申請できます。また管理者に企業の参加申請を送ることもできます", "applyJoinEnt.applyToJoin": "お客様はまだ当該企業を参加していないため、当該{alias}にまたは署名できません。参加申請をしますか？", "applyJoinEnt.sentSuccessful": "送信完了", "receiverItemDisclaimer.title": "機能使用注意", "receiverItemDisclaimer.desc.0": "『電子署名法』の関連規定によると、法的に有効な電子契約は、契約当事者が容易にアクセスできることが保証されなければならず、契約当事者がダウンロードや 閲覧することを禁止することは、「電子署名法」の要件に違反することになります。", "receiverItemDisclaimer.desc.1": "署名者が署名された契約書を閲覧・ダウンロードできることを保証してください。", "receiverItemDisclaimer.desc.2": "上記の内容を確認し、同意します", "receiverItemDisclaimer.confirm": "続ける", "templateListDynamicEntryConfirm.title": "注意", "templateListDynamicEntryConfirm.content.0": "新規契約のアップグレードを行ったことが検出されましたが、新規契約機能を使用できるようになる新しい動態テンプレートのアップグレードを続けますか", "templateListDynamicEntryConfirm.content.1": "テンプレートの添付ファイルの追加、テンプレートファイル組み合わせ、承認契約の送信などの機能が含まれます。 アップグレードに関しては、カスタマーサービスまたはカスタマーサクセスマネージャーにお問い合わせください。", "templateListDynamicEntryConfirm.content.2": "注意：アップグレード後、過去の動態テンプレートは新規契約テンプレートに移行されます。以前にhtml動態テンプレートを使用していた場合、アップグレード後はテンプレートが同期されませんので、事前にテンプレートデータをローカルに保存しておいてください。", "templateListDynamicEntryConfirm.cancel": "再度リマインドしません", "templateListDynamicEntryConfirm.confirm": "確定", "templateListOpenDynamicConfirm.title": "動態テンプレートサービスを開通しますか？", "templateListOpenDynamicConfirm.content.0": "動態テンプレートを開通すると、以下のことが可能になります", "templateListOpenDynamicConfirm.content.1": "アップロードされたワード文書内に直接業務フィールドを設定することで、ウェブページにドラッグ＆ドロップする必要がありません。", "templateListOpenDynamicConfirm.content.2": "テンプレートファイルのオンライン編集。", "templateListOpenDynamicConfirm.content.3": "任意の行数、列数の動態テンプレートの挿入。", "templateListOpenDynamicConfirm.tip": "すぐにサービスを開通するようベストサインの販売担当者に連絡します。", "templateListOpenDynamicConfirm.confirm": "わかりました。", "templateDynamicPosition.notice": "リマインダー", "templateDynamicPosition.initLoadingTip": "テンプレートフィールドおよびエディターを初期化中です。しばらくお待ち下さい......", "templateDynamicPosition.saveLoadingTip": "データを保存中です。データが失われる可能性がありますので、ページをリフレッシュしたり閉じないでください", "templateDynamicPosition.modifyNotice": "テンプレートフィールド名の修正はページ右側のポップアップウインドウで完了させてください。テキスト内で直接修正することは無効となり、ポップアップウインドウの名称は上書きされます。", "templateDynamicPosition.modifyName": "システムの検査にて、一部のフィールド名の修正操作は規則に適合していないことが判断されました。検査後再保存してください。", "templateDynamicPosition.modifyClose": "わかりました。", "templateDynamicPosition.initDocError": "ファイルの初期化エラー", "templateDynamicPosition.helpPoper": "動態テンプレートは全体的にグレードアップしました。使用上の注意事項を確認する場合、ヘルプメニューをクリックして確認してください。", "templateDynamicPosition.help": "ヘルプメニュー", "templateDynamicPosition.preview": "効果プレビュー", "templateDynamicPosition.someTextNoName": "各ファイルタグに名称を記入してください", "templateDynamicPosition.allSignDone": "各署名者に署名位置を指定してください", "templateDynamicPosition.sameName": "同名フィールドがあります。名称を修正してください", "templateDynamicPosition.saveSuccess": "保存完了", "templateDynamicPosition.optionsNotEnough.title": "注意", "templateDynamicPosition.optionsNotEnough.content": "単一のチェックボックス{alias}のオプションが2項目足りていません。追加してください", "templateDynamicPosition.optionsNotEnough.confirmBtn": "わかりました", "templateDynamicPosition.labelValid.confirmBtnWithTime": "わかりました（{num}）", "templateDynamicPosition.labelValid.noCloseTip": "5秒のカウントダウンが終了するまでページを閉じないでください", "templateDynamicPosition.labelValid.waiting": "システムはテンプレートフィールドが正しいかどうかを確認しています。保存ボタンを再度クリックする前に、5秒のカウントダウンが終了するのをお待ちください。", "templateDynamicPosition.noTerm": "現在使用可能な条項がありません。企業の管理コンソールで追加してください。", "templateDynamicPosition.helpSlider.title": "ヘルプメニュー", "templateDynamicPosition.helpSlider.firstStep": "手順1：契約者を選択", "templateDynamicPosition.helpSlider.secondStep": "手順2：署名位置を挿入", "templateDynamicPosition.helpSlider.secondStepDetail": "挿入が必要なフィールドを選択し、ファイル内で左クリックを行って挿入をクリックします。本文の内容を修正する必要がある場合、右側のポップアップウインドウで修正する必要があり、そうしないと操作は無効となります。（注意：業務フィールドのハイライトは最終的に発信される契約書の中には表示されません）", "templateDynamicPosition.bookmarkSensorTip": "テンプレート内の下記フィールドは保存が有効になりません。再度追加する必要があり、再使用しないのであれば削除をクリックしてください。", "templateDynamicPosition.deleteInvalidBookmarkTip": "（注意：テンプレートの保存でリフレッシュをクリックしたりページを閉じると、フィールドの保存が失敗する可能性があります）。", "templateDynamicPosition.moreThenTip": "{num}個のタグ待ち", "templateDynamicPosition.dataSyncFailTip": "動的テンプレートデータの処理エラーです。カスタマーサービス/テクニカルスタッフに連絡して処理をしてください", "templateDynamicFieldEdit.receiver": "受信先", "templateDynamicFieldEdit.info.notice": "注意：", "templateDynamicFieldEdit.info.table.0": "動態テンプレートは名前の重複不可。", "templateDynamicFieldEdit.info.table.1": "現在のエディタの動態テンプレートはプレースホルダーであり、テンプレート使用時には実際の行列数で表示されます", "templateDynamicFieldEdit.info.noTable.0": "同じ業務フィールドがテンプレート内の複数の場所に追加されている場合、記入者は一度だけそれを記入し、同じ値として保存するだけでよいです。", "templateDynamicFieldEdit.info.noTable.1": "属性設定は、現在のテンプレート内の同名のすべての業務フィールドと同期されます", "authorityApprove.account": "アカウント：", "authorityApprove.name": "氏名", "authorityApprove.sealReselect": "申請者がこの印章を持っており、契約書の署名には再授権が必要です。", "authorityApprove.electronicSeal": "印章の選択", "authorityApprove.admin.title": "管理者主任に引き継ぐ", "authorityApprove.admin.sender": "引き継ぎ者：", "authorityApprove.admin.powerTitle": "引き継ぎ者が保留を申請する権限", "authorityApprove.admin.detailTitle": "{name}（アカウント：{applyUserAccount}）は{entName}のメイン管理者をあなたに移譲しています。メイン管理者の主な権限は以下の通りです：", "authorityApprove.admin.detailInfo": "1、企業印章の使用と割り当て ；| 2、企業構成員の管理 ；| 3、企業契約書の管理；", "authorityApprove.admin.tip": "メイン管理者は、通常、会社の法定代表者、財務責任者、法務責任者、IT部門責任者等が務め、責務の効果的な遂行を確保します。", "authorityApprove.admin.tip2": "お客様は企業の管理者主任として受け継ぎますか？", "authorityApprove.admin.resultSuccess.title": "お客様は引き継ぎを受け入れました", "authorityApprove.admin.resultSuccess.tip": "これに同意することで、お客様は企業管理者となり、関連する権限や印章を受験することに同意したことになります。", "authorityApprove.admin.resultFail.title": "お客様は引き継ぎを拒否しました", "authorityApprove.admin.resultFail.tip": "企業管理者になることを拒否しました。拒否理由は：{reason}", "authorityApprove.admin.resultDetail": "審査詳細の引き継ぎ", "authorityApprove.power.title": "審査の権限", "authorityApprove.power.tip": "{applyUserName}は企業の構成員ではありません。権限に同意すると自動でこのメンバーが企業構成員として追加されます。", "authorityApprove.power.applyUserName": "申請者氏名：", "authorityApprove.power.powerTitle": "申請する権限", "authorityApprove.power.resultSuccess.title": "授権完了しました", "authorityApprove.power.resultSuccess.tip": "申請者は契約書の署名を完了するために一部の権限と機能を取得しました。", "authorityApprove.power.resultFail.title": "申請を却下しました", "authorityApprove.power.resultFail.tip": "申請者はお客様の返信を受け取り、意見{reason}をもとにして、申請を調整します。", "authorityApprove.power.resultDetail": "審査詳細申請", "authorityApprove.preAuth.applyUserName": "引き継ぎ者：", "authorityApprove.preAuth.title": "企業実名認証", "authorityApprove.preAuth.powerTitle": "申請する権限：", "authorityApprove.powerTitleTip": "閲覧権限を申請する対象の契約書には、署名者アカウントが指定されていない契約書と、指定された署名アカウントの契約書の中で他のアカウントに受領されたものが含まれます。", "authorityApprove.preAuth.presetConfiguration": "事前設定", "authorityApprove.preAuth.realName": "企業認証", "authorityApprove.preAuth.step1": "手順1：印章パターンを事前に設定してください。", "authorityApprove.preAuth.step2": "手順2：認証構成員の権限設定を引き継ぎます。", "authorityApprove.preAuth.applyInfo.t1": "お客様に企業認証のユーザを申請する", "authorityApprove.preAuth.applyInfo.t2": "認証申請する企業名", "authorityApprove.preAuth.signInfo": "送信者{contractSenderName}からの{contractAlias}{contractTitle}に署名が必要です。", "authorityApprove.preAuth.seal.c1": "方法一：システムのデフォルトパターンを使用", "authorityApprove.preAuth.seal.c2": "方法二：画像をアップロードして企業印章を生成", "authorityApprove.preAuth.tip": "{applyUserName}は企業の構成員ではありません。権限に同意すると自動でこのメンバーが企業構成員として追加されます。", "authorityApprove.preAuth.resultDetail": "審査詳細", "authorityApprove.viewEntContract": "契約書の閲覧", "authorityApprove.contractLimit": "契約書範囲：", "authorityApprove.entSeal": "契約書への署名（電子印章）", "authorityApprove.entSealTooltip": "印章を取得した後、署名権限の範囲内にあるすべての企業がこの印章を使用して署名することができます。", "authorityApprove.getMore": "更に権限を取得", "authorityApprove.distributionSeal": "印章を割り当てる", "moreRightDialog.moreRight": "その他の権限", "moreRightDialog.confirmBtn": "確定", "moreRightDialog.viewContract": "企業契約を閲覧する", "moreRightDialog.nowContract": "現在の契約書", "moreRightDialog.nowSenderProxyContract2": "{contractSenderName}が送信した契約", "moreRightDialog.allSenderProxyContract": "すべての契約書", "moreRightDialog.proxyContractTip": "閲覧権限を申請する対象の契約書には、署名者アカウントが指定されていない契約書と、指定された署名アカウントの契約書の中で他のアカウントに受領されたものが含まれます。", "moreRightDialog.signContract": "企業契約書の署名（{sealName}）", "moreRightDialog.nowSenderContract": "現在の送信者が送信した契約", "moreRightDialog.nowSenderContract2": "{contractSenderName}が発信した契約書", "moreRightDialog.allSenderContract": "すべての契約書", "moreRightDialog.noApplySignRight": "署名しません、印章は必要ありません", "moreRightDialog.canSignTip": "閲覧可能な範囲内で署名が許可されています。", "moreRightDialog.allTip": "説明：現在の発信者には企業およびそのグループ企業、子会社、業務ラインが含まれています。", "batchOrAllOperateContract.iKnow": "我知道了", "batchOrAllOperateContract.oneTypeSeal": "同じ企業は一つの印章を使用", "batchOrAllOperateContract.moreTypeSeal": "異なる契約書には異なる印章を使用", "batchOrAllOperateContract.noSealAuthTip": "この契約に署名するために必要な権限を取得していないため、一括署名の操作は実行できません。提案：これらの契約に個別に署名することをお勧めします。", "batchOrAllOperateContract.noSealAuthTipTitle": "一般的な理由：", "batchOrAllOperateContract.noSealAuthTip1": "1、あなたは契約当事者企業に加入していないので、その企業の契約書に署名できません。", "batchOrAllOperateContract.noSealAuthTip2": "2、あなたはまだ印章を持っていないので、契約書に署名できません。", "batchOrAllOperateContract.noSealAuthTip3": "3、あなたの会社は、特定の会社から受信した契約書に署名するには追加の署名権限を付与する必要があると規定していますが、あなたにはこの権限が付与されていません。", "batchOrAllOperateContract.amountOfContract": "部の契約書", "batchOrAllOperateContract.noAuthEnt": "未実名企業", "batchOrAllOperateContract.personSign": "個人の初期設定署名", "batchOrAllOperateContract.noChangeSeal": "変更の必要な印章データはありません。初期設定の印章を使用中です", "batchOrAllOperateContract.noSealTip": "現在切り替え可能な印章がありません", "batchOrAllOperateContract.selectSeal": "印章の選択", "batchOrAllOperateContract.useSeal": "使用", "batchOrAllOperateContract.changeSeal": "印章の変更", "batchOrAllOperateContract.moreContractSealTip": "以下の条件を同時に満たさないと使用できません：1、一つ前の「一括署名の契約書範囲」で「チェックを入れた契約書のみ」を選択している。2、企業があなたに複数の印章を割り当てている。", "batchOrAllOperateContract.signFooterTip": "契約書数が多くなれば、一括署名の完了時間も長くなります。しばらくお待ち下さい", "batchOrAllOperateContract.yes": "YES", "batchOrAllOperateContract.no": "NO", "batchOrAllOperateContract.reject": "却下", "batchOrAllOperateContract.agree": "同意", "batchOrAllOperateContract.cancel": "キャンセル", "batchOrAllOperateContract.confirm": "確定", "batchOrAllOperateContract.continue": "続ける", "batchOrAllOperateContract.continueSign": "続ける", "batchOrAllOperateContract.batch": "一括", "batchOrAllOperateContract.nowBatch": "今回の一括", "batchOrAllOperateContract.contractRange": "の契約書範囲は：", "batchOrAllOperateContract.operate": "操作", "batchOrAllOperateContract.allSelect": "選択合計", "batchOrAllOperateContract.outputFailTip": "少なくとも契約書リスト内の契約書を選んでエクスポートします", "batchOrAllOperateContract.changeStatusTip": "契約書は、「署名期限切れ」の契約状態を除くと自動的に無視されます。「署名期限切れ」の契約書の状態は「署名中」に修正されます。7日間の契約書署名有効期限がすぎた後、未署名契約書のアカウントに署名リマインダーをプッシュ送信しますか？", "batchOrAllOperateContract.changeStatusSupplement": "署名者が設定した「署名時効」は効力を失います", "batchOrAllOperateContract.approvalTip": "部の契約書で、その中でお客様の審査が必要ない契約書は自動的に無視されます。 審査結果を選択してください", "batchOrAllOperateContract.outputTip": "部の契約書。エクスポート結果に複数文書の契約の個々の補助契約を含める必要がありますか？", "batchOrAllOperateContract.revokeTip": "部の契約書で、その中で取り消しおよび完了した契約書は自動的に無視されます。", "batchOrAllOperateContract.remindTip": "部の契約書で、その中で取り消しおよび完了した契約書は自動的に無視されます。", "batchOrAllOperateContract.transferTip": "件の契約書", "batchOrAllOperateContract.batchSignTip": "部の契約書で、その中でお客様の署名が必要ない契約書は自動的に無視されます。今回の一括署名で使用している印章は：", "batchOrAllOperateContract.batchTagTip": "あなたの会社から送信した契約書でない場合は、ラベルを貼り付けることができません。", "batchOrAllOperateContract.downloadTip": "部の契約書。", "batchOrAllOperateContract.batchOperateResultPage": "一括操作結果ページ", "batchOrAllOperateContract.toHomePage": "トップページに戻る", "batchOrAllOperateContract.refresh": "ページのリフレッシュ", "batchOrAllOperateContract.logTips": "数量が多ければ多いほど、バッチ操作に時間がかかるため、他のタスクを処理してからこのページにアクセスできます。（エントリ：1、契約管理→一括操作記録、2、ファイル＋→一括タスクセンター、3、コンソール-チャージ管理-使用記録→使用記録ダウンロードタスク）", "batchOrAllOperateContract.operateName": "操作名", "batchOrAllOperateContract.operateTime": "操作日時", "batchOrAllOperateContract.operateResult": "操作結果", "batchOrAllOperateContract.operateLog": "操作ログ", "batchOrAllOperateContract.remark": "備考", "batchOrAllOperateContract.contractTask": "契約管理一括タスク", "batchOrAllOperateContract.archiveTask": "档案+批量任务", "batchOrAllOperateContract.useRecordTask": "レコードを使用したタスクのダウンロード", "batchOrAllOperateContract.createTime": "操作日時", "batchOrAllOperateContract.bizName": "档案柜名称", "batchOrAllOperateContract.bizId": "档案柜ID", "batchOrAllOperateContract.total": "データ件数", "batchOrAllOperateContract.cost": "预计耗时", "batchOrAllOperateContract.fileStatus": "ファイルステータス", "batchOrAllOperateContract.archiveExport": "档案柜列表导出", "batchOrAllOperateContract.archiveImport": "档案柜预导入", "batchOrAllOperateContract.collectImport": "采集导入", "batchOrAllOperateContract.audit": "全件契約情報自動入力", "batchOrAllOperateContract.realNameAnnualVerify": "実名年次審査", "batchOrAllOperateContract.customInfoAnnualVerify": "カスタム資料の年次審査", "batchOrAllOperateContract.transfer": "資料の転送", "batchOrAllOperateContract.queryResultsExport": "相对方企业查询结果导出", "batchOrAllOperateContract.status0": "等待进行", "batchOrAllOperateContract.status1": "进行中，请稍后...", "batchOrAllOperateContract.status2": "{day}天后过期", "batchOrAllOperateContract.status3": "已过期", "batchOrAllOperateContract.status4": "任务失败", "batchOrAllOperateContract.status5": "已取消", "batchOrAllOperateContract.download": "レコードを使用したエクスポート", "batchOrAllOperateContract.useRecordExport": "使用记录导出", "batchOrAllOperateContract.batchChangeStatus": "一括期限切れ後の延長", "batchOrAllOperateContract.batchSetTag": "タグの一括設定", "batchOrAllOperateContract.batchArchive": "一括ファイリング", "batchOrAllOperateContract.batchRemind": "一括リマインダー", "batchOrAllOperateContract.batchRevoke": "一括取り下げ", "batchOrAllOperateContract.batchExport": "一括エクスポート明細", "batchOrAllOperateContract.batchImport": "契約書インポート", "batchOrAllOperateContract.batchSend": "大量発送です", "batchOrAllOperateContract.batchExportDownload": "ダウンロード", "batchOrAllOperateContract.view": "表示", "batchOrAllOperateContract.downloadLinkFailure": "リンクは無効です", "batchOrAllOperateContract.downloadCancel": "キャンセル", "batchOrAllOperateContract.downloadError": "ダウンロードエラー", "batchOrAllOperateContract.batchExportFail": "エクスポートエラー", "batchOrAllOperateContract.batchTransfer": "一括引き継ぎ", "batchOrAllOperateContract.batchApproval": "一括審査", "batchOrAllOperateContract.batchDownload": "一括ダウンロード", "batchOrAllOperateContract.zip": "圧縮ファイル", "batchOrAllOperateContract.batchModifyLife": "契約満期日の一括修正", "batchOrAllOperateContract.batchModifyTip": "契約満期日は契約締切日ではありません。契約リマインダーを設定する場合、契約締切日を別に設定してください。", "batchOrAllOperateContract.batchSign": "一括署名", "batchOrAllOperateContract.notStart": "実行中", "batchOrAllOperateContract.doing": "実行中", "batchOrAllOperateContract.discontinue": "停止しました", "batchOrAllOperateContract.viewProgress": "進度", "batchOrAllOperateContract.operateProgress": "タスクの進捗状況です", "batchOrAllOperateContract.tip": "注意", "batchOrAllOperateContract.discontinueOperate": "タスク終了", "batchOrAllOperateContract.confirmDiscontinue": "タスク終了後、処理されていない契約書は再実行する必要があります。", "batchOrAllOperateContract.operateSuccess": "操作成功です", "batchOrAllOperateContract.taskTerminated": "任務は終了しました", "batchOrAllOperateContract.done": "完了済み", "batchOrAllOperateContract.detail": "{totalCount}件の処理が必要で、{nowCount}件処理済です。処理結果は、最終の処理ログに準じます。", "batchOrAllOperateContract.moreBatch": "その他一括操作", "batchOrAllOperateContract.batchLog": "一括操作記録", "batchOrAllOperateContract.feedback": "アンケートフィードバック", "batchOrAllOperateContract.tagTip": "企業にて新規作成されたタグは当該企業の発信する契約書の中だけで設定します。その他企業が発信する契約書には設定できません。", "batchOrAllOperateContract.changeTip": "引き継ぎ操作は「署名期限切れ」の契約書の状態を「署名中」に修正し、契約書の署名を再開するのに役に立ちます。", "batchOrAllOperateContract.remindFooterTip": "当日すでに6回リマインダーされたユーザーや署名/審査の順番が回っていないユーザーにはリマインドされません。", "batchOrAllOperateContract.signTip.tip1": "下記の署名要件を含む契約書はPC用ウェブサイトで一括署名できません：", "batchOrAllOperateContract.signTip.tip2": "（1）署名前にかならず下記の操作を終わらせてからでなければ署名する契約書を操作できません。：署名前の審査/契約書全文の閲読/契約書業務フィールドへの入力/契約付属資料の提出/契約主体資料の提出/実名認証の完了/指定印章の入手/業務照合印を通した場合。", "batchOrAllOperateContract.signTip.tip3": "（2）署名時には下記操作の完了を必要とする契約書：必須手書き署名", "batchOrAllOperateContract.signTip.tip4": "アドバイスとして：一部ずつこれらの契約書に単独で署名するか、2次元コードを読み取り携帯電話で一括署名するようにしてください。", "batchOrAllOperateContract.downloadFooterTip": "企業がお客様にダウンロード権限を与えていない場合、この企業の契約書をダウンロードできません。", "batchOrAllOperateContract.setOption1": "チェックを入れた契約書のみを設定", "batchOrAllOperateContract.changeOption1": "チェックを入れた契約書のみを修正", "batchOrAllOperateContract.revokeOption1": "チェックを入れた契約書のみを却下", "batchOrAllOperateContract.remindOption1": "チェックを入れた契約書のみをリマインダー", "batchOrAllOperateContract.transferOption1": "チェックを入れた契約書のみを引き継ぎ", "batchOrAllOperateContract.outputOption1": "チェックを入れた契約書詳細のみをエクスポート", "batchOrAllOperateContract.approvalOption1": "チェックを入れた契約書のみを審査", "batchOrAllOperateContract.signOption1": "チェックを入れた契約書のみを署名", "batchOrAllOperateContract.downloadOption1": "チェックを入れた契約書のみをダウンロード", "batchOrAllOperateContract.setOption2": "リストの中のすべての契約書書で設定し、現在のページでチェックしていない契約書やそのリストの他のページの契約書を含みます。ファイリングしたフォルダーもしくは検索機能の助けを借りて、一括操作する契約書範囲を調整できます。", "batchOrAllOperateContract.changeOption2": "リストの中のすべての契約書書をで修正し、現在のページでチェックしていない契約書やそのリストの他のページの契約書を含みます。ファイリングしたフォルダーもしくは検索機能の助けを借りて、一括操作する契約書範囲を調整できます。", "batchOrAllOperateContract.revokeOption2": "現在のリスト内のすべての審査中および署名中の契約書を却下し、現在のページでチェックしていない契約書やそのリストの他のページの契約書を含みます。ファイリングしたフォルダーもしくは検索機能の助けを借りて、一括操作する契約書範囲を調整できます。", "batchOrAllOperateContract.remindOption2": "現在のリスト内のの審査中および署名中の契約書をリマインドし、現在のページでチェックしていない契約書やそのリストの他のページの契約書を含みます。ファイリングしたフォルダーもしくは検索機能の助けを借りて、一括操作する契約書範囲を調整できます。", "batchOrAllOperateContract.transferOption2": "現在のリスト内のすべての契約書書を引き継ぎし、現在のページでチェックしていない契約書やそのリストの他のページの契約書を含みます。ファイリングしたフォルダーもしくは検索機能の助けを借りて、一括操作する契約書範囲を調整できます。", "batchOrAllOperateContract.outputOption2": "現在のリスト内のすべて契約書の契約明細をエクスポートし、現在のページでチェックしていない契約書やそのリストの他のページの契約書を含みます。ファイリングしたフォルダーもしくは検索機能の助けを借りて、一括エクスポートする契約書詳細の契約書範囲を調整できます。", "batchOrAllOperateContract.approvalOption2": "現在のリスト内のすべての審査待ちの契約書を審査し、現在のページでチェックしていない契約書やそのリストの他のページの契約書を含みます。ファイリングしたフォルダーもしくは検索機能の助けを借りて、一括操作する契約書範囲を調整できます。", "batchOrAllOperateContract.signOption2": "現在のリスト内の契約書に署名し、現在のページでチェックしていない契約書やそのリストの他のページの契約書を含みます。ファイリングしたフォルダーもしくは検索機能の助けを借りて、一括操作する契約書範囲を調整できます。", "batchOrAllOperateContract.batchExportTip": "1万本の契約書詳細を超えてはなりません。より上のバージョンを開通させれば一回でより多くの契約書詳細をエクスポートできます | 5万本の契約書詳細を超えてはなりません", "batchOrAllOperateContract.downloadOption2": "現在のリスト内のすべての契約書書をダウンロードし、現在のページでチェックしていない契約書やそのリストの他のページの契約書を含みます。ファイリングしたフォルダーもしくは検索機能の助けを借りて、一括操作する契約書範囲を調整できます。", "rejectSigner.tipTitle": "再署名の却下について", "rejectSigner.tipContent": "先に再署名を却下する署名フィールド（署名/捺印/署名者が記入するフィールド）をチェックし、「再署名の却下」ボタンを再クリックしてください。", "rejectSigner.noTip": "再度リマインドしません", "rejectSigner.iKnow": "わかりました。", "rejectSigner.rejectBtn": "再署名の却下", "rejectSigner.noOptionTip": "先に再署名を却下する署名フィールド(署名/捺印/署名者が記入するフィールド)を選択し、「再署名の却下」ボタンを再クリックしてください。", "rejectSigner.writeMustKnow": "再署名の入力について", "rejectSigner.confirm": "確定", "rejectSigner.cancel": "キャンセル", "rejectSigner.success": "却下完了", "rejectSigner.fail": "現在再署名しているユーザーは再署名を却下できません。ユーザーの署名が終わるのを待ってから再操作してください。", "rejectSigner.mustKnowPlaceHolder": "オプション、255文字以下とします", "rejectSigner.mustKnowTip": "契約のお知らせを完全にすると、署名者が再署名するのに役立ちます", "rejectSigner.placeTop": "将重叠的顶层置底", "counterDialog.longTime": "現在契約書を発信しています。全部を発信し終わるまでおよそXXかかります ", "counterDialog.close": "わかりました。", "counterDialog.minutes": "分", "timingSend.individualApprovalLimit": "グループ会社各社は各自の承認プロセスを設定できます，「定時送信」機能は使用できません。", "timingSend.hasNoTimingSendFeature": "お客様の企業はこの機能を開通していません", "timingSend.approveTopTip": "定時審査：「発信前審査」の一番目の審査者が契約書を受け取った時間：", "timingSend.approveBottomTip": "次の審査者が契約書を受け取った時間とは、上位の審査者が審査承認をした時間です。", "timingSend.signTopTip": "送信時刻（最初に署名する署名者に送信する時刻）: | 定時署名: 選択した署名者が契約書を受け取った時間：", "timingSend.signBottomTip1": "設定した定時発信時間が審査完了時間よりも早ければ、審査完了後にすぐ発信します。", "timingSend.signBottomTip2": "次の審査者が契約書を受け取った時間とは、上位の審査者が審査完了した時間です。", "timingSend.signBottomTip3": "最初の署名者に承認フローが設定されている場合は、承認完了後に送信されます。 | 署名者が所属する企業が「署名前審査」を設定していれば、審査者が契約書を受け取った時間となります。審査承認後、署名者は契約書を受け取ることができます。", "timingSend.receiveNow": "直ぐに送信", "timingSend.previousDay": "前日", "timingSend.nextDay": "翌日", "timingSend.timeLimitTip1": "定時署名の時間は定時審査時間より早くてはなりません", "timingSend.timeLimitTip2": "定時発信の時間は少なくとも現在の時間の15分後に設定しなければなりません。", "batchOrAllOperateContract.contractOutPut.outOfQuantity": "{num}件の契約書を選択しており、上限の5万件を超えているため、一括エクスポートできません。", "autoSignDialog.waitTip": "注意", "autoSignDialog.errorTip": "自動署名エラー原因", "autoSignDialog.title": "自動署名エラー原因注意", "autoSignDialog.reason": "以下の条件を満たしていないため、お客様は自動署名機能を使用して、この契約書に署名できません：", "autoSignDialog.wait": "現在自動署名を準備しています。", "tagManage.deleteDone": "削除に成功", "tagManage.setDone": "設定に成功", "tagManage.noEditPermission": "契約履行アラームを設定する権限がありません", "tagManage.addLabel": "ラベルを追加", "tagManage.delLabel": "ラベルを削除", "tagManage.slotTip1": "・設定規則は現在のフォルダの契約書に対し有効であり、企業コンソール-ラベル管理の設定に影響しません", "tagManage.slotTip2": "・フォルダとフォルダの間の設定規則も互いに影響しません。", "tagManage.afterDelLblTip": "削除すると、契約フォルダの契約書にあるラベルはなくなります", "tagManage.delLblAA": "“{name}”ラベルを削除", "tagManage.confirm": "確認", "tagManage.cancel": "キャンセル", "tagManage.noTagTip": "企業コンソール-ラベル管理にラベルが設定されていません", "tagManage.addTag": "ラベルを追加", "tagManage.updateTip": "契約のラベルは次のように更新されます:", "tagManage.noUseLabel": "ラベルなし", "tagManage.confirmTitle": "注意", "tagManage.send": "から送信された契約:", "settingDownloadFileName.settingTitle": "契約書ダウンロード名を設定", "settingDownloadFileName.defaultName": "デフォルトファイル名", "settingDownloadFileName.titleAndId": "(契約書タイトル+契約書ID)", "settingDownloadFileName.defineName": "カスタマイズファイル名", "settingDownloadFileName.defineNameTip": "（5つまで選択可能）", "settingDownloadFileName.limitNumTip": "カスタマイズファイル名を少なくとも一つ選択してください", "settingDownloadFileName.noInputValueTip": "カスタマイズファイル名を入力してください", "settingDownloadFileName.hasIllegalCharacter": "カスタマイズファイル名に不正な文字コードが含まれています", "settingDownloadFileName.inputPlaceholder": "ファイル名を入力してください", "settingDownloadFileName.contractTitle": "契約書タイトル", "settingDownloadFileName.contractId": "契約書ID", "settingDownloadFileName.receiver": "受信者", "settingDownloadFileName.personOrEntName": "個人/企業名", "settingDownloadFileName.hasSelectedCase": "選択された組み合わせ：", "settingDownloadFileName.noRemindBtn": "ログイン期間を今後表示しない", "settingDownloadFileName.tip": "注：采用私有存储方式的合同定义的文件名称将无法生效", "pageTitle.doc.docList": "契約書管理", "pageTitle.doc.docDetail": "契約内容", "pageTitle.doc.docExport": "契約のエクスポート", "pageTitle.doc.docView": "契約のプレビュー", "pageTitle.doc.sealApply": "申請スタンプ", "pageTitle.doc.authIntercept": "個人の実名傍受", "pageTitle.doc.entAuthIntercept": "企業実名傍受", "pageTitle.doc.applyJoinEnt": "入社を申し込む", "pageTitle.doc.batchLog": "バッチ操作の結果", "pageTitle.doc.rejectSigner": "再署名の拒否", "pageTitle.dynamicTemplate.set": "テンプレートを設定", "pageTitle.dynamicTemplate.preview": "ダイナミック テンプレート効果のプレビュー", "pageTitle.entDoc.manage": "フルフィルメント フォルダの管理", "pageTitle.entDoc.permission": "権限管理", "pageTitle.entDoc.list": "エンタープライズ パフォーマンス管理", "pageTitle.entDoc.detail": "エンタープライズ契約の詳細", "pageTitle.send.prepare": "テンプレートを選択", "pageTitle.send.send": "契約書を送る", "pageTitle.send.batchImport": "契約書を一括送信", "pageTitle.send.inputField": "送信者のビジネス フィールドに入力します。", "pageTitle.permission.confirm": "権限申請", "pageTitle.permission.apply": "アクセスリクエスト", "pageTitle.template.list": "テンプレート一覧", "pageTitle.template.childList": "テンプレートのサブリスト", "pageTitle.template.detail": "テンプレートの詳細", "pageTitle.template.permission": "権限管理", "pageTitle.template.config": "テンプレートを設定", "pageTitle.template.approval": "テンプレート承認", "transferContract.transferContract": "転送", "transferContract.contractTransfer": "転送", "transferContract.tip": "提示", "transferContract.confirmTip": "移交后，将不再由您持有，您的管理列表中无法搜索到该份合同。确定移交吗?", "transferContract.accountNameTip": "接收人姓名与账号不匹配，请确保接收人信息真实准确。", "transferContract.noJoinEntTip": "该接收人账号未加入企业，移交后对方有可能无法完成签署，是否确定移交。", "transferContract.success": "移交成功", "transferContract.resultInfo": "已成功移交给{receiverName}（{receiverAccount}），他将收到签署通知。", "transferContract.verifyCodeTip1": "您可通过查验码或立即跳转小程序。", "transferContract.verifyCodeTip2": "查看签署进度。查看后，「上上签查合同」小程序将为您保留查验记录。", "transferContract.downloadVerifyCode": "下载查验码", "transferContract.receiverAccount": "接收人账号", "transferContract.receiverName": "接收人姓名", "transferContract.receiverAccountPlaceholder": "请输入接收人手机号或邮箱", "transferContract.accountErrorTip": "请输入正确的账号", "transferContract.receiverNamePlaceholder": "请输入接收人真实姓名", "transferContract.transferNamePlaceholder": "请输入您的姓名", "transferContract.transferName": "移交人姓名", "transferContract.transferAccount": "移交人账号", "transferContract.transferTip": "请注意接收人账号务必填写正确，如若移交错误，需要联系企业主管理员将合同重新移交给正确签署人。", "transferContract.adminAccount": "账号：{entAdminAccount}", "transferContract.adminName": "企业昵称：{entAdminName}", "transferContract.admin": "主管理员", "transferContract.confirmTransfer": "确认移交", "transferContract.pleaseInputReceiverInfo": "请填写接收人信息：", "transferContract.pleaseInputTransferInfo": "请填写移交人信息：", "transferContract.name": "姓名：", "transferContract.account": "账号：", "transferContract.transfer": "移交", "transferContract.cancel": "取消", "ssoSendDialog.title": "提示", "ssoSendDialog.main": "请确认，您将以个人身份发送合同?", "ssoSendDialog.tip": "如需开展公司业务，请点击右上角，切换至对应的企业主体后，再发送合同。", "ssoSendDialog.confirm": "去切换至企业", "ssoSendDialog.cancel": "使用个人主体", "contractComparison.translateBtn": "合同翻译", "contractComparison.reapplytn": "捺印申請", "contractComparison.waitforStamp": "この契約書は{person}（{account}）の捺印を待っています。捺印担当者を変更する必要がありますか？", "contractComparison.comparisonBtn": "契約書比較", "contractComparison.extractionBtn": "契約書抽出", "contractComparison.documentSelect": "{type}するファイルを選択してください。Word・PDF形式のみ対応可能です", "contractComparison.noPermission": "この高度な機能については、専用のチェックインスタッフにお問い合わせいただくか、カスタマーサービス(400-993-6665)までお電話ください。", "contractComparison.prompt": "ヒント", "contractComparison.sendedContract": "発行元の契約", "contractComparison.uploadedContract": "アップロードした契約ファイル", "contractComparison.comparisonResult": "結果を比較します", "contractComparison.differences": "（合計{num}つの相違点）", "contractComparison.pageNum": "ページ{page}", "contractComparison.difference": "差異{num}", "contractComparison.uploadTitle": "{type}するファイルをアップロードしてください", "contractComparison.uploadError": "Word・PDF形式のみ対応可能です", "contractComparison.download": "契約をダウンロードします", "contractComparison.log.title": "契約書比較履歴", "contractComparison.log.detail": "詳細", "contractComparison.log.comparing": "ペアで...", "contractComparison.log.refresh": "更新します", "contractComparison.log.download": "結果を比較してダウンロードします", "contractComparison.log.toDetail": "比較の詳細を確認", "contractComparison.doCompare": "比較", "contractComparison.doTranslate": "翻訳", "contractComparison.doExtract": "ちゅうしゅつ", "safeBox.guideTitle": "合同保险柜", "safeBox.guideTitleTip": "保密合同，只有合同持有人（参与合同的人）才能查看", "safeBox.howToUse": "如何使用保险柜？", "safeBox.useGuide": "查看使用说明", "safeBox.step1": "第一步：前往企业控制台-角色管理页，为角色勾选合同权限里的的合同保密项。", "safeBox.step2": "第二步：拥有权限后，在合同详情页可以将合同移入保险柜。", "safeBox.step3": "第三步：保险柜内的保密合同在合同管理页会带有“秘”图标。", "safeBox.hasNoPermission": "合同已被移入保险柜，仅合同持有人（参与合同的人）可以查看", "safeBox.noSafeBoxPermission": "此合同为保密合同，只有合同持有人才能查看；您可以联系合同持有人将合同恢复为正常合同后再查看", "safeBox.hideInfoPlaceholder": "保险柜隐藏数据", "safeBox.receiverTip": "参与了合同，含发送、审批、签署、补全以及被抄送的本企业/集团成员账号", "safeBox.entFolderTip": "通过履约文件夹分享后，可以被更多人查看，请注意是否有泄密风险", "safeBox.view": "查看", "safeBox.safeBox": "保险柜", "lang": "ja", "customsCheck.customsStandardCheck": "税関基準検査", "customsCheck.checking": "関税書類の標準検査中です。しばらくお待ちください...", "customsCheck.customsFontRequire": "税関フォント要件", "customsCheck.customsFontUse": "以下の１４種類の標準フォントのうち１つまたは複数を使用してください", "customsCheck.customsCheckResult": "税関ファイル検査結果", "customsCheck.fileNameRequire": "ファイル名の長さが英数字６４文字または漢字３２文字を超えています", "customsCheck.fontErr": "ファイルのフォントは | となり、税関の要件を満たしていません", "customsCheck.checkFontRequirement": "税関のフォント要件を確認し", "customsCheck.emptyPageErr": "ファイルに空白ページがあります。空白ページを削除してから送信してください，空白ページは | ページ目です", "customsCheck.checkStandard": "検査内容は下記基準に基づく", "customsCheck.checkInGov": "具体的には税関システムで実際に署名した結果に準拠します", "customsCheck.nameMulErr": "ファイル名の長さ検査（英数字６４文字または漢字３２文字以内）", "customsCheck.emptyMulErr": "ファイルの空白ページ検査（ファイルに空白ページが存在してはなりません）", "customsCheck.checkFont": "ファイルフォント検査", "customsCheck.notSatisfy": "が要件を満たしていません", "customsCheck.useCustomsFont": "適切なフォントを使用してください。", "customsCheck.checkSuccess": "検査に合格しました", "customsCheck.checkPass": "検査合格", "customsCheck.document": "ファイル", "docContentTableCol.noReceiver": "どのような受取人も追加していません", "docSlider.check2017ContractTip.title": "注意", "docSlider.check2017ContractTip.msg": "新しく開いたウインドウページの中で２０１７年前の契約書を確認してください", "docSlider.check2017ContractTip.confirm": "わかりました", "docSlider.check2017Contract": "2017年以前の契約書を確認", "docSlider.all": "全部", "docSlider.unarchive": "ファイリングされていません", "docSlider.archived": "ファイリング済みです", "docSlider.shareToMemberTips.title": "お客様が共有しようとしているのは{name}の全構成員です", "docSlider.shareToMemberTips.share": "共有", "docSlider.shareToMemberTips.confirm": "確定", "docSlider.shareToMemberTips.cancel": "キャンセル", "docSlider.deleteFolderTips.title": "削除した後、フォルダー内の契約書を「すべての契約書書」に移動します。削除しますか？", "docSlider.deleteFolderTips.delete": "削除", "docSlider.deleteFolderTips.confirm": "確定", "docSlider.deleteFolderTips.cancel": "キャンセル", "docSlider.shareToMeFolder": "マイフォルダーを他の人に共有", "docSlider.sharedFolder": "フォルダーの共有", "docSlider.cancelShare": "共有の取り消し", "docSlider.syncEntFolder": "契約フォルダーと同期する", "docSlider.stopSyncEntFolder": "契約フォルダーの同期を停止する", "docSlider.share": "共有", "docSlider.initSign": "契約書を送信", "docSlider.otherSource": "その他引用元の文書を確認", "docSlider.otherSourceTip": "クリックするとインターフェイスのプラットフォームが発信した契約書・内容証明文書・ベストサインV2契約書を確認できます", "docSlider.toggleSearchBar": "検索を非表示", "docSlider.toggleSearchBar1": "検索を非表示", "docSlider.toggleSearchBar2": "検索を表示", "docSlider.fastOperate": "クイック操作", "docSlider.operateStatus.signing": "契約中", "docSlider.operateStatus.needMeOperate": "私が操作する必要があります", "docSlider.operateStatus.inApproval": "審査中", "docSlider.operateStatus.needOthersSign": "他の人の署名を必要です", "docSlider.operateStatus.closing": "まもなく契約期限切れです", "docSlider.operateStatus.signComplete": "締結済", "docSlider.operateStatus.closed": "すでに契約期限切れです", "docSlider.allDocs": "全てのファイル", "docSlider.allDocsType.inbox": "受信ボックス", "docSlider.allDocsType.outbox": "送信ボックス", "docSlider.allDocsType.closing": "契約がまもなく満期です", "docSlider.allDocsType.closed": "契約満了です", "docSlider.allDocsType.draft": "下書き", "docSlider.folder": "個人フォルダー", "docSlider.rename": "名前の変更", "docSlider.delete": "削除", "docSlider.enterpriseFolder": "企業フォルダー（契約書種類）", "docSlider.enterpriseFilingContract": "企業ファイリング契約書", "docSlider.noAuthority": "新版の契約書管理ポータルの権限がありません", "docSlider.noAuthorityTips.info": "開通後、企業が効率よく契約書の管理に役立つ「企業ファイリング契約書」管理の機能を体験できます。", "docSlider.noAuthorityTips.toDetail": "詳細確認", "docSlider.noAuthorityTips.cancel": "キャンセル", "docSlider.noAuthorityTips.open": "開通", "docSlider.assignAuthority": "管理者に権限をお客様に分配するよう連絡してください", "docSlider.msgBox.tips": "注意", "docSlider.msgBox.info": "新しく開いたウインドウページの中で「その他引用元のファイル」を確認してください", "docSlider.msgBox.confirm": "わかりました", "docSlider.addFolder": "フォルダーの追加", "docSlider.switchEntFail": "企業ファイリング契約書の切り替えエラーです。時間をおいてから試してください。", "docSlider.openFail": "開通エラー", "docSlider.openLater": "ログインに失敗しました。時間をおいてから試してください。", "docSlider.notPublicUser": "このユーザーはパブリッククラウドユーザーではありません", "docSlider.notEntUser": "このユーザーは企業ユーザーではありません", "docSlider.cantReadEntFolder": "このユーザーは企業フォルダー（契約書種類）を確認できません", "docSlider.stick": "ピン留め", "docSlider.autoArchive": "契約書の自動アーカイブ", "docSlider.performanceManage": "进入企业履约管理", "docContentTop.searchTitlePlaceholder": "契約名/発信者名/発信者企業名", "docContentTop.moreSearch": "詳細検索", "docContentTop.output": "エクスポート", "docContentTop.import": "インポート", "docContentTop.allContracts": "すべての契約書", "docContentTop.listConfig": "表示設定", "docContentTop.filterList.signStatus": "契約状態", "docContentTop.filterList.archiveFolders": "フォルダー", "docContentTop.filterList.contractType": "契約書種類", "docContentTop.filterList.sharedByMe": "私が共有したもの", "docContentTop.filterList.sharedToMe": "共有されたもの", "docContentTop.filterList.allFiles": "全ファイル", "docContentTop.companyTree": "組織図", "docContentTop.unmovedContract": "移動していない契約書", "docContentTop.contractLabelList.labor": "労働契約", "docContentTop.contractLabelList.borrow": "借款契約書", "docContentTop.contractLabelList.legal": "法律契約書", "docContentTop.contractLabelList.loan": "ローン契約", "docContentTop.contractLabelList.transfer": "譲渡契約書", "docContentTop.search.contractNum": "契約番号", "docContentTop.search.contractNumPlaceholder": "契約書番号を入力してください", "docContentTop.search.sender": "送信者", "docContentTop.search.senderPlaceholder": "発信者名もしくはアカウント", "docContentTop.search.receiver": "受信者", "docContentTop.search.receiverPlaceholder": "受信者名もしくはアカウント", "docContentTop.search.sendTime": "契約発信日時", "docContentTop.search.signDeadline": "署名期限", "docContentTop.search.timeStartPlaceholder": "開始日時を選択してください", "docContentTop.search.timeEndPlaceholder": "終了日時を選択してください", "docContentTop.search.source": "引用元プラットフォーム", "docContentTop.search.ssq": "ベストサイン", "docContentTop.search.search": "検索", "docContentTop.searchMsg.contractNum": "正確な契約書番号を入力してください", "docContentTop.searchMsg.sender": "正確な発信者名もしくはアカウントを入力してください", "docContentTop.searchMsg.receiver": "正確な受信者名もしくはアカウントを入力してください", "docContentTop.all": "全部", "docContentTop.signStatus.needMeApproval": "私の審査待ち", "docContentTop.signStatus.needMeSign": "署名待ち ", "docContentTop.signStatus.inApproval": "審査中", "docContentTop.signStatus.needOthersSign": "他の人の署名を必要です", "docContentTop.signStatus.signComplete": "締結済", "docContentTop.signStatus.signOverdue": "署名期限切れ", "docContentTop.signStatus.rejected": "拒否済み", "docContentTop.signStatus.revoked": "却下済み", "docContentTop.searchAll": "すべて選択", "docContentTop.confirm": "確定", "docContentTop.reset": "リセット", "docContentTop.selectRange": "日付範囲を選択し", "docContentTop.datePicker.weekend": "一週間以内", "docContentTop.datePicker.month": "一ヶ月以内", "docContentTop.datePicker.month3": "三ヶ月以内", "docContentTop.popover.listStatus": "リスト状態", "docContentTop.popover.reset": "初期状態への回復", "docContentTop.popover.showLabel": "フィールドの表示", "docContentTop.popover.showLabelOperate": "（ドラッグ＆ドロップで順番を調整、「X」のクリックで削除）", "docContentTop.popover.most": "最多50個", "docContentTop.popover.hideLabel": "フィールドを非表示", "docContentTop.popover.hideLabelOperate": "（上の領域にドラッグ＆ドロップして追加）", "docContentTop.popover.confirm": "確定", "docContentTop.popover.cancel": "キャンセル", "docContentTable.hideField": "フィールドを非表示", "docContentTable.showField": "フィールドを表示", "docContentTable.notFoundField": "テンプレートフィールドが見つかりません", "docContentTable.notFoundFieldTips.0": "テンプレートのなかで使用した名称が「xxx」という名前の一時的なフィールドが使用されており、それを取得する必要がある場合:", "docContentTable.notFoundFieldTips.1": "企業管理コンソール - フィールド管理 - 内容フィールドページで、「xxx」という名前のフィールドを設定し、約15粉末と、ここに「xxx」フィールドが表示されます。", "docContentTable.userSigned": "当該ユーザーはすでに署名しています", "docContentTable.userReject": "当該ユーザーは拒否済みです", "docContentTable.userNotSign": "当該ユーザーはまだ署名していません", "docContentTable.resend": "再送", "docContentTable.claim": "署名の受領", "docContentTable.proxySign": "代理署名", "docContentTable.contractMoveToAllFolder": "契約書をすべての契約書書の中に移動しました", "docContentTable.contractMoveToRecycle": "契約書をゴミ箱の中に移動しました", "docContentTable.contractRestoreToAllFolder": "契約書を「すべての契約書書」フォルダーに戻すことが完了しました", "docContentTable.notAllowDeleteContract": "現在のフォルダーは契約書の削除を許可していません", "docContentTable.searchTooMuchResultTip": "現在検索結果が多すぎます。お客様の検索条件を最適化してください", "docContentTable.cannotMoveToRecycle": "受け取り待ちの契約書は、ゴミ箱への移動に対応していません", "docContentTable.moveToRecycleTip": "ゴミ箱に移動しても契約状態と操作には影響しません", "docContentTable.statusIconDesc.draft": "下書き", "docContentTable.statusIconDesc.signning": "署名中", "docContentTable.statusIconDesc.reject": "署名拒否済み", "docContentTable.statusIconDesc.invalid": "契約破棄済み", "docContentTable.statusIconDesc.complete": "完了済み", "docContentTable.statusIconDesc.revoked": "契約は取り下げられました", "docContentTable.statusIconDesc.signOverdue": "契約は期限切れです", "docContentTable.statusIconDesc.sendApprove": "審査送信中", "docContentTable.statusIconDesc.approveReject": "審査却下", "docContentTable.deleteLableSuccess": "タグの切り離し完了", "docContentTable.clickDeleteLabel": "タグの解除をクリック", "docContentTable.canNotBatchDownloadTip": "一括ダウンロードできません。契約書をダウンロードするには、実名情報が一致しているか、契約書の発信者からダウンロードコードを取得する必要があります", "docContentTable.noCanDownloadContract": "ダウンロード可能な契約書なし", "docContentTable.downloadRemainContract": "その他の契約書のダウンロード", "docContentTable.cancleDownload": "ダウンロードの取り消し", "docContentTable.remind": "リマインダー", "docContentTable.needDownloadCodeTip": "契約書の発信者からダウンロードコードを取得し、一つずつダウンロードします。", "docContentTable.needAuthTip": " 個人実名認証をするか、現在の実名情報が一致してからでないと契約書をダウンロードできません。", "docContentTable.revokeContractIdsTip": "契約書が抹消されており、ダウンロードできません。", "docContentTable.canNotDownload": "一括ダウンロードできません。", "docContentTable.partCanNotDownload": "一部の契約書で一括ダウンロードできません", "docContentTable.noCanDeleteContract": "削除可能な契約書なし", "docContentTable.transferSucess": "転送完了", "docContentTable.transferFailure": "転送失敗", "docContentTable.notRightToTransferTip": "選択した契約書は引き継ぎできるようになっていません。再度契約書を選択し試してください", "docContentTable.batchSetTag": "タグの一括設定", "docContentTable.setTag": "タグの設定", "docContentTable.transfer": "転送", "docContentTable.archive": "ファイリング", "docContentTable.invalid": "無効", "docContentTable.moveOutRecycle": "復旧", "docContentTable.moveToRecycle": "ゴミ箱へ移動", "docContentTable.exportSignLink": "署名済みリンクをエクスポート", "docContentTable.exportSignLink.tip1": "署名リンクの一括エクスポート", "docContentTable.exportSignLink.tip2": "エクスポートする署名リンクは、以下すべての条件を満たす必要があります：", "docContentTable.exportSignLink.tip3": "・現在のページでチェックされている契約の署名リンク;", "docContentTable.exportSignLink.tip4": "・あなたの所属する企業／グループが送信した契約リンク;", "docContentTable.exportSignLink.tip5": "・現在署名者の署名待ちの契約リンク。", "docContentTable.changeSigner": "署名者の変更", "docContentTable.changeSignerDialog.confirm": "確認", "docContentTable.changeSignerDialog.cancel": "キャンセル", "docContentTable.changeSignerDialog.title": "契約書に署名していない個人署名者", "docContentTable.changeSignerDialog.tips": "署名順番の来ていない個人が変更することはできません", "docContentTable.changeSignerTable.signerName": "氏名", "docContentTable.changeSignerTable.idCard": "身分証", "docContentTable.changeSignerTable.account": "携帯電話/受信メールアドレス", "docContentTable.changeSignerTable.roleName": "契約上の役割", "docContentTable.changeSignerOption.must": "必須項目", "docContentTable.changeSignerOption.optional": "オプション", "docContentTable.changeSignerVerify.invalidSubmit": "提出したデータにフォーマットエラーがあります", "docContentTable.changeSignerVerify.success": "保存完了", "docContentTable.changeSignerVerify.fail": "保存エラー", "docContentTable.changeSignerVerify.invalidIDCard": "身分証フォーマットにエラーがあります", "docContentTable.changeSignerVerify.invalidAccount": "受信する携帯電話/メールアドレスフォーマットにエラーがあります", "docContentTable.changeSignerVerify.invalidSignerName": "氏名にフォーマットエラーがあります", "docContentTable.batchOperateLoading": "一括操作をロード中です", "docContentTable.selectedContracts": "{num}件のファイルを選択しています", "docContentTable.batchBtn.sign": "一括署名", "docContentTable.batchBtn.approval": "一括審査", "docContentTable.batchBtn.remind": "一括リマインダー", "docContentTable.batchBtn.revoke": "一括取り下げ", "docContentTable.batchBtn.download": "一括ダウンロード", "docContentTable.batchBtn.delete": "一括削除", "docContentTable.batchBtn.move": "一括移動", "docContentTable.operate": "操作", "docContentTable.searchNull": "このタイプの契約書は見つかっていません", "docContentTable.toDetail": "詳細をスキップ", "docContentTable.download": "ダウンロード", "docContentTable.move": "移動", "docContentTable.reSend": "再送開始", "docContentTable.moveCancel": "移動の取り消し", "docContentTable.signer": "受信先", "docContentTable.status": "状態", "docContentTable.sendDate": "発送日", "docContentTable.deadline": "署名期限", "docContentTable.contractStatus.needMeSign": "私の署名待ちです", "docContentTable.contractStatus.needMeApproval": "私の承認が必要です", "docContentTable.contractStatus.inApproval": "審査中", "docContentTable.contractStatus.needOthersSign": "他の人の署名を必要です", "docContentTable.contractStatus.signComplete": "締結済", "docContentTable.contractStatus.draft": "下書き", "docContentTable.contractStatus.signOverdue": "署名期限切れ", "docContentTable.contractStatus.rejected": "拒否済み", "docContentTable.contractStatus.revoked": "却下済み", "docContentTable.contractStatus.beRejected": "拒否されました", "docContentTable.contractStatus.deadline": "署名期限", "docContentTable.contractStatus.invalid": "無効済み", "docContentTable.signStatus": "契約状態", "docContentTable.catchMap.download": "ダウンロード", "docContentTable.catchMap.reject": "拒否", "docContentTable.catchMap.revoke": "却下", "docContentTable.catchMap.delete": "削除", "docContentTable.catchMap.cantOperate": "{operate}契約できません", "docContentTable.catchMap.hybridNetHeader": "発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは発信者の契約ストレージのサーバーに接続することができません。", "docContentTable.catchMap.hybridNetMsg": "アドバイス：ネットワークが正常かどうか検査してください", "docContentTable.catchMap.checkNet": "ネットワークが正常かどうか検査してください", "docContentTable.confirm": "確定", "docContentTable.cancel": "キャンセル", "docContentTable.continue": "ダウンロードの継続", "docContentTable.next": "続ける", "docContentTable.delete": "グループを移動", "docContentTable.searchAll": "すべて選択", "docContentTable.nullToSign": "署名可能なファイルはありません", "docContentTable.nullToApproval": "審査可能なファイルはありません", "docContentTable.nullToRemind": "リマインダー可能なファイルはありません", "docContentTable.nullToRevoke": "取り下げ可能なファイルはありません", "docContentTable.sign": "署名", "docContentTable.approval": "審査", "docContentTable.remindSucc": "リマインド完了", "docContentTable.remindFail": "下記{errorSum} 部の契約書の一括リマインドに失敗しました", "docContentTable.notice": "注意", "docContentTable.revoke": "取り下げ", "docContentTable.revokeReason": "一括取り下げの原因", "docContentTable.batchDownloadTip.msg1": "以下の契約書の発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは発信者の契約ストレージのサーバーに接続することができません。", "docContentTable.batchDownloadTip.msg2": "ダウンロード不可能な契約書は下記のとおりとなります。", "docContentTable.deleteSucc": "削除完了", "docContentTable.giveAuthor": "管理者に権限をお客様に分配するよう連絡してください", "docContentTable.view": "確認", "docContentTable.paperSign": "紙媒体署名 ", "docContentTable.onLineSign": "電子署名", "docContentTable.usePaperSign": "紙媒体署名の有効", "docContentTable.modifyExpires": "契約満期日の修正", "noBizLineDoc.title": "未指定业务线（或接收人尚未加入企业）的合同", "noBizLineDoc.confirm": "确认", "noBizLineDoc.cancel": "取消", "noBizLineDoc.noBizLineTip": "您有{number}份未指定业务线（或接收人尚未加入企业）的合同，请点此前往处理", "docDialog.searchName": "检索名称", "docDialog.inputSearchName": "请输入常用检索设置名称", "docDialog.commonSearch": "よく使う検索", "docDialog.saveCommonSearch": "よく使う検索として保存", "docDialog.setSearchName": "カスタム検索を設定", "docDialog.setSearchNameTip": "为刚保存的常用检索设置名称", "docDialog.autoSetListConfitTip": "使用時に、リスト構成（リストヘッダ）はこれらの検索項目に基づいて更新されます", "docDialog.searchNameNotEmpty": "常用检索名称不能为空", "docDialog.lestSearchContentTip": "少なくとも 1 つの検索内容を入力する必要があります", "docDialog.deleteSearchContent": "このよく使う検索を削除しますか", "docDialog.maxSetCommonSearchAmout": "よく使う検索を最大10個まで設定できます", "docDialog.approvalOpinion.title": "一括審査意見", "docDialog.approvalOpinion.yes": "同意", "docDialog.approvalOpinion.no": "却下", "docDialog.approvalOpinion.result": "審査結果：", "docDialog.approvalOpinion.fillInContent": "審査意見を記入してください：", "docDialog.approvalOpinion.placeHolder": "オプション、255文字以下とします", "docDialog.notice": "注意", "docDialog.ok": "わかりました", "docDialog.inputSenderPersonAndCompany": "送信者名を入力してください", "docDialog.inputReceiverPersonAndCompany": "受信者名を入力してください", "docDialog.contractLblSearch": "契約タグの検索", "docDialog.putAway": "閉じる", "docDialog.unfold": "詳細", "docDialog.searchFor": "検索", "docDialog.contractInvolveTip": "個人もしくは企業構成員の参画する契約書", "docDialog.plsSelect": "選択してください", "docDialog.moreBusinessFields": "詳細(業務フィールド)", "docDialog.confirm": "確認", "docDialog.plsSelectData": "日付を選択してください", "docDialog.addShortcutEntrance": "クイックポータルの追加", "docDialog.reStoreState": "初期状態への回復", "docDialog.showEntrance": "ポータルの表示", "docDialog.hideEntrance": "ポータルを隠す", "docDialog.shortcutManage": "クイックポータルの管理", "docDialog.plsInputShortcutName": "クイックポータル名を入力してください", "docDialog.plsChooseLable": "契約タグを選択してください", "docDialog.plsChooseSignStatus": "操作状態を選択してください", "docDialog.plsInputRoleTip": "業務の役割を入力してください", "docDialog.plsInputInfoTip": "条件情報が完全ではありません。追加してください", "docDialog.accountFormatErrorTip": "正しいメールアドレスを入力してください", "docDialog.finish": "完了", "docDialog.search": "確認", "docDialog.meetAllConditiionTip": "同時に以上の条件を満たす契約書はクイックポータルにあります", "docDialog.plsChooseLblName": "タグ名を選択してください", "docDialog.ifLableContain": "契約書タグに含まれていれば", "docDialog.defineAsLable": "タグの定義に基づく", "docDialog.addCondition": "追加条件", "docDialog.plsInputPlaceholder": "業務の役割を入力してください | 正確なメールアドレス/携帯番号を入力してください", "docDialog.signStatusIsTip": "かつその操作状態は", "docDialog.conditionTypeText.APPROVER": "審査者", "docDialog.conditionTypeText.ROLE": "契約上の役割", "docDialog.conditionTypeText.SIGNER": "署名者アカウント", "docDialog.conditionTypeText.SENDER": "発起人", "docDialog.ifOrEqual": "または | 等しい", "docDialog.codition": "条件", "docDialog.defineRoleTip": "契約書の契約上の役割に基づくか、当該企業の契約署名者・審査者・契約発起者のアカウントに基づくことで契約書のユーザー設定フィルターを操作できます", "docDialog.defineByStatus": "操作状態定義に基づく", "docDialog.back": "戻る", "docDialog.chooseIdentity": "身分の選択", "docDialog.plsSelectTime": "日時を選択してください", "docDialog.noMoreThan365dayTip": "日（365日を超えないこと）", "docDialog.noMoreThan365day": "365日を超えないこと", "docDialog.customize": "ユーザー設定", "docDialog.beforeNumDay": "{num}日前", "docDialog.belowContractOperteFail": "下記の{tip}契約書は操作に失敗しました。", "docDialog.plsInput": "入力してください", "docDialog.goSign": "署名に行く", "docDialog.num": "部", "docDialog.otherNeedSignTip": "その他署名を必要とする契約書", "docDialog.selectedContracts": "{num}件を選択しています", "docDialog.needClaimContract": "署名を受け取る必要のある契約書", "docDialog.batchContainClaimHint": "一括署名の契約書の中には契約書の受け取り待ちが含まれています。これらの契約書がお客様によって署名されるべきかどうか確認してください。", "docDialog.batchDowLoadHint": "選択した契約書は分割ダウンロードする必要があります", "docDialog.labelNotAddTips": "タグ{tag}は下記の契約書に貼り付けできません", "docDialog.batchDownloadTips": "選択した契約書は異なるストレージサーバーに保存してあるため、分割して契約書をダウンロードする必要があります。マルチクラウド上の契約書付属資料は契約書と一緒に一括ダウンロードできません。以下のボタンを順番にクリックし、契約書のダウンロードを完了させてください", "docDialog.batchDownload": "一括ダウンロード", "docDialog.saver": "契約書保存者", "docDialog.ssq": "ベストサイン", "docDialog.cancel": "キャンセル", "docDialog.delete": "削除", "docDialog.deleteTips": "このフォルダーのファイルは一緒に削除できません", "docDialog.deleteConfirm": "このフォルダーを削除しますか", "docDialog.delLabel": "タグの削除", "docDialog.folderPlaceholder": "フォルダー名を入力してください", "docDialog.addFolder": "フォルダーの追加", "docDialog.addLabel": "タグの追加", "docDialog.dividedLabel": "改行で区切られた複数のタブ", "docDialog.myLabels": "マイタグ", "docDialog.label": "{num}のタグ", "docDialog.max10": "最大で10個のタグを追加", "docDialog.length10": "タグは最大10文字", "docDialog.labelExist": "タグはすでに存在しています", "docDialog.selectMember": "構成員の選択", "docDialog.batchBtn.sign": "一括署名", "docDialog.batchBtn.approval": "一括審査", "docDialog.batchBtn.remind": "一括リマインダー", "docDialog.batchBtn.revoke": "一括取り下げ", "docDialog.batchSignTips": "お客様の署名が必要で、内容を記入する必要がない契約書は、一括して署名することができます", "docDialog.supportBatch": "以下の契約書{type}は一括をサポートしています", "docDialog.remindSucc": "リマインド完了", "docDialog.remindFail": "下記{errorSum} 部の契約書の一括リマインドに失敗しました", "docDialog.remind": "注意", "docDialog._confirm": "確定", "docDialog._cancel": "キャンセル", "docDialog.revoke": "取り下げ", "docDialog.exportDetail": "契約書詳細のエクスポート", "docDialog.lessThan365": "開始時間感覚は最長365日を超えられません", "docDialog.narrowRange": "契約数が{maxNum}を超える場合は、期間を短縮してください", "docDialog.lessThan2000": "1回のエクスポートで {maxNum} 個を超えないでください", "docDialog.sendTime": "契約発信日時", "docDialog.signDeadline": "署名期限", "docDialog.timeStartPlaceholder": "開始日時を選択", "docDialog.to": "から", "docDialog.timeEndPlaceholder": "終了日時を選択", "docDialog.signStatus": "契約状態", "docDialog.noFileExport": "エクスポートできるファイルはありません", "docDialog.downloading": "ダウンロード中です、しばらくお待ちください...", "docDialog.move": "移動", "docDialog.folder": "フォルダー", "docDialog.openFuzzyMatch": "現在は完全一致検索で、クリックするとあいまい検索に切り替わります", "docDialog.closeFuzzyMatch": "現在はあいまい検索で、クリックすると完全一致検索に切り替わります（検索の際に使用したキーワードと完全に一致する契約を表示）", "docDialog.archiveRuleDialog.title": "契約書ファイリングルール", "docDialog.archiveRuleDialog.tipContent.0": "『契約書自動ファイリング』契約書に対するフィルター条件をベースにフィルタリングを行い、要件に適合した契約書を自動的にファイリングします。", "docDialog.archiveRuleDialog.tipContent.1": "『手順1』：ルールを新規作成します。条件を選択して、条件の値を入力し、保存します。", "docDialog.archiveRuleDialog.tipContent.2": "『手順2』：目標フォルダーを選択します（契約書一部に付き同時ファイリングは一つのフォルダーに限られます）。", "docDialog.archiveRuleDialog.tipContent.3": "ルールのトリガー：ルールを作成後、新規発行または受領した契約、未ファイリングの契約などのステータスタイプが変更されたことをトリガーに、過去の契約書に対して手動でルールを実行することができます。ファイリングは毎日00: 00に自動で実行されます。", "docDialog.archiveRuleDialog.tipContent.4": "実行順序：リスト順に基づいて実行されます。先に新しいルールを基準に未ファイリングの契約書に対してファイリングが行われます。（ファイリングされた契約書にはルールが適用されなくなります）。ルールに合わない契約書は次のルールが引き続き適用されます。", "docDialog.archiveRuleDialog.create": "ルールの新規作成", "docDialog.archiveRuleDialog.excuTime": "ファイリングは毎日00:00に自動で実行されます", "docDialog.archiveRuleDialog.operation": "操作", "docDialog.archiveRuleDialog.ruleName": "ルール名", "docDialog.archiveRuleDialog.modify": "修正", "docDialog.archiveRuleDialog.enable": "有効", "docDialog.archiveRuleDialog.disable": "無効", "docDialog.archiveRuleDialog.delete": "削除", "docDialog.archiveRuleDialog.cancel": "キャンセル", "docDialog.archiveRuleDialog.confirm": "確認", "docDialog.archiveRuleDialog.saveSuccess": "保存完了", "docDialog.archiveRuleDialog.excuAutoArchive": "過去の契約書に対するファイリング", "docDialog.archiveRuleDialog.excuSuccess": "実行完了", "docDialog.archiveRuleDialog.isRunning": "ルール実行中...", "docDialog.editArchiveRuleDialog.title": "1.ルールの.設定条件", "docDialog.editArchiveRuleDialog.rulePlaceHolderRight": "左側の欄からフィールドを選択してください", "docDialog.editArchiveRuleDialog.rulePlaceHolderLeft": "条件1", "docDialog.editArchiveRuleDialog.next": "次へ", "docDialog.editArchiveRuleDialog.cancel": "キャンセル", "docDialog.editArchiveRuleDialog.back": "戻る", "docDialog.editArchiveRuleDialog.prev": "前へ", "docDialog.editArchiveRuleDialog.save": "保存", "docDialog.editArchiveRuleDialog.selectFloderTitle": "2.ファイリングフォルダの選択", "docDialog.editArchiveRuleDialog.baseOnRcpt": "構成員に基づく", "docDialog.editArchiveRuleDialog.baseOnAttr": "システムフィールドに基づく", "docDialog.editArchiveRuleDialog.baseOntBusinessField": "業務/詳細フィールドに基づく", "docDialog.editArchiveRuleDialog.baseOnTag": "契約タグに基づく", "docDialog.editArchiveRuleDialog.selectFolder": "フォルダーを選択してください", "docDialog.editArchiveRuleDialog.ruleNotEmpty": "条件{index}は空欄に出来ません", "docDialog.editArchiveRuleDialog.multiPlaceHolder": "複数のフィールド値を用いる場合「,（カンマ）」で区切ります", "docDialog.editArchiveRuleDialog.condition": "条件", "docDialog.editArchiveRuleDialog.include": "含む", "docDialog.editArchiveRuleDialog.selectOnTag": "契約タグに基づき選択済み", "docDialog.editArchiveRuleDialog.selectOnField": "{name}フィールドを選択済み", "docDialog.editArchiveRuleDialog.errorMsg": "条件エラーです。修正してください", "docBatch.agree": "同意", "docBatch.reject": "却下", "docBatch.approve": "審査", "docBatch.approving": "審査中...", "docBatch.approved": "審査完了", "docBatch.approveAgree": "審査結果：同意", "docBatch.approveReject": "審査結果：却下", "docBatch.approveSuggest": "審査意見", "docBatch.canNotInput": "任意", "docBatch.confirm1": "確定", "docBatch.cancel1": "キャンセル", "docBatch.inputSignPsw": "契約パスワードを入力してください", "docBatch.inputSixDigitalNum": "6桁の数字を入力してください。", "docBatch.changeEmailVerify": "メール認証に切り替える", "docBatch.changePhoneVerify": "携帯電話認証に切り替える", "docBatch.batchApproveSuccess": "一括審査完了", "docBatch.belowContractApporveFail": "下記 {length}部の契約書の一括審査に失敗しました", "docBatch.tips": "注意", "docBatch.confirm": "確定", "docBatch.signVerify": "契約の検証", "docBatch.signPswLockedTip": "署名パスワードがロックされています。３時間後に自動解除されます。または", "docBatch.findBackPsw": "パスワードの再取得", "docBatch.toUnlockNow": "すぐにロック解除できます", "docBatch.signPswCanEnterTip": "署名パスワードエラーです。もう２/１回入力できます。または", "docBatch.forgetPsw": "パスワードをお忘れの場合", "docBatch.verifyFormatError": "認証コードのフォーマットエラーです", "docBatch.signPassFormatError": "契約パスワードの形式が正しくありません", "docBatch.networkTimeoutTip": "ネットワークタイムアウトしました。ページをリフレッシュして契約書の署名状況を確認してください", "docBatch.batchSignSuccess": "一括署名完了", "docBatch.belowContractSignFail": "下記 {length}部の契約書の一括署名に失敗しました", "docBatch.signWithNameTip": "現在{name}の名義で契約書に署名しています", "docBatch.useElectronicSeal": "電子印章を使用", "docBatch.noWantUseTheSealTip": "この印章を使いたくありません。実名認証後印章を変更できます", "docBatch.toAuth": "実名認証へ", "docBatch.useSignature": "署名を使用", "docBatch.signatureFinishTip": "署名し終わると、自分の署名をより法的に保障するため、実名に行うこともできます", "docBatch.toUseSignatureTip": "左側の「署名箇所」をクリックして署名してください。自分の署名をより法的に保障するため、実名に行うこともできます", "docBatch.batchSign": "一括署名", "docBatch.batchApprove": "一括審査", "docBatch.ssqNotReviewDiffTip": "ベストサインは契約書の現在のバージョンと有効なバージョンとの契約内容の相違を確認しません。一括署名機能を利用することにより、お客様は以下のバージョンの契約書に署名することに同意したことになります。", "docBatch.chooseElectronicSeal": "印章の選択", "docBatch.fileNumTip": "{num}件のファイル", "docBatch.totalFileNumTip": "全{num}件のファイル", "docBatch.chooseDefaultSignature": "デフォルト署名の選択", "docBatch.more": "もっと", "docBatch.sign": "署名", "docBatch.file": "ファイル", "docBatch.signature": "署名", "docBatch.dataErrorTip": "データエラーです。再試行してください。", "docBatch.noCanSignContract": "署名できる契約書はありません", "docBatch.noCanApproveContract": "審査できる契約書はありません", "docBatch.noReceiver": "どのような受取人も追加していません", "docBatch.label": "タグ", "docView.totalPageTip": "第{num}ページ目、計{total}ページ", "docView.numOfPage": "ページ数", "docView.page": "ページ", "docView.canNotCheckContract": "契約書が確認できません", "docView.privateStoreContractTip": "発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは契約ストレージのサーバーに接続することができません", "docExport.exportRecord": "記録のエクスポート", "docExport.refresh": "更新", "docExport.every1000ContractWaitingTime": "契約詳細1000件につき約0.5分の待ち時間", "docExport.fileName": "ファイル名", "docExport.createTime": "生成日時", "docExport.chooseTimePeriod": "日時フィールド", "docExport.to": "から", "docExport.contractStatus": "契約状態を選択", "docExport.operate": "操作", "docExport.download": "ダウンロード", "docExport.expired": "有効期限切れ", "docExport.waitExport": "エクスポート待ち", "batchSearch.add": "一括インポート", "batchSearch.batchAddOption": "一括追加オプション", "batchSearch.batchAddTip": "デフォルトでは行を単位に、1行1項目として、順番に記入してください。", "batchSearch.batchAddPlaceHolder": "内容を入力してください", "batchSearch.name": "一括検索", "batchSearch.viewContract": "契約書の確認", "batchSearch.selectSearchItem": "検索条件の選択", "batchSearch.itemSelectTip": "検索条件を選択してください", "batchSearch.searchContent": "一括入力（検索キーワード毎に一行を占め、最大20行）", "batchSearch.searchContentLineLimitTip": "検索条件で契約番号又は社内番号を選択した場合、最大100行まで可能", "batchSearch.searchContentPlaceholder": "検索ワードを入力してください", "batchSearch.searchConditions": "検索記録：{num}行", "batchSearch.contractsTotal": "契約書数：{count}部", "batchSearch.unfindConditions": "未確認記録：{num}行", "batchSearch.contractNums": "契約数", "batchSearch.order": "No", "batchSearch.searchContentLengthLimitTip": "最多で100件のキーワードに対応しており、それ以上は処理されません", "batchSearch.inputCorrectFieldName": "正確な検索条件を選択してください（プルダウンメニューから検索条件を選択）", "docDetail.crossPlatformSign": "クロスプラットフォーム署名", "docDetail.platformSign.ja": "日本側署名者", "docDetail.platformSign.zh": "中国側署名者", "docDetail.specialContract": "特殊合同", "docDetail.specialContractTip": "跨企业转交的特殊合同，只能查看，不要做接口调用、下载、作废、重新发送、出证，否则可能出现异常", "docDetail.signRole": "（契約上の役割）", "docDetail.signRoleTextOnly": "契約上の役割：", "docDetail.scanCodeSign": "署名の読み取り", "docDetail.other": "その他", "docDetail.shareSignLink": "署名リンクの共有", "docDetail.messageAndFaceVerify": "顔認証＋認証コード署名", "docDetail.faceSign": "顔認証署名", "docDetail.faceFirstVerifyCodeSecond": "顔認証が優先で、予備で認証コードの署名になります", "docDetail.contractRecipient": "契約書受信者", "docDetail.inputReceiver": "受信者", "docDetail.inputReceiverTip": "送信者が入力した担当者名。送信企業（グループ子会社を含む）のみ閲覧可能。", "docDetail.inputReceiverNotInput": "未入力", "docDetail.personalOperateLog": "個人契約書操作記録", "docDetail.recordDialog.date": "日付", "docDetail.recordDialog.user": "ユーザー", "docDetail.recordDialog.operate": "操作", "docDetail.recordDialog.view": "確認", "docDetail.recordDialog.download": "ダウンロード", "docDetail.remarks": "備考", "docDetail.operateRecords": "操作記録", "docDetail.borrowingRecords": "閲覧履歴", "docDetail.currentHolder": "現在の対象者", "docDetail.currentEnterprise": "現在の企業", "docDetail.companyInterOperationLog": "企業内部操作ログ", "docDetail.companyInterOperationLogTip": "企業内部操作ログは、該当企業のみご確認いただけます。", "docDetail.receiverMap.sender": "契約書送信者", "docDetail.receiverMap.signer": "契約書受信者", "docDetail.receiverMap.ccUser": "契約書の副本発送者", "docDetail.receiverMap.editor": "契約書補完者", "docDetail.noRead": "未読", "docDetail.read": "既読", "docDetail.downloadCode": "契約書ダウンロードコード", "docDetail.noTagToAddHint": "タグがありません。企業の管理コンソールで追加してください。", "docDetail.noSender": "契約書の送信者ではないので、ラベルを貼り付けることができません", "docDetail.noOpenFeature": "この契約の送信元企業は、まだタグを設定する機能を開始していません", "docDetail.requireFieldNotAllowEmpty": "必須項目は空欄に出来ません", "docDetail.modifySuccess": "修正に成功しました", "docDetail.uncategorized": "未分類", "docDetail.notAllowModifyContractType": "{type}内の契約書は契約書タイプの修正を許可されていません", "docDetail.setTag": "タグの設定", "docDetail.contractTag": "契約タグ", "docDetail.plsInput": "入力してください", "docDetail.plsInputCompanyInternalNum": "企業内部番号を入力してください", "docDetail.companyInternalNum": "社内管理番号", "docDetail.none": "無", "docDetail.plsSelect": "選択してください", "docDetail.modify": "変更", "docDetail.contractDetailInfo": "契約の詳細情報", "docDetail.contractDetailInfoTip": "契約の詳細情報は、送信企業（グループ子会社を含む）のみ閲覧可能です。", "docDetail.viewDetail": "", "docDetail.slideContentTip.downloadFile": "ダウンロード元ファイル", "docDetail.slideContentTip.look": "確認", "docDetail.slideContentTip.signNotice": "契約注意事項", "docDetail.slideContentTip.contractAncillaryInformation": "契約付属資料", "docDetail.slideContentTip.content": "内容", "docDetail.slideContentTip.document": "ファイル", "docDetail.slideContentTip.supplementsTitle": "{account}の補足契約", "docDetail.slideContentTip.fromCurrentSupplements": "現在のテンプレートからの補足契約：", "docDetail.slideContentTip.fromOtherSupplements": "その他テンプレートからの補足契約：", "docDetail.slideContentTip.supplementsContractId": "契約書番号：", "docDetail.slideContentTip.supplementsContractTitle": "契約表題：", "docDetail.slideContentTip.moreSupplementsLine": "詳細を確認", "docDetail.slideContentTip.updateTime": "更新時間", "docDetail.slideContentTip.updateTimeTip": "契約が署名される前、送信者は署名に関する注意事項を更新できます。", "docDetail.slideContentTip.updatePrivateBtn": "更新", "docDetail.boxCollection": "契約主体資料", "docDetail.downloadDepositConfirmTip.title": "ダウンロードした契約書内容証明ページは匿名化されたものであり、手続き者個人情報は伏せられており、裁判で使用することはできません。訴訟が必要な場合、ベストサインに連絡して完全な契約書内容証明ページを入手することができます。", "docDetail.downloadDepositConfirmTip.hint": "注意", "docDetail.downloadDepositConfirmTip.confrim": "ダウンロードの継続", "docDetail.downloadDepositConfirmTip.cancel": "キャンセル", "docDetail.downloadTip.title": "契約書が未完了のため、未発効の契約書のプレビューファイルをダウンロードしています。", "docDetail.downloadTip.normalVersion": "正常バージョンの表示", "docDetail.downloadTip.highLightVersion": "審査専用バージョン：関連情報をわかりやすくするため、契約書内のすべての「テンプレート内容フィールド」がハイライト化されています。", "docDetail.downloadTip.versionTip": "必要なバージョンを選択してダウンロードしてください：", "docDetail.downloadTip.invalidTitle": "契約書が無効化されたため、未発効の契約書のプレビューファイルをダウンロードしています。", "docDetail.downloadTip.hint": "注意", "docDetail.downloadTip.confirm": "確定", "docDetail.downloadTip.cancel": "キャンセル", "docDetail.transferFail": "転送失敗", "docDetail.transferSuccessGoManagePage": "転送完了、契約書管理ページに戻ります", "docDetail.claimSign": "署名の受領", "docDetail.downloadDepositPageTip": "契約締結証明書（匿名化）のダウンロード", "docDetail.downloadPageTip": "契約締結証明書のダウンロード", "docDetail.resend": "再送", "docDetail.claimSuccess": "受領成功、審査の合格を待ってから署名することができます", "docDetail.approved": "承認済み", "docDetail.proxySign": "代理署名", "docDetail.notPassed": "却下済み", "docDetail.approving": "審査中", "docDetail.signning": "署名待ち", "docDetail.notarized": "正当化済み", "docDetail.invalid": "無効済み", "docDetail.currentFolder": "現在のフォルダー", "docDetail.archive": "ファイリング", "docDetail.moveToSafeBox": "本人専用フォルダに移動", "docDetail.moveToSafeBoxTip": "合同移入保险柜后，只能由合同持有人（合同参与人）才能查看，主管理员也不能查看。", "docDetail.moveFromSafeBox": "移出保险柜", "docDetail.moveFromSafeBoxTip": "移出保险柜后，合同的管理员能统一查看、管理此合同", "docDetail.moveToSuccess": "移入成功", "docDetail.moveOutSuccess": "移出成功", "docDetail.deadlineForSigning": "署名期限", "docDetail.endFinishTime": "署名完了日", "docDetail.contractImportTime": "契約書インポート時間", "docDetail.contractSendTime": "送信日時", "docDetail.back": "戻る", "docDetail.has": "無効化済み", "docDetail.now": "無効化処理中:", "docDetail.for": "無効化中", "docDetail.contractInfo": "契約書情報", "docDetail.basicInfo": "基本情報", "docDetail.contractNum": "番号", "docDetail.downloadVerifyCode": "契約書確認コードのダウンロード", "docDetail.verifyCode": "契約書確認コード", "docDetail.sender": "送信者", "docDetail.personAccount": "個人アカウント", "docDetail.entAccount": "企業アカウント", "docDetail.operator": "担当者", "docDetail.signStartTime": "契約発信日時", "docDetail.signDeadline": "署名期限", "docDetail.contractExpireDate": "契約満了日", "docDetail.viewInvalidContract": "『無効宣言』の確認", "docDetail.labelLimitEditTip": "貴社の管理者が「{name}」フィールドを編集不可に設定しています。このフィールド値を変更するには、「コンソール - 業務フィールド管理 - 契約説明フィールド」ページにアクセスし、「フィールド値の変更を許可する」に調整してください。", "docDetail.edit": "変更", "docDetail.settings": "設定", "docDetail.clear": "クリア", "docDetail.from": "引用元", "docDetail.folder": "フォルダー", "docDetail.contractType": "契約書種類", "docDetail.reason": "理由", "docDetail.sign": "署名", "docDetail.approval": "審査", "docDetail.viewAttach": "添付ページの確認", "docDetail.downloadContract": "契約書のダウンロード", "docDetail.downloadAttach": "契約書内容証明のダウンロード", "docDetail.print": "プリント", "docDetail.certificatedTooltip": "この契約書と関連証拠は杭州インターネット法院（裁判所）司法チェーンで内容証明されています", "docDetail.needMeSign": "私の署名待ちです", "docDetail.needMeApproval": "私の承認が必要です", "docDetail.inApproval": "審査中", "docDetail.needOthersSign": "他の人の署名を必要です", "docDetail.signComplete": "締結済", "docDetail.signOverdue": "署名期限切れ", "docDetail.rejected": "拒否済み", "docDetail.revoked": "却下済み", "docDetail.contractCompleteTime": "契約完了時間", "docDetail.contractEndTime": "契約終了時間", "docDetail.reject": "拒否", "docDetail.revoke": "取り下げ", "docDetail.download": "ダウンロード", "docDetail.viewSignOrders": "署名順序の確認", "docDetail.viewApprovalProcess": "審査プロセスの確認", "docDetail.viewApprovalAnnotate": "查看审批批注", "docDetail.completed": "完了済み", "docDetail.cc": "副本", "docDetail.ccer": "副本送信者", "docDetail.signer": "契約者", "docDetail.signSubject": "契約者", "docDetail.signSubjectTooltip": "発信者の記入した契約主体は", "docDetail.user": "ユーザー", "docDetail.IDNumber": "身分証明書番号", "docDetail.state": "状態", "docDetail.time": "署名日時", "docDetail.notice": "リマインダー", "docDetail.detail": "詳細", "docDetail.RealNameCertificationRequired": "実名認証が必要です", "docDetail.RealNameCertificationNotRequired": "実名認証は必要ありません", "docDetail.MustHandwrittenSignature": "手書き署名が必須です", "docDetail.handWritingRecognition": "手書き筆跡認識", "docDetail.handWritingRecognitionChangeTip1": "1. 契約書の「閲覧」権限、及び該当契約テンプレートの「送信」と「契約者の調整」権限が必要です。", "docDetail.handWritingRecognitionChangeTip2": "2. 現在の契約者が署名を完了していない場合のみ、この設定を変更できます。", "docDetail.privateMessage": "メッセージ", "docDetail.attachment": "資料", "docDetail.rejectReason": "原因", "docDetail.notSigned": "未署名", "docDetail.notViewed": "未確認", "docDetail.viewed": "確認済み", "docDetail.signed": "署名済み", "docDetail.viewedNotSigned": "未署名閲読済み", "docDetail.notApproval": "未審査", "docDetail.remindSucceed": "リマインダーメッセージを発信しました", "docDetail.reviewDetails": "審査詳細", "docDetail.close": "閉じる", "docDetail.entInnerOperateDetail": "企業内部操作詳細", "docDetail.approve": "同意", "docDetail.disapprove": "却下", "docDetail.applySeal": "申請に用いる印章", "docDetail.applied": "申請済み", "docDetail.apply": "申請", "docDetail.toOtherSign": "他の人に転送して署名", "docDetail.handOver": "転送", "docDetail.approvalOpinions": "審査意見", "docDetail.useSeal": "用いる印章", "docDetail.signature": "署名", "docDetail.use": "使用", "docDetail.date": "日付", "docDetail.fill": "記入", "docDetail.times": "回", "docDetail.place": "箇所", "docDetail.contractDetail": "契約書詳細", "docDetail.viewMore": "詳細を確認", "docDetail.collapse": "折りたたむ", "docDetail.WXLinkTitle": "ミニプログラムリンク：", "docDetail.signLinkTitle": "ウェブリンク：", "docDetail.signLink": "署名リンク", "docDetail.saveQRCode": "二次元コードの保存もしくはリンクのコピー、署名者に共有", "docDetail.saveWXCode": "プログラムコードの保存もしくはリンクのコピー、署名者に共有", "docDetail.changeQRCode": "QRコードの切り替え", "docDetail.changeWXCode": "プログラムコードの切り替え", "docDetail.signQRCode": "署名リンク二次元コード", "docDetail.showlinkDec": "{senderName}より「{contractName}」が届いています。下記リンクをクリックして確認してください：{url}", "docDetail.copyUrl": "リンクをコピー", "docDetail.copy": "コピー", "docDetail.copySucc": "コピー完了", "docDetail.copyFail": "コピー失敗", "docDetail.certified": "認証済み", "docDetail.unCertified": "未認証", "docDetail.claimed": "受領済み", "docDetail.unSort": "未分類", "docDetail.signPage": "ページ数：{page}ページ", "docDetail.handWriteNotAllowed": "ベストサイン署名システムを使用", "docDetail.entSign": "署名", "docDetail.stamp": "捺印", "docDetail.stampSign": "捺印及び署名", "docDetail.requireEnterIdentityAssurance": "手続き担当者の身分認証を有効", "docDetail.noNeedToSign": "署名する必要がなくなりました", "docDetail.requestSeal": "業務照合印", "docDetail.requestSealAgree": "印章適合", "docDetail.requestSealRefuse": "印章不適合", "docDetail.requestSealRemark": "印章不適合の備考", "docDetail.viewContentInfo": "契約書内容フィールドの確認", "docDetail.contractContentInfo": "契約書内容フィールド", "docDetail.contentDate": "日付", "docDetail.combobox": "検索候補", "docDetail.text": "テキスト", "docDetail.number": "デジタル", "docDetail.notFilled": "ここは未記入", "docDetail.radio": "単一選択", "docDetail.checkbox": "複数選択", "docDetail.filledBySender": "発信者により記入", "docDetail.filledBy": "{writer}により記入", "docDetail.empty": "結果は空欄", "docDetail.changeToPaperSign": "紙媒体の署名に変更済み", "docDetail.templateNum": "テンプレート番号", "docDetail.QRCode": "契約書確認コード", "docDetail.templateTip": "契約書がテンプレートを使用していない場合、テンプレート番号は初期設定で０になっています", "docDetail.changeContractStatusForPaperSign": "修正状態", "docDetail.changeContractStatus": "期限切れ後の延長", "docDetail.changeContractStatusConfirm.title": "期限切れ後の延長", "docDetail.changeContractStatusConfirm.tip.0": "契約書の署名状態を「署名完了」に修正し、非署名契約者には「紙媒体に切り替え中」と表示します。", "docDetail.changeContractStatusConfirm.tip.1": "契約書の状態変更後元に戻すことはできません。捺印した紙媒体の原紙を受け取った後確定をクリックしてください。", "docDetail.changeContractStatusConfirm.confirm": "確定", "docDetail.changeContractStatusConfirm.cancel": "キャンセル", "docDetail.hasUsePaperSign": "(紙媒体署名の有効化)", "docDetail.signedAging": "署名期間", "docDetail.completeContractInfo": "补全合同信息", "docDetail.day": "日", "docDetail.hour": "時間", "docDetail.minute": "分", "docDetail.accountChangeTip": "ユーザーアカウントが「{previous}」から「{current}」に変更されました", "docDetail.byEncryptionSign": "署名コード方式で署名します", "docDetail.password": "パスワード", "docDetail.byTwoFactorSign": "二要素認証により署名します", "docDetail.completeByRole": "待签约角色{roleName}补全", "docDetail.completedByRole": "（由签约角色{roleName}补全）", "docDetail.completeSuccess": "补全成功", "docDetail.completeInfoFillErrorTip": "信息填写不规范，请检查页面上的报错说明（一般为红色），按提示修改后重试", "docDetail.viewContractOfSameCode": "按照\"公司内部编号\"快速查找", "docDetail.contractTitle": "合同标题：", "docDetail.contractId": "合同编号：", "docDetail.privateTip": "契約者と送信者企業のみ見ることができます", "shortcut.all": "すべての契約書書", "shortcut.sign": "私の署名待ちです", "shortcut.approve": "私の承認が必要です", "chooseEntFolder.title": "企業契約書管理システムの選択", "chooseEntFolder.desc": "アクセスする企業契約書管理システムを選択してください：", "syncEntFolder.title": "契約フォルダーと同期する", "syncEntFolder.createFolder": "フォルダーの新規作成", "exportContract.title": "エクスポート範囲", "exportContract.content1": "エクスポートする範囲は現在検索結果にある全ての契約書の明細です（明細フィールドと契約書リストのヘッダーは同じです）。", "exportContract.content2": "エクスポート結果に複数文書の契約の個々の補助契約を含める必要がありますか？", "exportContract.radioText.0": "はい", "exportContract.radioText.1": "いいえ", "exportContract.cancel": "キャンセル", "exportContract.export": "エクスポートを開始します", "scanCodeRemind.tip": "注意", "scanCodeRemind.confirm": "わかりました。", "scanCodeRemind.content": "この契約書には手書き署名を読み取った署名者が含まれています。契約書の詳細ページで契約書確認コードをダウンロ－ドし、ご自身で送信して通知してください。", "scanCodeRemind.detailContent": "この契約主体は手書き署名を読み取った署名者が含まれています。契約書の詳細ページで契約書確認コードをダウンロ－ドし、ご自身で送信して通知してください。", "crossplatformList.notice": "越境契約書が{number}件あります。確認するにはここをクリックしてください。", "crossplatformList.breadcrumb": "越境契約書", "crossplatformList.viewDetail": "詳細の確認", "consts.shortcutMap.myFolders": "マイ契約フォルダー", "consts.shortcutMap.allFolders": "全ての契約フォルダー", "consts.contractStatus.all": "全ての状態", "consts.contractStatus.needMeSign": "署名待ち ", "consts.contractStatus.needMeApproval": "私の審査待ち", "consts.contractStatus.inApproval": "審査中", "consts.contractStatus.needOthersSign": "他の人の署名を必要です", "consts.contractStatus.signComplete": "締結済", "consts.contractStatus.signOverdue": "署名期限切れ", "consts.contractStatus.rejected": "拒否済み", "consts.contractStatus.revoked": "却下済み", "consts.contractStatus.invalid": "無効済み", "consts.rejectReasonList.signOperateReason": "署名操作/検証操作に対して疑問をお持ちであれば、更に交流を進める必要があります", "consts.rejectReasonList.termReason": "契約条件/内容に異議があれば、更に交流を進める必要があります", "consts.rejectReasonList.explainReason": "契約内容に対してわからないことがあれば、事前に告知してください", "consts.rejectReasonList.otherReason": "その他（理由を記載してください）", "consts.templatePermissionMap.sendContract": "契約書を送信", "consts.templatePermissionMap.modifyDocument": "ファイルの調整", "consts.templatePermissionMap.modifyReceiver": "契約者の調整", "consts.templatePermissionMap.addCCReceiver": "ccを追加します", "consts.templatePermissionMap.modifySignRequirement": "署名要件の調整", "consts.templatePermissionMap.dragSignLabel": "モバイル署名フィールド（署名位置を含む）", "consts.templatePermissionMap.modifySignLabel": "削除署名フィールドの新規作成/削除（署名位置を含む）", "consts.templatePermissionMap.editable": "テンプレートの編集", "consts.templatePermissionMap.templateDuplicate": "テンプレートのコピー", "consts.templatePermissionMap.modifyDocumentFederation": "ファイル組み合わせ設定", "consts.templatePermissionMap.invalidStatement": "無効に設定", "consts.templatePermissionMap.grantManage": "権限設定", "consts.templatePermissionMap.editCustomScene": "場面設定", "consts.templatePermissionMap.setDefaultValue": "デフォルト値として設定", "consts.templatePermissionMap.templateSpecialSeal": "専用印章のテンプレート", "consts.templatePermissionMap.editSupplyAgree": "補足契約", "consts.templatePermissionMap.contractConfidentiality": "契約の秘密保持", "consts.templatePermissionMap.stampRecommend": "AIエージェント設定", "consts.templatePermissionDesc.useTmp.name": "テンプレートの使用権限", "consts.templatePermissionDesc.useTmp.tabName": "テンプレートの使用", "consts.templatePermissionDesc.useTmp.sendContract": "現在のテンプレートを使用許可してから契約書を発信できます。この権限のもとでその他のテンプレート権限を与えることができます", "consts.templatePermissionDesc.useTmp.modifyDocument": "テンプレートを使用して契約書を発信する際、アップロードして契約書ファイルや契約書付属資料などを許可します。また使用する中で許可すると、臨時でテンプレートの中にあるアップロード済みのファイルを削除することができます", "consts.templatePermissionDesc.useTmp.modifyReceiver": "テンプレートを使用して契約書を発信する際、契約者のアカウント、名称の修正を許可し、契約者の追加もしくは削除することができます（「署名フィールドの追加」権限と一緒に使用する設定が必要です）", "consts.templatePermissionDesc.useTmp.modifySignRequirement": "テンプレートを使用して契約書を発信する際、使用時の署名要件の修正許可をします", "consts.templatePermissionDesc.useTmp.dragSignLabel": "テンプレートを使用して契約書を発信する際、設定済みの捺印箇所・署名箇所・業務フィールドの位置の調整を許可します", "consts.templatePermissionDesc.useTmp.modifySignLabel": "テンプレートを使用して契約書を発信する際、署名者の捺印箇所・署名箇所の追加を許可します", "consts.templatePermissionDesc.useTmp.setDefaultValue": "契約書を発信する際、署名者にフィールドの初期値を書き入れることができます", "consts.templatePermissionDesc.useTmp.addCCReceiver": "テンプレートを使用して契約書を送信する時、共有先のアカウントのみ追加できます。", "consts.templatePermissionDesc.manageTmp.name": "テンプレートの管理権限", "consts.templatePermissionDesc.manageTmp.tabName": "テンプレート管理", "consts.templatePermissionDesc.manageTmp.editable": "契約書テンプレート一覧ページで「編集」ボタンをクリックしてテンプレートを編集することができます。また、テンプレートの削除、有効化/無効化、ダウンロードにも対応しています。", "consts.templatePermissionDesc.manageTmp.templateDuplicate": "契約書テンプレートリストページで「コピー」ボタンをクリックしてテンプレートをコピーする許可をします。動態テンプレートには対応していません", "consts.templatePermissionDesc.manageTmp.modifyDocumentFederation": "テンプレート書類リストページで書類組み合わせの追加・削除・修正を許可します", "consts.templatePermissionDesc.manageTmp.modifySceneConfig": "テンプレート詳細ページのシーン設定項目のレイアウトを許可します", "consts.templatePermissionDesc.manageTmp.invalidStatement": "どのようにして署名の完了したテンプレート契約書を排除するのか", "consts.templatePermissionDesc.manageTmp.specialSealConfig": "テンプレート契約書の署名者に印章図案を指定する", "consts.templatePermissionDesc.manageTmp.editSupplyAgree": "現在のテンプレートの関連書類を設定し、現在のテンプレートの契約書の補足契約として関連するテンプレートを同じ署名者の契約書に発信します", "consts.templatePermissionDesc.manageTmp.contractConfidentiality": "当グループ/企業の契約関係者（送付、署名捺印、審査、補完）を設定し、自動的にある固定アカウントへ転送することができます", "consts.templatePermissionDesc.manageTmp.stampRecommend": "AIエージェントの有効化を許可します。電子署名プロセスの特定の段階でAIエージェントが手動操作を代替できます。", "consts.templatePermissionDesc.manageGrant.name": "管理権限の権限", "consts.templatePermissionDesc.manageGrant.tabName": "管理権限", "consts.templatePermissionDesc.manageGrant.grantManage": "契約書テンプレートリストページで「授権構成員」もしくは「授権の役割」をクリックしてテンプレートを他の人に授権することを許可します", "consts.contractAlias.doc": "ファイル", "consts.contractAlias.letter": "問い合わせ書簡", "consts.contractAlias.proof": "授権書", "consts.contractAlias.contract": "契約書", "consts.contractAlias.agreement": "同意書", "consts.contractAlias.service_report": "作業指示書", "consts.contractStatusCascader.inApproval": "審査中", "consts.contractStatusCascader.inSigning": "署名中", "consts.contractStatusCascader.isCompleted": "完了済み", "consts.contractStatusCascader.isCancelled": "キャンセル済み", "consts.contractStatusCascader.revokeCancel": "契約は取り下げられました", "consts.contractStatusCascader.overDue": "署名期限切れ", "consts.contractStatusCascader.sendApprovalNotPassed": "審査却下", "consts.contractStatusCascader.reject": "署名拒否済み", "consts.contractStatusCascader.invalid": "無効済み", "consts.entFolderPermissionMap.contractBorrow": "契約書の借覧", "consts.entFolderPermissionMap.borrowApproval": "借覧審査者の修正", "consts.entFolderPermissionMap.viewList": "明細リストの確認", "consts.entFolderPermissionMap.downloadList": "明細リストのエクスポート", "consts.entFolderPermissionMap.viewContract": "契約詳細ページの確認", "consts.entFolderPermissionMap.downloadContract": "契約書のダウンロード", "consts.entFolderPermissionMap.invalidContract": "無効契約書", "consts.entFolderPermissionMap.resendContract": "契約書の再送", "consts.entFolderPermissionMap.setTag": "タグの設定", "consts.entFolderPermissionMap.distriButtonPermission": "権限設定の引き継ぎ", "consts.entFolderPermissionMap.syncHronize": "個人フォルダーの同期", "consts.entFolderPermissionMap.remove": "契約書の削除", "consts.entFolderPermissionMap.group": "契約書グループ", "consts.entFolderPermissionMap.edit": "フォルダーの修正", "consts.crossFormHeader.contractTitle": "契約タイトル", "consts.crossFormHeader.sender": "送信者", "consts.crossFormHeader.receiver": "受信者", "consts.crossFormHeader.contractStatus": "契約状態", "consts.crossFormHeader.contractId": "契約番号", "consts.crossFormHeader.sendTime": "送信日付", "consts.crossFormHeader.expireTime": "契約満了日", "consts.crossFormHeader.signDeadlineTime": "署名の締切日", "consts.crossFormHeader.finishTime": "署名完了時間", "hybridBusiness.isCheckingNet": "マルチクラウドのネット環境を検査中", "mixin.createSuccessful": "新規作成完了", "mixin.setLabel": "タグの設定", "recoverSpecialSeal.title": "印章が使用できません", "recoverSpecialSeal.description1": "送信者は契約時にこの印章を使用して契約書に署名するよう要求していますが、御社はこの印章をすでに削除しています。確実に署名が行えるよう、管理者にこの印章の復元を要求してください", "recoverSpecialSeal.description2": "もし、この印章が本当に今後の使用に適さない場合、送信者に連絡して印章のデザインの修正を要求した後、再度契約書に署名するようにしてください。", "recoverSpecialSeal.postRecover": "印章の復元申請", "recoverSpecialSeal.note": "タップ後管理者が印章復元申請のSMS/メールを受け取ると同時に、同時に印章の管理ページで申請を見ることができます。", "recoverSpecialSeal.requestSend": "復元申請の提出完了", "certificationRenewalDialog.renewalTitle": "デジタル証書期限延長", "certificationRenewalDialog.renewalTip": "お客様のデジタル証書は期限切れとなりましたので、契約の署名手続を正常に行うために、直ちに期限延長を行ってください", "certificationRenewalDialog.previousIdentity": "証書の所有者：", "certificationRenewalDialog.previousCA": "原証書の発行機構：", "certificationRenewalDialog.previousExpiryDate": "原証書の有効期限：", "certificationRenewalDialog.previousId": "原証書のシリアルコード：", "certificationRenewalDialog.renewal": "期限延長に同意する", "pdf.previewFail": "書類プレビューエラーです", "pdf.pager": "{x}ページ目　合計{y}ページ", "pdf.parseFailed": "PDFファイルの解析に失敗しました。「確定」をクリックして再試行してください", "pdf.confirm": "確定", "tagManage.title": "タグの設定", "importOffLineDoc.importDoc": "契約書のインポート", "importOffLineDoc.step0Title": "手順1：企業名のインポート確認", "importOffLineDoc.step1Title": "手順2：Excelのアップロード", "importOffLineDoc.step2Title": "手順3：契約書ファイルのアップロード", "importOffLineDoc.step1Info": "先にExcelテンプレートをダウンロードし、入力完了後再度インポートします。契約書数量は1000を超えないでください。", "importOffLineDoc.next": "次へ", "importOffLineDoc.entName": "企業名", "importOffLineDoc.archiveFolder": "ファイリングフォルダ", "importOffLineDoc.downloadExcel": "Excelのダウンロード", "importOffLineDoc.uploadExcel": "Excelのアップロード", "importOffLineDoc.reUploadExcel": "再アップロード", "importOffLineDoc.step2Info.0": "1. 契約書書類はPDFもしくは画像フォーマットに限ります。；", "importOffLineDoc.step2Info.1": "2. すべての契約書書書類は一つのフォルダーに置いた後、フォルダーごとZIP形式で圧縮します(１５０Mを超えないこと）；", "importOffLineDoc.step2Info.2": "3. ファイル名には拡張子（例：.pdf）が含まれ、第2ステップでExcelに記入したファイル名と一致させる必要があります。；", "importOffLineDoc.uploadZip": "ZIPファイルのアップロードをクリック", "importOffLineDoc.reUploadZip": "ZIPファイルを再アップロード", "importOffLineDoc.done": "確定", "importOffLineDoc.back": "戻る", "importOffLineDoc.contractTitle": "契約書名", "importOffLineDoc.singerAccount": "署名者アカウント", "importOffLineDoc.singerName": "署名者名", "importOffLineDoc.uploadSucTip": "アップロードに成功しました。「確定」をクリックしてインポートを始めてください", "importOffLineDoc.outbox": "メールボックス", "importOffLineDoc.fileLessThan": "{num}M以下のファイルをアップロードしてください", "importOffLineDoc.fileTypeValid": "{type}フォーマットの文書のみアップロードできます。", "download.contactGetDownloadCodeTip": "契約発起者に連絡してダウンロードコードを入手するか、本企業業務システムにログインしてダウンロードを試してみてください。", "download.downloadCode": "ダウンロードコード", "download.hint": "注意", "download.download": "ダウンロード", "download.plsInput": "入力してください", "download.plsInputDownloadCode": "ダウンロードコードを入力してください", "download.downloadCodeError": "ダウンロードコードが間違っています", "download.noAuthDownload": "個人実名認証を完了するか実名情報の一致を確認してからでないと契約書をダウンロードできません。", "download.noAuthView": "個人実名認証を完了するか実名情報の一致を確認してからでないと契約書の詳細情報を確認できません。", "download.allFiles": "全ファイル", "download.cancel": "キャンセル", "download.plsSelectFiles": "先にファイルを選択してください", "download.publicCloudDownloadTip": "ダウンロードする契約書の中には署名者がアップロードしたその他の資料が含まれています。契約書と一緒にダウンロードしますか？", "download.hybridCloudDownloadTip": "ダウンロードする契約書の中には署名者がアップロードしたその他の資料が含まれています。", "download.sameTimeDownloadAttachTip": "契約付属資料と一緒にダウンロード", "download.downloadContract": "契約書のダウンロード", "download.downloadAttach": "契約付属資料のダウンロード", "transfer.list1": "リスト1", "transfer.list2": "リスト2", "transfer.maxSelectNum": "フィールド数が上限の{maxLength}を超えています。{balance}つのフィールドを削除してください", "poperCascader.plsSelect": "選択してください", "poperCascader.person": "人", "poperCascader.selectNumTip": "選択済{A}/{B}個{C}", "poperCascader.allSelect": "すべて選択", "approvalDetail.submitter": "提出者", "approvalDetail.signatory": "署名者", "approvalDetail.reviewSchedule": "審査進度", "approvalDetail.downloadFile": "ダウンロード元ファイル", "approvalDetail.content": "内容", "approvalDetail.document": "ファイル", "approvalDialog.stepTip.0": "契約書の審査フローを選択してください", "approvalDialog.stepTip.1": "審査に合格後、契約書を自動で送信します", "approvalDialog.stepTip.2": "審査に合格後、契約書を自動で受信します", "approvalDialog.back": "戻る", "approvalDialog.next.0": "次へ", "approvalDialog.next.1": "審査提出", "approvalDialog.ourApprovalBeforeSign": "当方で契約書前の審査フローに署名します", "approvalDialog.chooseApprover": "審査者の選択：", "approvalDialog.completeApprovalFlow": "お客様の提出した審査フローに不備があります。追加してから再度提出してください。", "approvalDialog.completeTargetEntApprovalFlow": "ご提出いただいた{entName}の承認プロセスは不完全です。完了してから再提出してください。", "approvalDialog.viewPrivateLetter": "メッセージの確認", "approvalDialog.addPrivateLetter": "メッセージの追加", "approvalDialog.useEntFlow": "{name}の審査フローを使用", "approvalDialog.beforeSendApprove": "発信前審査", "approvalDialog.beforeSignApprove": "署名前審査", "approvalDialog.beforeSignApproveTip": "署名者に本企業/本グループの子会社が含まれていない場合は、署名前の承認フローが起動しません。", "approvalDialog.contractTypeFlow": "契約書種類 {contractTypeName} の承認フローを設定します。", "approvalDialog.errorTip": "最初に承認フローを選択してください", "approvalDialog.useGroupApproval": "グループ会社各社は本社の承認プロセスを使用します。", "approvalDialog.setApprovalIndividual": "グループ会社各社は各自の承認プロセスを設定できます。", "approvalDialog.errorTipWithEntName": "{entName}の承認フローを選択してください", "approvalDialog.noWorkflow": "この企業が送信する契約は承認の必要がありません。", "cancelContract.confirm": "確認", "cancelContract.selectRejectReason": "署名拒否理由を選択してください", "cancelContract.reasonWriteTip": "拒否理由を記入してください", "cancelContract.refuseReasonOther": "拒否理由の詳細(オプション) | 拒否理由の詳細(必須)", "cancelContract.inputRejectReason": "拒否理由を記入することで相手方にお客様の問題を理解する手助けになり、契約手続きを早くします", "cancelContract.reject": "拒否", "cancelContract.pwdWrong": "署名パスワードエラーです。もう２/１回入力できます。または", "cancelContract.forgetPwd": "パスワードをお忘れの場合", "cancelContract.pwdLocked": " 署名パスワードがロックされています。３時間後に自動解除されます。または", "cancelContract.retrievePwd": "パスワードの再取得", "cancelContract.unlock": "すぐにロック解除できます", "cancelContract.inputReason": "原因{type}を入力してください（入力しなくともよい）", "cancelContract.revokeTips": "撤回後契約受信先は契約内容を確認できますが、撤回理由は確認できます。| 受信先が契約書を受け取っていない場合（契約書が発送前審査に合格していない場合もしくは署名に順序があり署名する番になっていない場合）完全にこの契約書を見ることはできません。", "cancelContract.revokeReasons": "契約内容に修正が必要です | 受信者アカウントに修正が必要です | 署名者はこの契約を受信する必要がありません", "cancelContract.revokeOtherReason": "その他", "cancelContract.rejectTips": "署名拒否後この契約に署名できません", "cancelContract.signPwd": "署名パスワード", "cancelContract.inputPwd6": "6桁の契約パスワードを入力してください", "cancelContract.inputPwd": "契約パスワードを入力してください", "cancelContract.inputNumber6": "6桁の数字を入力してください。", "cancelContract.mail": "メールアドレス", "cancelContract.phone": "携帯電話", "cancelContract.verify": "認証コード", "cancelContract.mailVerify": "認証コード", "cancelContract.otherNotice.1": "SMSを受け取っていません。試す", "cancelContract.otherNotice.2": "音声認証コード", "cancelContract.otherNotice.3": "または", "cancelContract.otherNotice.4": "SMS認証コード", "cancelContract.otherNotice.5": "電子メール認証コード", "cancelContract.sendSucc": "送信完了", "cancelContract.sendInternalErr": "送信時間間隔が短すぎます", "cancelContract.getVerifyCode": "先に認証コードを取得してください", "cancelContract.succ": "{type}完了", "cancelContract.refuseConfirmTip": "あなたは「{reason}」という理由でこの契約書への署名を拒否します。続行したら、この契約書に署名できなくなります。続行しますか？", "cancelContract.waitAndThink": "キャンセル", "contractMove.newFolder": "フォルダーの新規作成", "contractMove.plsInput": "入力してください", "contractMove.archive": "ファイリング", "contractMove.noFolder": "フォルダーがありません", "contractMove.confirm": "確認", "contractMove.cancel": "キャンセル", "contractMove.folderPlaceholder": "フォルダー名を入力してください", "contractMove.fileSucc": "ファイリング完了", "contractMove.noFunctionLimit": "御社はこの機能を開通していません。カスタマーサービスに連絡して開通することができます。", "contractMove.hasMoveTip": "ファイリング済の契約書を再度ファイリングすると、契約書は元のフォルダから移動します。", "contractMove.howToViewMovedStatus": "契約書がファイリング済かどうかを確認するには", "contractsTransferDialog.originOwner": "元対象者", "contractsTransferDialog.contractTransfer": "契約書の引き継ぎ", "contractsTransferDialog.newOwner": "新対象者", "contractsTransferDialog.selectedOwner": "引き継ぎ関係の選択済み", "contractsTransferDialog.searchTip": "アカウント/氏名検索を入力をサポート", "contractsTransferDialog.confirm": "確定", "contractsTransferDialog.chooseNewOwner": "新対象者を選択してください", "contractsTransferDialog.transferRelationship": "少なくとも引き継ぎ関係者1名を追加", "contractsTransferDialog.notCliam": "契約書は受領されています", "contractsTransferDialog.bizTransfer": "事業部門をまたいで転送", "contractsTransferDialog.innerTransfer": "事業部門内で転送", "contractsTransferDialog.chooseMultiLine": "業務ラインを選択してください", "contractsTransferDialog.chooseAccount": "アカウントを選択します", "contractsTransferDialog.displayAccount": "上位50のアカウントのみ表示", "contractsTransferDialog.transferRoles": "以下のロールの契約を転送した場合：", "contractsTransferDialog.tip": "注意", "contractsTransferDialog.continue": "続行", "contractsTransferDialog.know": "わかりました", "contractsTransferDialog.cancel": "キャンセル", "linkContract.reason.reSend": "再発信した契約書", "linkContract.reason.beReSend": "再発信された契約書", "linkContract.reason.invalid": "無効宣言の契約書", "linkContract.reason.beInvalid": "無効にされた契約書", "linkContract.reason.paperSign": "紙媒体スキャンに対応した契約書原本", "linkContract.reason.bePaperSign": "紙媒体スキャンデータ", "linkContract.reason.manualLinked": "手動関連付け", "linkContract.contractTitle": "契約書名", "linkContract.contractNo": "契約番号", "linkContract.linkedDetail": "関連詳細", "linkContract.noPermissionTip": "お客様にはこの関連契約を確認する権限がありません", "linkContract.noCanLinked": "手動関連付けした契約書を取り消すことだけができます", "linkContract.title": "契約書の関連付け", "linkContract.connectMore": "関連付け", "linkContract.placeholder": "主契約書番号を入力してください", "linkContract.contractNoLengthTip": "契約番号は19桁の数字である必要があります", "linkContract.revoke": "契約は取り下げられました", "linkContract.overdue": "署名期限切れ", "linkContract.approvalNotPassed": "審査却下", "linkContract.reject": "署名拒否済み", "linkContract.invalid": "契約破棄済み", "linkContract.signing": "署名中", "linkContract.complete": "完了済み", "linkContract.approvaling": "審査中", "linkContract.disconnect": "関連付けの解除", "linkContract.connectSuccess": "関連付けの完了", "linkContract.connectExist": "関連付けはすでに存在しています", "linkContract.disconnectSuccess": "関連付けの解除完了", "linkContract.connectLimit": "関連付け契約書数の上限は100部です", "linkContract.noFunctionLimit": "御社はこの機能を開通していません。カスタマーサービスに連絡して開通することができます。", "linkContract.disconnectTip.0": "契約書の関連付けを解除します", "linkContract.disconnectTip.1": "か？", "linkContract.submit": "確定", "linkContract.cancel": "キャンセル", "linkContract.entAccount": "企業アカウント", "linkContract.personAccount": "個人アカウント", "linkContract.whetherMasterContract": "主契約ですか", "qrCodeTab.pleaseScanToHandleWrite": "Wechatもしくは携帯電話のブラウザーで読み取り、モバイルデバイス上で手書きサインをしてください", "qrCodeTab.save": "保存", "selectSignType.chooseSignType": "契約方法を選択", "selectSignType.useTemplate": "テンプレートの使用", "selectSignType.useLocalFile": "ローカルファイルのアップロード", "signValidation.VerCodeVerify": "認証コードによる検証", "signValidation.QrCodeVerify": "二次元コード検証", "signValidation.verifyTip": "ベストサインでは現在お客様の安全なデジタル証書をコールしています。契約環境は安全ですので安心して署名してください。", "signValidation.verifyAllTip": "ベストサインでは企業デジタル証書とお客様の個人デジタル証書をコールしています。契約環境は安全ですので安心して署名してください。", "signValidation.appScanVerify": "ベストサインアプリの二次元コード検証", "signValidation.downloadBSApp": "ベストサインアプリをダウンロード", "signValidation.scanned": "二次元コード成功", "signValidation.confirmInBSApp": "ベストサインアプリの中で署名を確認してください", "signValidation.qrCodeExpired": "二次元コードが失効しています。リロードしてください", "signValidation.appKey": "アプリセキュリティ検証", "signValidation.goToScan": "読み取る", "signValidation.signSuc": "署名成功", "signValidation.if": "どうか", "signValidation.forgetPassword": "パスワードをお忘れの場合", "signValidation.signPsw": "署名パスワード", "signValidation.signPwdType": "请输入6位数字", "signValidation.email": "メールアドレス", "signValidation.phoneNumber": "携帯電話", "signValidation.setNotificationInUserCenter": "ユーザーセンターで通知方法を設定してください", "signValidation.mailVerificationCode": "認証コード", "signValidation.verificationCode": "認証コード", "signValidation.msgTip": "SMSを受け取っていません。試す", "signValidation.voiceVerCode": "音声認証コード", "signValidation.or": "または", "signValidation.SMSVerCode": "SMS認証コード", "signValidation.emailVerCode": "電子メール認証コード", "signValidation.doNotWantUseVerCode": "認証コードを使いたくありません", "signValidation.try": "試す", "signValidation.goToFaceVerify": "顔認証に入る", "signValidation.submit": "確定", "signValidation.SentSuccessfully": "送信完了", "signValidation.intervalTip": "送信時間間隔が短すぎます", "signValidation.signVerification": "署名", "signValidation.appliedSeal": "印刷にて申請書を提出しています", "signValidation.operationCompleted": "操作完了", "signValidation.firstStepTip1": "ダイイチホウ：顔認証署名", "signValidation.firstStepTip2": "（顔認証署名の要求がある契約書に使用）", "signValidation.secondStepTip1": "ニホウ：認証コード署名", "signValidation.secondStepTip2": "（認証コード署名の要求がある契約書に使用）", "signValidation.useVerCode": "認証コードで認証", "signValidation.useSignPsw": "契約パスワードで認証", "signValidation.setSignPsw": "契約パスワード検証の設定", "signValidation.inputVerifyCodeTip": "認証コードを入力してください", "signValidation.tip": "設定完了後、デフォルトで優先的に署名パスワードを使用します。修正の必要がある場合、上上簽電子署名プラットフォームにログインし「ユーザーセンター」で、または上上簽ミニプログラムにログインし「アカウント管理」で、設定の調整を行うことができます。", "signValidation.saveAndReturnSign": "保存して署名に戻る", "signValidation.signPwdRemind": "常に署名している、認証コードが待ちすぎている、契約パスワードを試してみては？", "signValidation.toSetSignPwd": "契約の暗証番号を設定して", "switchSubject.chooseIdentity": "身分の選択", "switchSubject.chooseMultiLine": "業務ラインを選択してください", "switchSubject.confirm": "確認", "switchSubject.cancel": "キャンセル", "switchSubject.switchIdentity": "操作テンプレートを企業用に切り替える", "switchSubject.switchIdentityTips": "以下のアカウントにはテンプレートの操作権限があります。切り替えを選択してください：", "unverifyConfirmDialog.tip": "注意", "unverifyConfirmDialog.isGroupProxyAuth": "本企業は現在の認証状態が集団の代理認証であり、契約書の受取人はお客様の身分を識別できません。まず実名認証資料を追加してください。", "unverifyConfirmDialog.unverify": "実名認証を行っていないため、契約書受信者の身分を識別できません。実名認証をした後署名するようにしてください", "unverifyConfirmDialog.goAuthenticate": "認証へ", "unverifyConfirmDialog.enterprise": "メーカー", "unverifyConfirmDialog.theEnterprise": "企業", "unverifyConfirmDialog.you": "お客様", "contractQrCodeDialog.selectVerifyCode": "チェックコードの選択", "contractQrCodeDialog.viewVerifyCode": "パターンの確認", "contractQrCodeDialog.preview": "プレビュー", "contractQrCodeDialog.msg.selectVerifyCode": "チェックコードを選択してください", "contractQrCodeDialog.msg.success": "変更完了", "addAttachmentConfig.dialogTitle": "付属資料フィールドの設定", "addAttachmentConfig.fieldInd": "No.", "addAttachmentConfig.fieldName": "フィールド名", "addAttachmentConfig.fieldType": "フィールド内容", "addAttachmentConfig.fieldRequire": "必須項目かどうか", "addAttachmentConfig.lengthLimit": "20字以下とします", "addAttachmentConfig.type": "PDF、Word、Excel及び画像フォーマット", "addAttachmentConfig.notRequire": "オプション", "addAttachmentConfig.addField": "フィールドの追加", "addAttachmentConfig.submit": "保存", "addAttachmentConfig.cancel": "キャンセル", "addAttachmentConfig.requireName": "名称が未記入です", "addAttachmentConfig.saveSucc": "保存完了", "authInfoChange.title": "実名情報変更の検知", "authInfoChange.confirm": "确认", "authInfoChange.changeAuth": "実名更新", "authInfoChange.notifyAdmin": "管理者に通知", "authInfoChange.notifySuccess": "通知完了", "authInfoChange.operateSuccess": "処理完了", "authInfoChange.warningTip.tip1": "審査の結果、貴社“{entName}”の上上簽での企業実名情報{oldAuthInfo}と最新の工商届出情報{newAuthInfo}が一致しません。", "authInfoChange.warningTip.tip2": "お客様が締結する電子契約の法規準拠性と効力を保証するため、最新の企業情報を使用して再度実名認証を行ってください。", "authInfoChange.warningTip.tip3": "この操作を行っても、お客様の現在の企業情報は却下されません。", "authInfoChange.suggestTip.tip1": "お客様の企業がグループ組織ではない場合、専属CSMへご連絡いただくか、ベストサインカスタマーサービスホットライン400-993-6665へおかけいただけますと、実名認証情報の更新をお手伝いします。更新後に、引き続き締結を行うことができます。", "authInfoChange.suggestTip.tip2": "【管理員{adminInfo}に通知する】", "authInfoChange.suggestTip.tip3": "をクリックし、ただちに管理員へ通知を送信して、管理員に再度実名認証を行うよう促すことができます。オフラインで通知し、業務の展開を速やかに推進することもできます。", "excelScale.setExcelScale": "EXCELファイルの縮小設定", "excelScale.info": "EXCELファイルのすべての内容が１ページに表示されるように縮小されます。（このテンプレートを使用する際にアップロードされるすべての Excel ファイルに適用されます）", "excelScale.on": "EXCELファイル縮小設定", "excelScale.line.tip1": "この機能を有効にするとテンプレートにアップロードされるファイルや添付資料に適用されます", "excelScale.line.tip2": "テンプレートを使用する際、テンプレートの設定を受け継ぎます", "excelScale.saveSucc": "保存完了", "excelScale.saveFail": "保存エラー", "internalSignConfig.configTitle.0": "内部文書への署名のみに使用設定", "internalSignConfig.configTitle.1": "内部文書への署名のみに使用設定", "internalSignConfig.title": "内部文書への署名のみに使用設定", "internalSignConfig.tip.0": "この機能は正式な契約書（労働契約など）には適用されません。この機能を有効にすると、テンプレートの使用は以下のように制限されます：", "internalSignConfig.tip.1": "• 署名者として企業内のメンバーアカウントしか追加できません", "internalSignConfig.tip.2": "• 署名者が使用できるのは企業署名の方法のみです", "internalSignConfig.checkboxInfo.0": "署名者は実名でない署名が容認されます（法的効力において実名署名に及びません）。チェックが入っていない場合、実名署名が必要です", "internalSignConfig.checkboxInfo.1": "署名者が契約書に署名する際に検証を行わずに署名することを許可する。チェックしていない場合は、認証コード（または契約パスワード、顔認証など）で検証してから署名することになります。", "internalSignConfig.checkboxInfo.2": "署名者が契約書に署名する時、手書き署名でないことが容認されます（デフォルトの署名を使用できます）。チェックが入っていない場合は、手書き署名が必要です", "internalSignConfig.setAliasTip": "外部向けの「契約書別名」を設定", "internalSignConfig.setAliasInfo": "テンプレート詳細ページ→場面設定で、外部向けの「契約書別名」を設定できます", "permissionHeader.hi": "こんにちわ", "permissionHeader.exit": "終了", "permissionHeader.help": "ヘルプ", "permissionHeader.hotline": "サービスホットライン", "paperSign.title": "紙媒体の署名を使用", "paperSign.stepText.0": "次へ", "paperSign.stepText.1": "紙媒体署名確認", "paperSign.stepText.2": "確定", "paperSign.needUploadFile": "先にスキャンファイルをアップロードしてください", "paperSign.uploadError": "アップロードエラーです", "paperSign.cancel": "キャンセル", "paperSign.downloadPaperFile": "紙媒体署名ファイルの取得", "paperSign.step0.title": "先に契約書をダウンロード印刷し、物理的な捺印をした上で、送信者へ郵送するひつようがあります", "paperSign.step0.address": "郵送住所：", "paperSign.step0.contactName": "受取者氏名：", "paperSign.step0.contactPhone": "受取者連絡方法：", "paperSign.step0.defaultValue": "オフライン方法で送信者から受け取ってください", "paperSign.step1.title0": "手順1：ダウンロード及び紙媒体契約書の印刷", "paperSign.step1.title0Desc.0": "ダウンロードし印刷した契約書にはすでに署名している電子印章の図案が含まれていなければなりません。紙媒体署名ファイルを取得してください。", "paperSign.step1.title1": "手順2：印章の捺印", "paperSign.step1.title1Desc": "紙媒体契約書に契約書に有効な会社の印章を捺印してください。", "paperSign.step1.title2.0": "手順3：", "paperSign.step1.title2.1": "スキャンデータのアップロード，", "paperSign.step1.title2.2": "認証コードを入力して、紙媒体署名を完了してください", "paperSign.step1.title2Desc.0": "紙媒体契約書をスキャンしデータ（PDFファイル）に変換後アップロードしてください。", "paperSign.step1.title2Desc.1": "検証コードを再入力すると、署名は完了します。電子契約書内にはお客様の印章図案は表示されませんが、この操作を行ったことは記録されます。", "paperSign.step2.title.0": "紙媒体契約書のスキャンデータ（PDFファイル）をアップロードしてください。", "paperSign.step2.title.1": "紙媒体契約書をダウンロードし署名した後、再度確定ボタンをタップして、紙媒体契約書のプロセスを終了します。", "paperSign.step2.uploadFile": "スキャンファイルのアップロード", "paperSign.step2.getCodeVerify": "契約署名検証の取得", "paperSign.step2.isUploading": "アップロード中...", "paperSign.operationCompleted": "操作完了", "changeSignDeadline.title": "契約状態の修正", "changeSignDeadline.desc": "確定をクリックすると、次のように変化します：", "changeSignDeadline.changeTime": "「署名期限切れ」の契約状態は「署名中」に修正され、契約書の署名契約期限は{signDeadline}に変更されます", "changeSignDeadline.changeTimeTip": "署名者が設定した「署名時効」は効力を失います", "changeSignDeadline.choose": "選択可能なのは：", "changeSignDeadline.checkSignNotice": "未署名契約書のアカウントに署名リマインダーをプッシュ送信", "changeSignDeadline.confirmChange": "契約状態の修正をしますか？ ", "changeSignDeadline.cancel": "キャンセル", "changeSignDeadline.confirm": "確定", "changeSignDeadline.signDeadlineTip": "署名期限の延長は最大で3回までとなります。", "addEntFolder.title": "契約フォルダーの新規作成", "addEntFolder.ent": "所属企業", "addEntFolder.entHolder": "選択してください", "addEntFolder.folderName": "契約フォルダー名", "addEntFolder.folderNameHolder": "20字以下とし、必ず記入しなければなりません", "addEntFolder.mark": "備考", "addEntFolder.markHolder": "100字以下とし、オプションとします", "addEntFolder.cancel": "キャンセル", "addEntFolder.confirm": "確定", "addEntFolder.folderNameTip": "契約フォルダー名は空欄にできません！", "entContractDownload.failTitle": "以下のドキュメントのダウンロードに失敗しました", "entContractDownload.documentId": "文書ID", "entContractDownload.documentTitle": "文書名称", "entContractDownload.failReason": "失敗の理由", "fileLimit.fileLessThan": "{num}M以下のファイルをアップロードしてください", "fileLimit.usePdf": "アップロードする際PDFファイルもしくは画像を使用してください", "fileLimit.beExcel": "Excelファイルをアップロードしてください", "fileLimit.beAttachmentFile": "PDF・Word・Excelもしくは画像形式のファイルをアップロードしてください", "fileLimit.beZip": "ZIP,7zファイルをアップロードしてください", "fileLimit.fileNameMoreThan": "ファイル名の長さが{num}を超えると、自動で切り取ります", "regs.entNameMaxLength": "企業名は60文字以下にしてください", "unPermissionRemind.title": "署名権限がありません", "unPermissionRemind.content.noAuth.1": "送信者の指定した署名企業名は：{receiverEntName}です", "unPermissionRemind.content.noAuth.2": "お客様のアカウントと実名登録された企業名が一致しません", "unPermissionRemind.content.noAuth.3": "１．ページのガイダンスに基づき、{receiverEntName}に対する認証を完了してください", "unPermissionRemind.content.noAuth.4": "２．認証済みであれば、企業名に記入ミスがないかどうか確認してください。もしも一致していない場合、発信者もしくはベストサインのカスタマーサービスまで確認の連絡をお願いします。", "unPermissionRemind.content.noJoin": "この契約書は{senderEntName}が{receiverEntName}に送信したものです。お客様のアカウントは{receiverEntName}に属しておらず、署名権限がありません。署名するためには当該企業に所属する為の申請をして頂く必要があります。", "unPermissionRemind.content.noView": "この契約書は{senderEntName}が{receiverEntName}に送信したもので、検索の結果、お客様に署名権限がない状況のため、申請が必要です。", "unPermissionRemind.content.noSign": "この契約書は{senderEntName}が{receiverEntName}に送信したもので、企業電子認証を使って署名する必要があります。検索の結果、署名権限がない状況のため、申請が必要です。", "unPermissionRemind.transferTip": "お客様も契約書を印章権限のあるスタッフに引き継いで署名を行うことができます。署名の取次が成功するとお客様に通知します。", "unPermissionRemind.transferBtn": "署名を引き継ぐ>>", "unPermissionRemind.giveOtherAuthTip": "お客様は他の管理者に引き継いで認証を行うことができます。", "unPermissionRemind.giveOtherAuthBtn": "他人の認証を引き継ぐ>>", "unPermissionRemind.noAuthPaperSign": "企業認証が完了していない場合、ダウンロードと印刷を選択して紙媒体での署名が行なえます。", "unPermissionRemind.otherPaperSign": "お客様も契約書ファイルのダウンロードと印刷が選択でき、企業印章管理者がオフラインで捺印署名することもできます。", "unPermissionRemind.paperSignBtn": "紙媒体署名 >>", "unPermissionRemind.applyBtn": "申請へ", "unPermissionRemind.authBtn": "認証へ", "unPermissionRemind.returnBtn": "戻る", "unPermissionRemind.transferContract": "転送", "unPermissionRemind.cantTransferTip": "收件方企业存在多业务线，暂不支持移交", "applyPermission.transferAdmin": "管理者主任に引き継ぐ", "applyPermission.transferAdminTip": "管理者主任に引き継いだ後、お客様は下記の権限を保留し、その他の権限を選択できます。", "applyPermission.giveOtherAuth": "他人の認証を引き継ぐ", "applyPermission.giveOtherAuthTip": "認証を他の人に移譲する時に、契約を円滑に締結するため、以下の権限を申請しています。実際のニーズに応じて、適切な権限を選択することもできます。", "applyPermission.rightApply": "権限のリクエスト", "applyPermission.adminName": "メイン管理者氏名", "applyPermission.account": "アカウント", "applyPermission.applyUserName": "申請者氏名", "applyPermission.senderNamePlaceholder": "お客様の氏名を入力してください", "applyPermission.senderAccountPlaceholder": "お客様のアカウントを入力してください", "applyPermission.specialSeal": "専用印章のテンプレート", "applyPermission.electronicSeal": "電子公印", "applyPermission.phoneOrEmail": "携帯電話番号/メールアドレス", "applyPermission.applyRight": "申請する権限", "applyPermission.ent": "メーカー", "applyPermission.receiverAccountPlaceholder": "引き継ぎする人のアカウントを入力してください", "applyPermission.sender": "引き継ぎ者", "applyPermission.yourName": "お客様の氏名", "applyPermission.applyName": "申請者氏名", "applyPermission.moreRight": "その他の権限", "applyPermission.confirmAdminTip": "メイン管理者権限を", "applyPermission.confirmAdminBtn": "申請", "applyPermission.otherWayApply": "その他方法で申請", "applyPermission.applyToAdmin": "申請", "applyPermission.sendBtn": "発信通知", "applyPermission.applyRightTip": "管理者主任に面識がない場合、その他の方法で申請することを選択します、企業のその他管理者もしくは印章管理者の許可を取得すると、契約書を確認できます。", "applyPermissionResult.title": "申請完了。", "applyPermissionResult.content11": "審査完了通知をお待ちください。", "applyPermissionResult.content12": "審査完了通知をお待ちください。", "applyPermissionResult.content2": "引き継ぎ者（{account}）に通知された時点で、お客様の取次申請は処理されます。処理結果についてはSMSでお客様に通知されます。", "applyPermissionResult.returnBtn": "戻る", "applyPermissionResult.bindingError": "账号暂未单点绑定成功，无法跳转，请等待审批完成", "morePermissionDialog.title": "その他の権限", "morePermissionDialog.confirmBtn": "確定", "morePermissionDialog.cancel": "キャンセル", "morePermissionDialog.viewContract": "企業契約を閲覧する", "morePermissionDialog.nowContract": "現在の契約書", "morePermissionDialog.nowSenderProxyContract": "現在の送信者が送信した契約", "morePermissionDialog.allSenderProxyContract": "すべての契約書", "morePermissionDialog.proxyContractTip": "閲覧権限を申請する対象の契約書には、署名者アカウントが指定されていない契約書と、指定された署名アカウントの契約書の中で他のアカウントに受領されたものが含まれます。", "morePermissionDialog.signContract": "契約書の署名", "morePermissionDialog.nowSenderContract": "現在の送信者が送信した契約", "morePermissionDialog.allSenderContract": "すべての契約書", "morePermissionDialog.noApplySignRight": "署名権限なし", "morePermissionDialog.canSignTip": "", "morePermissionDialog.allTip": "", "morePermissionDialog.error.1": "引き継ぎ先のアカウントを入力してください", "morePermissionDialog.error.2": "正しいアカウントを入力してください", "morePermissionDialog.error.3": "申請者の氏名を入力してください", "morePermissionDialog.error.4": "メイン管理者に権限を申請してください。メイン管理者からの応答がない場合は、「その他方法で申請」を試してください", "applyToBeAdminDialog.title": "管理者主任として申請", "applyToBeAdminDialog.content1": "お客様は{entName}企業のシステム管理者主任として申請しています。管理者主任の主な職責と権限は：", "applyToBeAdminDialog.content2": "１、企業印章の使用と割り当て | ２、企業構成員の管理 | ３、企業契約書の管理", "applyToBeAdminDialog.tip": "その他の管理者主任機能は申請が成功してから、PC版のベストサインプラットフォームhttp://ent.bestsign.cnにログインして確認してください。", "applyToBeAdminDialog.footer": "メイン管理者は、通常、会社の法定代表者、財務責任者、法務責任者、IT部門責任者等が務め、責務の効果的な遂行を確保します。", "applyToBeAdminDialog.confirm": "申請する", "applyToBeAdminDialog.cancel": "キャンセル", "infoProtectDialog.userAuth": "使用刷脸服务须同意", "infoProtectDialog.titleWithSeperator": "《上上签如何保护您的个人信息》", "infoProtectDialog.title": "上上签如何保护您的个人信息", "infoProtectDialog.auth": "实名认证", "infoProtectDialog.faceSign": "刷脸签署", "infoProtectDialog.contentDesp": "您提交个人身份等信息（以下简称\"个人信息\"）时已经充分知悉并同意：", "infoProtectDialog.detailTip1": "（1）为了完成您的{title}，您已经授权您的企业自行或委托上上签将您的个人信息提交给为实现电子签约之目的而提供相应服务的其他主体（比如CA机构、公证处等）；", "infoProtectDialog.detailTip2": "（1）の承認内容に加えて、あなたは{title}の意思確認（顔認証署名）のために顔情報を提出することに別途同意し、BestSignが電子署名サービスとそれ関連する証明書の発行を提供する目的でのみお客様の顔情報を確認・保管・参照・共有することに同意します。もしこの内容に同意しない場合は、直ちに顔情報の提供を中止し、他の認証方法を選択してください。", "infoProtectDialog.detailTip3": "（3）除了前述（1）（2）以及法律规定的情形外，上上签未经您的授权不会主动将您的个人信息提交给任何第三人。", "infoProtectDialog.know": "知道了", "faceSign.faceFailed": "非常抱歉，您的人脸比对失败", "faceSign.verifyTry": "请核实身份信息后重试", "faceSign.upSignReq": "今天的人脸比对次数已达到上限，请明天重试或联系合同发起者修改签署要求", "faceSign.reqFace": "发件人要求你进行刷脸校验", "faceSign.signAfterFace": "刷脸通过后即可完成合同签署", "faceSign.qrcodeInvalid": "二维码信息已过期，请刷新", "faceSign.nameIs": "姓名为", "faceSign.IDNumIs": "身份证号为", "faceSign.retry": "重试", "faceSign.pleaseScanToSign": "Alipay・WeChatを使用してQRコードを読み取って署名してください。", "faceSign.pleaseScanAliPay": "请使用支付宝app扫描二维码签署", "faceSign.pleaseScanWeChat": "请使用微信app扫描二维码签署", "entDocList.title": "企業契約書管理", "entDocList.goBackDocList": "マイ契約書管理に戻る", "entDocList.createEntFolder": "契約フォルダーの新規作成", "entDocList.columnFolderName": "契約フォルダー名", "entDocList.columnCreateUserName": "作成者", "entDocList.columnRemark": "備考", "entDocList.columnPermission": "権限", "entDocList.permissionManage": "権限管理", "entDocList.emptyText": "データなし", "entDocList.operate": "操作", "entDocList.view": "確認", "entDocList.noPermission": "明細リストを表示する権限がありません", "entFolderPermission.title": "企業契約書管理", "entFolderPermission.myPermission": "現アカウントのフォルダー権限には次のものが含まれています：", "entFolderPermission.assignPermission": "フォルダー権限の設定", "entFolderPermission.unAssignPermission": "権限の取り消し", "entFolderPermission.permissionResult": "授権結果", "entFolderPermission.permissionLog": "授権ログ", "entFolderAssignPermission.title": "フォルダー権限の設定 | 権限の取り消し", "entFolderAssignPermission.next": "次へ", "entFolderAssignPermission.cancel": "キャンセル", "entFolderPermissionPerson.title": "授権者の選択 | 取り消しを行う権限者の選択", "entFolderPermissionPerson.role": "役割の授権", "entFolderPermissionPerson.user": "授権者", "entFolderPermissionPerson.confirm": "確定", "entFolderEditFolder.title.folderName": "フォルダー名の変更", "entFolderEditFolder.title.remark": "フォルダーの備考", "entFolderEditFolder.folderNamePlaceholder": "フォルダー名を入力してください", "entFolderEditFolder.folderNameEmpty": "契約フォルダー名は空欄にできません！", "entFolderEditFolder.remark": "備考を記入", "entFolderEditFolder.remarkPlaceholder": "入力してください", "entFolderEditFolder.confirm": "確定", "entFolderEditFolder.cancel": "キャンセル", "transferFolder.title": "契約フォルダーの転送", "transferFolder.oldOwner": "元対象者：", "transferFolder.newOwner": "新対象者：", "transferFolder.confirm": "確定", "transferFolder.successTip": "転送完了", "entDoLisSlider.back": "マイ契約書管理に戻る", "entDoLisSlider.reName": "名前の変更", "entDoLisSlider.transfer": "転送", "entDoLisSlider.editPermission": "権限の編集", "entDoLisSlider.editReMark": "備考", "entDoLisSlider.remind": "契約リマインダー", "entDoLisSlider.creator": "作成者：", "entDoLisSlider.myPermission": "マイ権限：", "entDoLisSlider.remark": "備考：", "entDoLisSlider.borrowApprover": "借覧審査者：", "entDoLisSlider.addApprover": " ＋ 追加", "entDoLisSlider.deleteApproverSuc": "削除完了", "entDoLisSlider.know": "わかりました。", "entDoLisSlider.deleteApproverTip": "企業フォルダーに借覧審査者がいない場合、「借覧権限」は使用不可、つまり使用者がこのフォルダー内の契約書の借覧ができないことを意味します。最終的には借覧審査者を一名必ず残してください。", "entDoLisSlider.deleteApproverTitle": "注意", "entDocTable.operate": "操作", "entDocTable.download": "ダウンロード", "entDocTable.view": "確認", "entDocTable.borrow": "借覧審査", "entSearch.moreBusinessFields": "詳細(業務フィールド)", "entSearch.plsSelect": "選択してください", "entSearch.contractInvolveTip": "個人もしくは企業構成員の参画する契約書", "entSearch.searchFor": "検索", "entSearch.unfold": "展開する", "entSearch.putAway": "折りたたむ", "entSearch.export": "このフォルダー明細をエクスポート", "entSearch.listConfig": "このフォルダー明細を設定", "entSearch.plsInput": "入力してください", "entSearch.inputSenderPersonAndCompany": "送信者名を入力してください", "entSearch.inputReceiverPersonAndCompany": "受信者名を入力してください", "entSearch.plsSelectData": "日付を選択してください", "entSearch.confirm": "確認", "entSearch.noPermission": "明細リストを表示する権限がありません", "entSearch.notSupported": "現時点では非対応", "entDocDetail.mainContractDetail": "主契約内容", "entDocDetail.correctorContract": "契約書の関連付け", "entDocDetail.contractInfo": "契約の情報：", "entDocDetail.contractTitle": "契約表題：", "entDocDetail.contractId": "契約書番号：", "entDocDetail.idMainContract": "契約書は主契約ですか：", "entDocDetail.entFolderName": "契約フォルダー名：", "entDocDetail.yes": "YES", "entDocDetail.no": "NO", "entDocDetail.tag": "契約タグ：", "entDocDetail.loanRecord.title": "借覧ログ：", "entDocDetail.loanRecord.account": "借覧アカウント", "entDocDetail.loanRecord.time": "借覧日時", "entDocDetail.loanRecord.duration": "借覧時間", "entDocDetail.loanRecord.timeUnit": "日", "noReadPermissionTip.title": "注意", "noReadPermissionTip.tip": "現在の企業フォルダー内では、お客様に契約書の確認と借覧の権限がありません。管理員に連絡し関連権限を取得してから再度試してください。", "noReadPermissionTip.know": "わかりました。", "applyBorrowDialog.title": "契約書の借覧申請", "applyBorrowDialog.borrowTime": "借覧時間：", "applyBorrowDialog.timePlaceHolder": "選択してください", "applyBorrowDialog.timeUnit": "日", "applyBorrowDialog.reasonPlaceHolder": "審査者が借覧許可をするかどうかの判断をするため、借覧理由を記入してください。オプション、255文字以下とします", "applyBorrowDialog.approver": "審査者：", "applyBorrowDialog.rejectReason": "却下理由：", "applyBorrowDialog.cancel": "キャンセル", "applyBorrowDialog.reApply": "再申請", "applyBorrowDialog.confirm": "確認", "applyBorrowDialog.applyingStatus": "審査中", "applyBorrowDialog.successStatus": "審査通過", "applyBorrowDialog.rejectedStatus": "審査却下", "applyBorrowDialog.noTimesTip": "先に借覧時間を選択してください！", "applyBorrowDialog.applySendTip": "申請送信完了！", "addApproverDialog.title": "構成員の選択", "addApproverDialog.confirm": "確定", "addApproverDialog.maxApproverTip": "借覧審査者は3名を超えないでください！", "addApproverDialog.noApproverTip": "先に構成員を選択してください！", "borrowApproval.title": "借覧審査", "borrowApproval.applicant": "借覧構成員", "borrowApproval.borrowTime": "借覧時間", "borrowApproval.borrowReason": "借覧理由", "borrowApproval.approvalRes": "借覧結果", "borrowApproval.reject": "却下", "borrowApproval.agree": "通過", "borrowApproval.timeUnit": "日", "borrowApproval.rejectTitle": "却下理由", "borrowApproval.rejectPlaceHolder": "却下理由を記入してください。オプションで、255文字以下とします", "borrowApproval.cancel": "キャンセル", "borrowApproval.confirm": "確認", "components.sendPointPosition.index-1c6dcb-1": "キーワードとマッチしない捺印欄・署名欄があります。仮に契約書1ページ目の左下隅に配置しますので、手動で調整してください。", "components.sendPointPosition.index-1c6dcb-2": "ヒント", "components.sendPointPosition.index-1c6dcb-3": "確定", "components.sendPointPosition.index-1c6dcb-4": "所在しますドキュメント：", "operate.certificateReport": "电子签约存证报告", "operate.notarization": "公证书", "operate.selectProject": "証明書申請項目を選択してください", "operate.downloadAttacment": "契約締結証明書ダウンロード", "operate.downloadAttacmentTip": "契約締結証明書では、契約書の基本情報および契約送受信から署名完了までのすべての署名者と署名情報を記録しています。", "special-doc-dialog.tip1": "システムは、ドキュメントに時計回りの回転 [{rotateContent}] があることを検出しました。これには、コントラクトを開始する前に前処理が必要です。プロセス全体はシステムによって自動的に完了します。", "special-doc-dialog.tip2": "前処理が必要な一般的な理由", "special-doc-dialog.tip3": "Adobe Reader (Foxit Reader などの他の PDF リーダーを含む) でファイルを表示すると、レイアウトが反転し、ファイルが「回転」していることがわかりました。", "special-doc-dialog.tip4": "たとえば、横長であるはずの紙の文書をスキャナーでスキャンすると縦長に処理されてしまいました。スキャンした PDF を Adob​​e で読み取ると、ページ上のテキストが反転していることに気づき、Adobe Reader ツールバーをクリックしました。回転ボタンは、このページの PDF を横向きに「回転」させます。", "special-doc-dialog.tip5": "前処理後の影響（ファイルサイズが大きくなります）", "special-doc-dialog.tip6": "1. 元の文書に追加されたCA証明書や他の暗号化戦略は、前処理後に無効になります。", "special-doc-dialog.tip7": "2. 送信される契約書の文字は選択やコピーができなくなります。", "special-doc-dialog.tip8": "3. 文書の鮮明度が低下し、ファイルサイズが大きくなる場合があります。", "special-doc-dialog.tip9": "前処理済みファイルを引き続き使用する場合は、「続行」ボタンをクリックして契約送信処理を完了してください。", "special-doc-dialog.tip10": "元のファイルを直接使用し、システムの前処理をスキップします", "special-doc-dialog.tip11": "方法 1: Adob​​e Reader (Foxit Reader などの他の PDF リーダーを含む) を使用して PDF を開き、ページのサムネールを右クリックして「回転」操作を実行するか、Adobe Reader をクリックします。ツールバーのボタンを回転し、反時計回りに回転させて初期状態にしてから、再度アップロードします (ファイルが反転され、テキストが反転していることを無視して、直接使用します)。", "special-doc-dialog.tip12": "回路図を見る", "special-doc-dialog.tip13": "方法 2 (PDF スキャンが利用可能): 紙文書を再スキャンして正しいレイアウト (スキャン時に紙の水平方向と垂直方向が正しく設定される) を取得し、ファイルを再アップロードして契約書を送信します。", "SsoConfirm.index-c220bb-1": "", "SsoConfirm.index-c220bb-2": "", "SsoConfirm.index-c220bb-3": "", "SsoConfirm.index-c220bb-4": "", "SsoConfirm.index-c220bb-5": "", "SsoConfirm.index-c220bb-6": "", "SsoConfirm.index-c220bb-7": "", "SsoConfirm.index-c220bb-8": "", "SsoConfirm.index-c220bb-9": "", "SsoConfirm.index-c220bb-10": "", "SsoConfirm.index-c220bb-11": "", "SsoConfirm.index-c220bb-12": "", "SsoConfirm.index-c220bb-13": "", "sealSelectDialog.title1": "印章使用申請", "sealSelectDialog.title2": "押印者の設定", "sealSelectDialog.nowApplySealList": "以下の印章を要求しています", "sealSelectDialog.chooseApplyPersonToDeal": "押印者を選択して下さい（この契約を引き続き閲覧し、フォローすることができます）。", "sealSelectDialog.contactGroupAdminToDistributeSeal": "集団の管理者に印章を分配するよう連絡してください", "sealSelectDialog.cancel": "取り消し", "sealSelectDialog.confirm": "使用", "sealSelectDialog.confirmSucTip": "申請が成功し、相手側の処理を待っています", "sealSelectDialog.applySealTip": "この契約は現在{person}（{account}）の署名待ちです。以下の２つの方法のどちらかで進めてください：", "sealSelectDialog.tipContent1": "1. {person}（{account}）に署名を依頼する：", "sealSelectDialog.tipContent1Desc": "{person}（{account}）に署名してもらいたい場合は、電子署名プラットフォームにログインして署名を完了するように依頼してください。", "sealSelectDialog.tipContent2": "2. 自分で署名する：", "sealSelectDialog.tipContent2Desc": "自分で契約書に署名したい場合（既に押印権限をお持ちの場合）、まずは{person}（{account}）に契約を拒否してもらってください：", "sealSelectDialog.refuseSignTip1": "手順1：電子署名プラットフォームにログインし、該当の契約書を選択します。", "sealSelectDialog.refuseSignTip2": "手順2：契約詳細ページで「拒否」ボタンをクリックします。", "sealSelectDialog.refuseSignTip3": "手順3：表示されるダイアログで拒否を実行します。", "sealSelectDialog.refuseSignAfter": "拒否後の操作：", "sealSelectDialog.refuseSignAfterDesc": "{person}（{account}）が拒否を完了すると、契約詳細ページに新たに「署名」ボタンが表示されます。このボタンをクリックし、通常の手順で契約の署名を完了してください。", "sealSelectDialog.specialScene": "特別な状況の対処：", "sealSelectDialog.specialSceneOpt": "{person}（{account}）と連絡が取れない場合は、メイン管理者に依頼して、「契約転送」機能を使用して、{person}（{account}）から他のメンバーに署名権限を移転すれば、上記手順で署名操作ができます。", "templateReceiverConfig.err.needAddSender": "本企業/本人が署名者に設定されていないので、契約書が送信されたら、あなたは契約書に署名しません。署名者として追加しますか？", "templateReceiverConfig.err.addSender": "署名者として追加", "templateReceiverConfig.err.needSeal": "【押印】の署名要件に従い、以下の文書に【押印】の位置を設定する必要がある", "templateReceiverConfig.err.needSignature": "【署名】の署名要件に従い、以下の文書に【署名】の位置を設定する必要があります", "templateReceiverConfig.err.needSealSignature": "【押印及署名】の署名要件に従い、同一の文書内の署名場所には「押印」と「押印者署名」を同時に設定する必要があります。", "templateReceiverConfig.err.needSealTip": " 署名者は文書上に他のフィールド（署名日付または署名者が記入するフィールド）が存在しますが、押印箇所が設定されていません。他のフィールドを削除するか、押印箇所を設定することができます。", "templateReceiverConfig.err.needSignatureTip": "署名者は文書上に他のフィールド（署名日付または署名者が記入するフィールド）が存在しますが、サイン箇所が設定されていません。他のフィールドを削除するか、サイン箇所を設定することができます。", "labelLackTip.everyDocSign": "署名者が各文書に署名位置を設定しないと、文書の閲覧や署名ができません。", "labelNameConflictTip.hasExistName": "文書内に「{name}」という名前のフィールドが既に{num}つ存在します。", "labelNameConflictTip.nameChangeAdvice": "「{name}」フィールドの内容が完全に一致する必要がある場合は、引き続き「{name}」をフィールド名として使用できます。内容が異なる場合は、フィールドの内容や目的を正確に説明する名前を選択することをお勧めします。", "labelNameConflictTip.pleaseChangeName": "名前を付けようとしているフィールド名「{name}」はすでに存在します。混乱を避けるために、異なる名前を選択してください。", "labelNameConflictTip.inputLabelName": "下の入力欄に新しいフィールド名を入力してください。", "labelNameConflictTip.closeTip": "ポップアップウィンドウを閉じると、フィールドは保存されず自動的に削除されます。", "labelNameConflictTip.closeTipOfEdit": "弹窗を閉じると、フィールド名の変更が保存されません。", "labelEdit.importantNotice": "重要なお知らせ", "labelEdit.tipInfo": "複数の署名役割を設定し、各署名役割は指定された押印箇所に署名させることをお勧めします。", "labelEdit.tipInfos.title1": "通常のシナリオ：", "labelEdit.tipInfos.con1": "電子契約書上の同じ「署名役割」の複数の押印箇所では、デフォルトで同じ印章が使用されます。各押印箇所に利用する印章を手動で変更することは可能ですが、これは見落としにつながる可能性があります。", "labelEdit.tipInfos.title2": "推奨方法：", "labelEdit.tipInfos.con2": "正確性を確保するために、署名者を複数の署名役割に設定し、各役割にそれぞれの印章を利用することをお勧めします。これにより、各署名役割で署名する際に、使用する印章を個別に確認する必要があり、混乱を減らすことができます。", "labelEdit.tipInfos.title3": "特殊なケース：", "labelEdit.tipInfos.con3": "すべての署名箇所に同じ印章を使用する必要がある場合は、電子署名プラットフォーム内で一つの署名役割が複数の押印箇所に署名することが適切です。", "labelEdit.detailedInstructions": "詳細説明", "labelEdit.operationDiagram": "操作手順図", "templateDetail.noEditPermission": "テンプレートファイルをダウンロードするには、先に「テンプレート編集」の権限を取得する必要があります", "templatePermission.table.searchPlaceholder": "アカウントを入力してテンプレート権限を検索", "permissionRight.dialog.rightName": "権限名称", "permissionRight.dialog.recycleRight": "権限取り消し方法", "permissionRight.dialog.moreTip": "詳細説明", "permissionRight.dialog.recycleByAccount": "アカウント{account}の権限を取り消す", "permissionRight.dialog.recycleByRole": "ロール「{role}」からこのアカウントを削除する（コンソールでの操作が必要）", "permissionRight.dialog.sendTip1": "以下の企業に契約を送信できます：", "permissionRight.dialog.sendTip2": "テンプレートから取得した権限：{ents}", "permissionRight.dialog.sendTip3": "グループ管理権限から取得した権限：{ents}", "permissionRight.dialog.grantManage": "テンプレートのすべてのユーザーが「権限割り当て」の権限を持っていない場合、システムは自動的にメイン管理者にこの権限を付与し、この権限は取り消すことができません（他のユーザーも「権限割り当て」権限を持っている場合のみ、メイン管理者からこの権限を取り消すことができます）。", "permissionRight.dialog.roleAffect": "{entName}の以下のアカウントに対応しています：", "permissionRight.dialog.noRight": "現在のアカウントはこのテンプレートに対する権限を持っていません。", "permissionRight.dialog.more": "（{count}社）", "agent.riskJudgement": "AI弁護士", "agent.uploadText": "リスク判断が必要なファイルのアップロードをお願いします", "agent.startTips": "これでリスクはんだんを始められです", "agent.feedback": "調査フィードバック", "agent.satisfy": "对分析结果满意，继续下一项分析", "agent.dissatisfy": "对分析结果不满意，重新进行分析", "agent.custom": "カスタム審査ルールを入力してください", "agent.submit": "送信", "agent.others": "その他", "agent.autoExtract": "抽出完了まで自動処理継続", "agent.autoRisk": "分析プロセスを自動実行", "agent.aiGenerated": "AI Generated - © BestSign", "agent.extractTitle": "情報抽出", "agent.riskTitle": "AI弁護士", "agent.deepInference": "AI弁護士（深度推論）", "agent.chooseRisk": "解析対象ファイルを選択", "agent.chooseExtract": "抽出用ソースファイル指定", "agent.analyzing": "コンテンツ解析実行中", "agent.advice": "修正案自動作成中", "agent.options": "選択肢生成処理中", "agent.selectFunc": "ご希望の機能をお選びください", "agent.inputTips": "正確な情報を入力必須", "filter.yes": "はい", "agent.deepThinking": "深い思考中", "agent.deepThoughtCompleted": "深い思考が完了しました", "agent.original": "原本", "agent.revision": "改善提案", "agent.diff": "比較", "charge.packagePurchaseTitle": "【{title}機能】プラン購入", "charge.payOnce": "特惠限购一次", "charge.payNow": "今すぐ購入", "charge.amount": "数量", "charge.unitPrice": "単価", "charge.limitTime": "有効期間", "charge.copy": "部", "charge.month": "月", "charge.compareInfo1": "ご利用ガイド：", "charge.compareInfo2": "{index}、購入{type}に基づく利用可能限度額は、対応する企業の全メンバーが利用可能です。個人でご利用の場合は、画面上部のログイン主体を個人アカウントに切り替えてください。", "charge.compareInfo3": "{index}、アップロードした契約書の{per}数に基づく使用量計算", "charge.codePay": "QRコードをスキャンして支払い", "charge.aliPay": "アリペイ支払い", "charge.wxPay": "ウィーチャット支払い", "charge.paySuccess": "購入成功", "charge.payIno": "機能有効化 | 購入対象 | 支払金額", "charge.contactUs": "お問い合わせ | QRコードをスキャンして専門アドバイザーに相談", "copySealDialog.title": "押印欄をコピー", "copySealDialog.pageDecTop": "", "copySealDialog.pageDecMiddle": "ページ目と", "copySealDialog.pageDecBottom": "ページ目に押印欄をコピーします。", "copySealDialog.dec": "現在のページの押印欄を他のページにコピーし、これらのページの押印欄は現在のページと一致します。その後、各ページの押印欄は各自調整できます。", "copySealDialog.moreBtn": "詳しい説明", "copySealDialog.moreDec": "契約文書の前半部分に押印欄が設定されている場合にのみ、「押印欄をコピー」ボタンが表示されます（例：100ページの文書で、1～50ページの中に押印欄がある場合に限り、ワンクリックで他のページへ押印欄をコピーできます）。", "copySealDialog.confirm": "確定", "copySealDialog.cancel": "キャンセル", "labelEdit.friendlyReminder": "ご案内：", "labelEdit.decorateTipInfos.con1": "ワンクリックでコピーされた押印欄のうち、最後の印影のみに電子証明書が付与されます。これで、署名行為の合法性・真正性が十分に確保されます。", "labelEdit.decorateTipInfos.con2": "もし特定の重要な箇所に電子証明書付きの印章を必ず使用したい場合は、ステップ2：「印章」コンポーネントをドラッグして、押印欄を設定してください。このように設定された印章には電子証明書が付与されます。", "labels.sealCopy": "押印欄をコピー", "labels.delCopySeal": "現在の押印欄のみを削除", "labels.delAllCopySeal": "コピーされたすべての押印欄を削除", "labelEdit.blankTip": "契約テンプレートを使用して契約を送信する場合、署名位置や入力フィールドはシステムによって自動生成されるため、別途指定する必要はありません。", "labelEdit.autoPosition": "パターン1：キーワードに基づいて生成", "labelEdit.autoPositionTip": "契約書内の押印欄や入力フィールドの位置は、契約書内のキーワードの位置に基づいて自動生成されます。キーワードが存在しない場合は、1ページ目の左下に配置されます。押印欄の位置（押印欄/フィールドとキーワードとの距離）をドラッグして調整することをお勧めします。", "labelEdit.allPage": "パターン2：事前に設定された位置に基づいて生成", "labelEdit.allPageTip": "契約書内の押印欄や入力フィールドの位置は、事前に設定された位置の座標に基づいて自動生成されます。", "labelEdit.staticPosition": "全てのページの同じ位置に、押印欄/入力フィールドを生成します。", "docDetail.dualRequired": "二重記録検証が必須です", "authorize.title": "ご利用条件", "authorize.content": "AI契約分析で業務効率化！同意の上、今すぐ体験", "authorize.cancel": "いいえ", "authorize.confirm": "同意して利用開始", "authorize.contract": "『ハッブル製品利用条件』を確認", "agent.aiInterpret": "AI解読", "agent.reJudge": "再判断", "agent.tipsContent": "再判定を行うと利用回数が減ります。続けますか？", "agent.confirm": "確認", "agent.tip": "提示", "aiAgent.title": "押印エージェント", "aiAgent.description1": "ローカルからアップロードされた文書について、エージェントが自動的に各署名者の押印位置を見つけ、手動で押印位置を設定する煩雑な作業を代替します", "aiAgent.description2": "*単一文書のページ数は50ページを超えることはできません", "aiAgent.description3": "*「テンプレート専用印」、「自動押印」などの機能と組み合わせることで、非標準契約の押印の完全自動化を実現できます", "aiAgent.configTitle": "押印ルール設定", "aiAgent.configTip": "使用するルールにチェックを入れ、各ルールの順序を調整して、ルールが競合する際の優先順位を明確にしてください。", "aiAgent.rule.needOverlappingSeal": "割印が必要", "aiAgent.rule.needOverlappingSealDesc": "*チェックしない場合、割印の押印位置は自動追加されません", "aiAgent.rule.reciprocalSealing": "対等押印：既存の押印位置に基づいて署名者の押印位置を照合", "aiAgent.rule.reciprocalSealingDesc": "*例えば、契約書に相手方の3つの印章（異なるページに分散）が既にある場合、システムは自動的に対応する位置に我が方の3つの対応する押印位置を生成します。注：この機能は一方押印時のみ有効です。", "aiAgent.rule.sealOnLastLine": "ファイルに明確な押印位置がない場合、ファイルの最後の行のテキストに押印", "aiAgent.rule.sealOnLastLineDesc": "*従来の意味での契約書ではなく、明確な押印位置の指示がないファイル、例えば内部伝票など押印が必要なファイル", "aiAgent.rule.reasonableAreaSealing": "印章は合理的な領域に配置すべき（押印欄付近、契約書最終ページの会社情報部分など）", "aiAgent.rule.reasonableAreaSealingDesc": "*対等押印、各ページ押印などのルールと競合する可能性があり、優先的に満たす必要があるルールを確保するために優先順位を調整する必要があります", "aiAgent.rule.sealEachPage": "各ページ押印", "aiAgent.rule.sealEachPageDesc": "*照合表、入札文書の場合、各ページの同じ位置に押印が必要です", "aiAgent.adjustmentTitle": "セルフサービス最適化", "aiAgent.positionAdjustment": "(1) 押印位置最適化（割印を除く）", "aiAgent.moveUp": "システムが指定した押印位置を上に移動する必要があります", "aiAgent.moveLeft": "システムが指定した押印位置を左に移動する必要があります", "aiAgent.centimeter": "センチメートル", "aiAgent.adjustTip1": "*負の数を入力することで、反対方向を表すことができます。", "aiAgent.adjustTip2": "*Nセンチメートルは印刷後のサイズです。デフォルトでは、印章の直径は約4センチメートルで、これに基づいて換算できます", "aiAgent.adjustTip3": "*押印位置を移動すると、既存の押印位置と重複する可能性があります。ご了承ください", "aiAgent.contentAdjustment": "(2) 押印するかどうかの調整", "aiAgent.addSealPrefix": "ファイルに次の内容が現れた場合", "aiAgent.addSealSuffix": "その内容に対応する位置に押印が必要です", "aiAgent.removeSealPrefix": "ファイルに次の内容が現れた場合", "aiAgent.removeSealSuffix": "その内容に対応する位置に押印は不要です", "aiAgent.adjustTip4": "*ここに設定すると、ルールは最高優先度になります。この設定は、片方の押印時のみ有効です", "aiAgent.saveConfig": "設定を保存", "aiAgent.resetConfig": "設定をリセット", "aiAgent.featureNotAvailable": "上上签サポートスタッフに連絡して有料機能を有効にしてください", "aiAgent.enableSuccess": "押印エージェントを有効にしました", "aiAgent.disableSuccess": "押印エージェントを無効にしました", "aiAgent.operationFailed": "操作に失敗しました。後でもう一度お試しください", "aiAgent.templateIdRequired": "テンプレートIDは空にできません", "aiAgent.saveSuccess": "保存に成功しました", "aiAgent.saveFailed": "保存に失敗しました。後でもう一度お試しください", "aiAgent.loadFailed": "設定の読み込みに失敗しました。後でもう一度お試しください", "autoStamp.buttonText": "自動押印", "autoStamp.progressTitle": "押印エージェントが{documentNames}の内容に基づいて署名者の押印位置を指定しています。押印完了後、結果をご確認ください。ご期待に沿うものであれば、「送信」ボタンをクリックして、この契約書を正式に送信してください。", "autoStamp.progressText": "進行状況{percentage}%", "autoStamp.cancelButton": "自動押印をキャンセル", "autoStamp.taskComplete": "自動押印が完了しました", "autoStamp.taskFailed": "自動押印に失敗しました。後でもう一度お試しください"}