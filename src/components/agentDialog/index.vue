<template>
    <div>
        <el-dialog
            :visible.sync="dialogVisible"
            :show-close="false"
            custom-class="agent-dialog"
            :append-to-body="true"
            @close="closeDialog"
        >
            <div slot="title" class="agent-dialog__header">
                <div class="agent-dialog__header-left">
                    <p>{{ agentTitle }}</p>
                </div>
                <i class="el-icon-ssq-guanbi" @click="closeDialog"></i>
            </div>
            <AgentInfoContent
                v-if="dialogVisible"
                ref="agentInfoContent"
                :contract-id="contractId"
                :receiver-id="receiverId"
                :riskList="riskList"
                :agentType="agentType"
                :productType="productType"
                :productConfig="productConfig"
                @showCharge="showChargeDialog = true"
                @renderFragment="(list) => $emit('renderFragment', list)"
                @locateShadow="(num) => $emit('locateShadow', num)"
            ></AgentInfoContent>
        </el-dialog>
        <ChargeDialog
            :show="showChargeDialog"
            :onlyPerson="true"
            :productConfig="productConfig"
            :productType="productType"
            @handleClose="getPayResult"
            @hideDialog="showChargeDialog = false"
        />
    </div>
</template>

<script>
import AgentInfoContent from '@/components/agentInfoContent/index.vue';
import ChargeDialog from '@/components/charge/index.vue';
export default {
    name: 'AgentDialog',
    components: {
        AgentInfoContent,
        ChargeDialog,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        contractId: {
            type: String,
            default: '',
        },
        receiverId: {
            type: String,
            default: '',
        },
        agentTitle: {
            type: String,
            default: '',
        },
        riskList: {
            type: Array,
            default: () => [],
        },
        agentType: {
            type: String,
            default: '',
        },
        productType: {
            type: Number,
            default: 15, // 15比对，16翻译
        },
        productConfig: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            showChargeDialog: false,
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(v) {
                this.$emit('update:visible', v);
            },
        },
    },
    watch: {},
    methods: {
        closeDialog() {
            this.$emit('close');
        },
        getPayResult() {
            this.showChargeDialog = false;
            this.$refs.agentInfoContent.getPayResult();
        },
    },
};
</script>

<style lang="scss" scoped>
 ::v-deep .agent-dialog {
    height: 90vh;
    width: 600px;
    margin-top: 0 !important;
    top: 50% !important;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    .agent-dialog__header {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-shrink: 0;
        &-left {
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            p {
                font-size: 16px;
            }
        }
        .el-icon-ssq-guanbi {
            margin-inline-start: 10px;
            cursor: pointer;
        }
    }
    .el-dialog__header {
        padding: 14px 15px !important;
    }
    .el-dialog__body {
        padding-left: 0px !important;
        padding-right: 0px !important;
        padding-bottom: 0px !important;
        flex-grow: 1;
        overflow: hidden;
    }
}
</style>
