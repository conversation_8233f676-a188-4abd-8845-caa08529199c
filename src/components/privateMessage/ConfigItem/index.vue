<template>
    <div v-if="type==='edit'" class="config-item-edit">
        <el-checkbox v-if="configItem.hasOwnProperty('ifAllowedDownload')" v-model="configItem.ifAllowedDownload">{{ $t('addReceiver.canDownload') }}</el-checkbox>
        <div class="fr">
            <el-switch
                v-model="configItem.ifDisplayConfig"
                :width="30"
            >
            </el-switch>
            {{ $t('addReceiver.shouEntry') }}<CommonTip class="item"
                effect="dark"
                :content="$t('addReceiver.shouEntryTip')"
                placement="top"
            />
            <NecessarySelect :value="configItem.ifRequired" @input="update" />
        </div>
    </div>
    <div v-else class="config-item-use">（{{ configItem.ifRequired ? $t('localCommon.require') : $t('localCommon.optional') }}）<span class="error-tip" v-if="error">{{ error }}</span></div>
</template>
<script>
import NecessarySelect from 'src/components/necessarySelect';
export default {
    components: {
        NecessarySelect,
    },
    props: {
        item: {
            type: Object,
            default: () => {

            },
        },
        type: {
            type: String,
            default: '',
        },
        error: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            configItem: {},
        };
    },
    watch: {
        item: {
            immediate: true,
            handler(newValue) {
                this.configItem = newValue;
            },
        },
        configItem: {
            immediate: true,
            handler(newValue, oldValue) {
                if (newValue !== oldValue) {
                    this.$emit('changeConfig', newValue);
                }
            },
        },
    },
    methods: {
        update($event) {
            this.configItem.ifRequired = $event;
        },
    },
};
</script>
<style lang="scss">
  .config-item-edit{
    padding-left: 100px;
    line-height: 24px;
    height: 24px;
    .el-checkbox {
      .el-checkbox__label{
        font-size: 12px;
      }
      .el-checkbox__input .el-checkbox__inner{
        width:12px;
        height: 12px;
        &::after{
          top:0;
          left:3px;
        }
      }
    }

  }
  .config-item-use{
    font-size: 12px;
    line-height: 24px;
    height: 24px;
    color: $--color-info;
    .error-tip{
      color: $--color-danger;
    }
  }
</style>
