import { createWrapper, createStore } from 'src/testUtils';
import flushPromises from 'flush-promises';
import cancelContract from '../index';
import cloneDeep from 'lodash/cloneDeep';
import {  RouterLinkStub } from '@vue/test-utils';
// const localVue = createLocalVue();
import openPageAfterAjax from 'pub-utils/linkOpener.js';
jest.mock('src/lang/index.js');
jest.mock('pub-directives/va/index.js');
// jest.mock('pub-components/countDown/index.vue', () => ({
//     template: '<div></div>',
// }));
jest.mock('pub-utils/linkOpener.js');
jest.useFakeTimers();
describe('cancelContract.vue', () => {
    const mockBatchOperateContracts = jest.fn().mockResolvedValue({
        data: {
            code: '0',
        },
    });
    const baseStoreOptions = {
        getters: {
            getUserAccount: jest.fn().mockImplementation(() => {
                return '123';
            }),
            SIGN_SECOND_CHECK: jest.fn().mockImplementation(() => {
                return false;
            }),
        },
        modules: {
            doc: {
                name: 'doc',
                namespaced: true,
                actions: {
                    batchOperateContracts: mockBatchOperateContracts,
                },
            },
        },
    };
    const storeOptions = cloneDeep(baseStoreOptions);
    const store = createStore(storeOptions);
    const mockGetFun = jest.fn().mockResolvedValue({
        data: [
            { reasonType: 6, id: '66' },
            {
                code: '<EMAIL>',
                notificationId: '2763795238253234183',
                type: 1,
                id: '11',
                userId: '2763795238085462018',
                reasonType: '1',
                refuseReason: 'aaa',
            }, {
                code: '456',
                notificationId: '2764418947246725124',
                type: 2,
                id: '22',
                userId: '2763795238085462018',
                reasonType: '2',
                refuseReason: 'bbb',
            }],
    });
    const mockSendVerCodeFun = jest.fn().mockResolvedValue({ data: { value: 'test123' } });
    const mockSuccessMsgFun = jest.fn();
    const mockErrorMsgFun = jest.fn();
    // const mockResetFun = jest.fn();
    // const mockRunFun = jest.fn();
    const mockPushFun = jest.fn();
    const mockSubmitFun = jest.fn();
    const mockPutFun = jest.fn().mockImplementation(() => {
        return Promise.resolve({});
    });
    const mockEmitFun = jest.fn();
    global._mockTime = 0;
    const getBaseWrapperOptions = ({ refuseType, contractIds, sendVerCode } = { refuseType: 'REVOKE_CANCEL', contractIds: '123', sendVerCode: mockSendVerCodeFun }) => {
        return {
            data() {
                return {
                    phoneNotice: {
                        code: '123',
                    },
                    sendType: {
                        tyep: 'S',
                    },
                    mailNotice: {
                        code: '123',
                    },
                    otherReason: [
                        { checkFlag: true },
                    ],
                    rejectReasonList: [
                        { reasonType: 6 },
                    ],
                    cancel: {
                        show: true,
                        signPass: '',
                        reason: '',
                        rule: {
                            signPass: [
                                { message: '', trigger: 'blur' },
                                { min: 6, max: 6, message: '', trigger: 'blur' },
                            ],
                        },
                        refuseMaxLength: 30,
                        verifyCode: '',
                    },
                    checkFlag: true,
                    rejectRadio: 0,
                    verifyKey: true,
                    signing: false,
                };
            },
            directives: {
                va: {},
            },
            stubs: {
                RouterLink: RouterLinkStub,
                // CountDown:
                //     localVue.component(
                //         'CountDown', {
                //             template: '<div></div>',
                //             data() {
                //                 return {
                //                     time: global._mockTime,
                //                 };
                //             },
                //             methods: {
                //                 reset: mockResetFun,
                //                 run: mockRunFun,
                //             },
                //         },
                //     ),
            },
            propsData: {
                params: {
                    refuseType: refuseType,
                    dialogTitle: 'testTitle',
                    contractIds: contractIds,
                    selectedLine: {
                        contractId: '123',
                    },
                    sendOperateHttpPoint: jest.fn(),
                },
            },
            mocks: {
                $i18n: {
                    locale: 'zh',
                },
                GLOBAL: {
                    rootPathName: 'mockPath',
                },
                $http: {
                    get: mockGetFun,
                    put: mockPutFun,
                    sendVerCode: sendVerCode,
                },
                $route: {
                    query: {
                        contractId: '123',
                    },
                    fullPath: 'testPath',
                },
                $MessageToast: {
                    success: mockSuccessMsgFun,
                    error: mockErrorMsgFun,
                },
                $refs: {
                    btn: {
                        reset: jest.fn(),
                    },
                },
                $router: {
                    push: mockPushFun,
                },
                $validation: {
                    submitValidate: mockSubmitFun,
                },
                $hybrid: {
                    offlineTip: jest.fn().mockResolvedValue('test'),
                },
                $emit: mockEmitFun,
            },
        };
    };
    test('拒签合同页面数据初始化', async() => {
        const wrapper = createWrapper(cancelContract, store, cloneDeep(getBaseWrapperOptions({ refuseType: 'REJECT' })));
        await flushPromises();
        expect(mockGetFun).toHaveBeenCalledWith('/contract-api/contracts/refuse-reasons?contractId=123');
        expect(wrapper.vm.otherReason).toEqual([{ 'checkFlag': true }, { id: '66', 'reasonType': 6 }]);
        expect(wrapper.vm.isRejectOpt).toBeTruthy();
        wrapper.vm.rejectCheckbox = ['11', '22'];
        await flushPromises();
        expect(wrapper.vm.rejectReasonResultList).toEqual([{
            reasonType: '1',
            refuseReason: 'aaa',
        }, {
            reasonType: '2',
            refuseReason: 'bbb',
        }]);
        // expect(mockGetFun).toHaveBeenCalledWith('/users/notifications');
        // expect(wrapper.vm.mailNotice).toEqual({ 'code': '<EMAIL>', 'notificationId': '2763795238253234183', 'type': 1, 'userId': '2763795238085462018' });
        // expect(wrapper.vm.phoneNotice).toEqual({ 'code': '456', 'notificationId': '2764418947246725124', 'type': 2, 'userId': '2763795238085462018' });
        // expect(wrapper.vm.notice).toEqual('456');
        // expect(wrapper.vm.sendType).toEqual({ 'type': 'S' });
    });
    // test('点击发送验证码', async() => {
    //     let wrapper = createWrapper(cancelContract, store, cloneDeep(getBaseWrapperOptions({
    //         contractIds: '123',
    //         sendVerCode: mockSendVerCodeFun,
    //     })));
    //     wrapper.vm.handleClickSendCode();
    //     jest.runAllTimers();
    //     expect(mockRunFun).toHaveBeenCalled();
    //     expect(wrapper.vm.countDownDisabled).toBeFalsy();
    //     expect(mockSendVerCodeFun).toHaveBeenCalledWith({ 'bizTargetKey': '123', 'code': 'B008', 'sendType': 'S' });
    //     await flushPromises();
    //     expect(mockSuccessMsgFun).toHaveBeenCalledWith('cancelContract.sendSucc');
    //
    //     // mock sendVerCode 返回的状态为reject;
    //     const sendVerCodeFun = jest.fn().mockRejectedValue('');
    //     wrapper = createWrapper(cancelContract, store, cloneDeep(getBaseWrapperOptions({ sendVerCode: sendVerCodeFun })));
    //     wrapper.vm.handleClickSendCode();
    //     await flushPromises();
    //     expect(mockResetFun).toHaveBeenCalled();
    // });

    // test('点击语音验证码', () => {
    //     const wrapper = createWrapper(cancelContract, store, cloneDeep(getBaseWrapperOptions()));
    //     wrapper.vm.handleClickVoice();
    //     expect(wrapper.vm.sendType).toEqual({ 'type': 'V' });
    //     expect(wrapper.vm.notice).toEqual('123');
    // });

    // test('点击邮箱或者电话', async() => {
    //     const wrapper = createWrapper(cancelContract, store, cloneDeep(getBaseWrapperOptions()));
    //     wrapper.vm.handleClickMailAndPhone();
    //     expect(wrapper.vm.sendType).toEqual({ 'type': 'E' });
    //     expect(wrapper.vm.notice).toEqual('123');
    //
    //     wrapper.setData({ sendType: { type: 'E' } });
    //     await flushPromises();
    //     expect(wrapper.vm.sendType).toEqual({ 'type': 'S' });
    //     expect(wrapper.vm.notice).toEqual('456');
    // });

    // test('计时器时间大于零的场景', () => {
    //     global._mockTime = 10;
    //     const wrapper = createWrapper(cancelContract, store, cloneDeep(getBaseWrapperOptions()));
    //     wrapper.vm.handleClickVoice();
    //     wrapper.vm.handleClickMailAndPhone();
    //     expect(mockErrorMsgFun).toHaveBeenCalledWith('cancelContract.sendInternalErr');
    // });
    test('点击忘记密码', () => {
        const wrapper = createWrapper(cancelContract, store, cloneDeep(getBaseWrapperOptions()));
        wrapper.vm.handleForgetSignPass();
        expect(mockPushFun).toHaveBeenCalledWith('mockPath/resetSignPassword?userAccount=123');
        expect(global.localStorage.ForgetPassReturnSignHref).toEqual('testPath');
    });

    test('点击拒签', () => {
        const wrapper = createWrapper(cancelContract, store, cloneDeep(getBaseWrapperOptions({ refuseType: 'REJECT' })));
        wrapper.vm.handleRejectNext();
        expect(wrapper.vm.isMust).toBeFalsy();
        wrapper.setData({ otherReason: [{ checkFlag: false }] });
        expect(wrapper.vm.rejectReasonDialog).toBeTruthy();
        expect(wrapper.vm.rejectConfirmDialog).toBeFalsy();
    });

    test('批量撤回合同', async() => {
        const wrapper = createWrapper(cancelContract, store, getBaseWrapperOptions({ refuseType: 'REVOKE_CANCEL' }));
        // 多选
        wrapper.vm.params.selectedType = 'part';
        wrapper.vm.handleCancelContract();
        await flushPromises();
        expect(openPageAfterAjax).toBeCalledWith('/sign-flow/doc-manage/batch-log');
    });

    test('单份撤回合同', async() => {
        let wrapper = createWrapper(cancelContract, store, getBaseWrapperOptions({ refuseType: 'REVOKE_CANCEL', contractIds: '' }));

        await flushPromises();
        wrapper.vm.handleCancelContract();
        await flushPromises();
        expect(mockPutFun).toHaveBeenCalledWith('/contract-api/contracts/123/break', { 'reasonType': 0, 'refuseReason': '', 'refuseType': 'REVOKE_CANCEL', 'signPass': '', 'verifyCode': '', 'verifyKey': true, 'verifyWay': '', refuseReasons: [] });
        await flushPromises();
        expect(mockSuccessMsgFun).toHaveBeenCalledWith({ 'iconClass': 'el-icon-ssq-qianyuewancheng', 'message': 'cancelContract.succ' });
        expect(mockEmitFun).toHaveBeenCalledWith('refreshTable');
        expect(mockEmitFun).toHaveBeenCalledWith('close');

        // mock接口失败的情况
        const sendVerCode = jest.fn().mockRejectedValue('mockFailData');
        wrapper = createWrapper(cancelContract, store, getBaseWrapperOptions({ refuseType: 'REVOKE_CANCEL', contractIds: '123', sendVerCode: sendVerCode }));
        await flushPromises();
        expect(wrapper.vm.signing).toBeFalsy();
    });

    test('v-va指令存在时校验', async() => {
        baseStoreOptions.getters.SIGN_SECOND_CHECK = jest.fn().mockImplementation(() => {
            return true;
        });
        const storeOptions = cloneDeep(baseStoreOptions);
        const store = createStore(storeOptions);
        await flushPromises();
        const wrapper = createWrapper(cancelContract, store, getBaseWrapperOptions({ refuseType: 'REVOKE_CANCEL', contractIds: '' }));
        wrapper.vm.handleCancelContract();
        expect(wrapper.vm.SIGN_SECOND_CHECK).toBeTruthy();
        expect(mockSubmitFun.mock.calls[0][0]).toEqual('cancel');
        expect(mockSubmitFun.mock.calls[0][1]).toEqual({ 'reason': '', 'refuseMaxLength': 30, 'rule': { 'signPass': [{ 'message': '', 'trigger': 'blur' }, { 'max': 6, 'message': '', 'min': 6, 'trigger': 'blur' }] }, 'show': true, 'signPass': '', 'verifyCode': '' });
    });
});
