<template>
    <div class="risk-judge-popup__agent">
        <div class="risk-judge-popup__agent-body">
            <div class="risk-judge-popup__agent-body-box">
                <div class="risk-judge-popup__agent-body-role" v-if="!docAnalysisId && !contractId">
                    <div
                        class="risk-judge-popup__agent-body-role-icon robot"
                    >
                        <div class="hubble"></div>
                    </div>
                    <div
                        class="risk-judge-popup__agent-body-role-content"
                    >
                        <div
                            class="content-container ai"
                        >
                            <p>{{ $t('agent.uploadText') }}</p>
                        </div>
                    </div>
                </div>
                <div class="risk-judge-popup__agent-body-role" v-if="docAnalysisId && !rightRiskReady">
                    <div
                        class="risk-judge-popup__agent-body-role-icon robot"
                    >
                        <div class="hubble"></div>
                    </div>
                    <div
                        class="risk-judge-popup__agent-body-role-content"
                    >
                        <div
                            class="content-container ai"
                        >
                            <p>{{ $t('agent.startTips') }}</p>
                        </div>
                    </div>
                </div>
                <div class="risk-judge-popup__agent-body-role" v-if="riskList.length > 1">
                    <div
                        class="risk-judge-popup__agent-body-role-icon robot"
                    >
                        <div class="hubble"></div>
                    </div>
                    <div
                        class="risk-judge-popup__agent-body-role-content"
                    >
                        <div
                            class="content-container ai"
                        >
                            <VueMarkdown
                                class="ai"
                                :source="chooseTips"
                            ></VueMarkdown>
                        </div>
                        <div class="user-operate">
                            <div
                                class="user-operate-checkbox"
                                :class="documentId ?
                                    documentId === file.documentId ?
                                        'checked' :
                                        'unchecked' :
                                    ''"
                                v-for="file in riskList"
                                :key="file.documentId"
                                @click="initOrRecord(file)"
                            >
                                <div
                                    class="symbol"
                                    v-if="documentId === file.documentId"
                                >
                                    <i class="el-icon-ssq-selected"></i>
                                </div>
                                {{ file.fileName }}
                            </div>
                        </div>
                    </div>
                </div>
                <template v-for="(msg, num) in historyAnalysisResults">
                    <div class="risk-judge-popup__agent-body-role" :key="msg.messageId" v-if="msg.textContent && msg.textContent !== ''">
                        <div
                            class="risk-judge-popup__agent-body-role-icon"
                            :class="msg.roleType === 'AI' ? 'robot' :'human'"
                        >
                            <div class="hubble" v-if="msg.roleType === 'AI' && msg.textContent !== ''"></div>
                            <i class="el-icon-ssq-yonghu" v-if="msg.roleType === 'USER' && msg.textContent !== ''" />
                        </div>
                        <div
                            class="risk-judge-popup__agent-body-role-content"
                        >
                            <div
                                class="content-container"
                                :class="msg.roleType === 'AI' ? 'ai' :'user'"
                            >
                                <DeepThink :content="msg.reasoning" v-if="msg.roleType === 'AI' && msg.reasoning"></DeepThink>
                                <VueMarkdown
                                    class="risk-judge-popup-line"
                                    :source="getContent(msg.textContent)"
                                ></VueMarkdown>
                                <template v-if="msg.roleType === 'AI' && msg.riskWpsBuffer && msg.riskWpsBuffer.originalContent">
                                    <ExpandableBox :open="false" :buffer="msg.riskWpsBuffer"></ExpandableBox>
                                </template>
                            </div>
                            <div class="user-operate">
                                <div
                                    class="user-operate-checkbox"
                                    :class="getCheckOrNot({btn, num}) ?
                                        'checked' :
                                        num === historyAnalysisResults.length - 1 ?
                                            '' :
                                            'unchecked'"
                                    v-for="(btn, index) in msg.selectedOptions"
                                    :key="index"
                                    @click="submitChoose(btn, num + 1, true)"
                                >
                                    <div
                                        class="symbol"
                                        v-if="getCheckOrNot({btn, num}) && btn !== $t('agent.satisfy') && btn !== $t('agent.dissatisfy')"
                                    >
                                        <i class="el-icon-ssq-selected"></i>
                                    </div>
                                    <i
                                        class="satisfy-symbol el-icon-ssq-xiaolian"
                                        v-if="btn === $t('agent.satisfy')"
                                    ></i>
                                    <i
                                        class="satisfy-symbol el-icon-ssq-kulian"
                                        v-if="btn === $t('agent.dissatisfy')"
                                    ></i>
                                    {{ btn }}
                                </div>
                            </div>
                            <div v-if="!needAnalysis && isCustom && num === historyAnalysisResults.length - 1" class="user-operate-custom-input">
                                <el-input class="dialog-input"
                                    resize="none"
                                    :autosize="{ minRows: 4, maxRows: 10}"
                                    type="textarea"
                                    v-model="message"
                                    :placeholder="$t('agent.custom')"
                                ></el-input>
                                <el-button size="small" type="primary" :disabled="!message" @click="submitMessage(message)">{{ $t('agent.submit') }}</el-button>
                            </div>
                        </div>
                    </div>
                </template>
                <div class="risk-judge-popup__agent-body-role" v-if="needAnalysis">
                    <div class="risk-judge-popup__agent-body-role-icon robot">
                        <div class="hubble"></div>
                    </div>
                    <div class="risk-judge-popup__agent-body-role-content">
                        <div
                            class="content-container ai"
                        >
                            <DeepThink :isThinking="isThinking" isOpen :content="currentReasoning" v-if="currentReasoning"></DeepThink>
                            <VueMarkdown
                                class="risk-judge-popup-line"
                                :source="responseContent ? responseContent : analysisMsg"
                                :config="markdownConfig"
                            ></VueMarkdown>
                            <template v-if="riskWpsBuffer.originalContent">
                                <ExpandableBox :buffer="riskWpsBuffer" :currentTab="inProposal ? $t('agent.revision') : ''"></ExpandableBox>
                            </template>
                        </div>
                        <div class="user-operate">
                            <span v-show="inUserInput || inWpsChange">{{ btnMsg }}</span>
                            <div
                                class="user-operate-checkbox"
                                :class="{'checked': btn === '去小程序查看' || btn === '支付解锁完整的风险判断报告'}"
                                v-for="(btn, index) in operateBtns.choosen"
                                v-show="btn !== '去小程序查看'"
                                :key="index"
                                @click="submitChoose(btn, null, true)"
                            >
                                <i
                                    class="satisfy-symbol el-icon-ssq-xiaolian"
                                    v-if="btn === $t('agent.satisfy')"
                                ></i>
                                <i
                                    class="satisfy-symbol el-icon-ssq-kulian"
                                    v-if="btn === $t('agent.dissatisfy')"
                                ></i>
                                {{ btn }}
                            </div>
                            <div
                                class="user-operate-checkbox"
                                v-if="
                                    operateBtns.choosen &&
                                        operateBtns.choosen.length &&
                                        !['agreementRiskJudgement', 'jumpToWeChatMiniProgram', 'needPay', 'riskJudgementPlan', 'customRuleUsing'].includes(operateBtns.userInputHelperType) ||
                                        operateBtns.userInputHelperType === 'agreementTerminologyExtract'
                                "
                                @click="showInput"
                                :class="{'checked': isElse}"
                            >
                                {{ $t('agent.others') }}
                            </div>
                        </div>
                        <div v-if="isElse">
                            <el-input class="dialog-input" v-model="message" :placeholder="operateBtns.tipsForOtherOptions">
                                <el-button slot="append" size="small" type="primary" @click="submitMessage(message)">{{ $t('agent.submit') }}</el-button>
                            </el-input>
                        </div>
                        <div v-if="isCustom" class="user-operate-custom-input">
                            <el-input class="dialog-input" type="textarea" v-model="message" :placeholder="operateBtns.tipsForOtherOptions"></el-input>
                            <el-button size="small" type="primary" @click="submitMessage(message)">{{ $t('agent.submit') }}</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="content-mask" v-if="preventClick"></div>
        <div class="risk-judge-popup__agent-footer">
            <div class="footer-operate">
                <el-checkbox v-model="autoReply" shape="square">
                    {{ agentType === 'EXTRACT' ? $t('agent.autoExtract') : $t('agent.autoRisk') }}
                </el-checkbox>
                <el-button
                    @click="rejudge"
                    :disabled="historyAnalysisResults.length === 0"
                >
                    {{ $t('agent.reJudge') }}
                </el-button>
            </div>
            <p>{{ $t('agent.aiGenerated') }}</p>
        </div>
    </div>
</template>

<script>
import VueMarkdown from 'vue-markdown';
import { mapState } from 'vuex';
import { hubbleWps } from 'src/mixins/hubbleWps.js';
import ExpandableBox from './expandableBox';
import DeepThink from './deepThink';
import {
    confirmOperateType,
    hubbleJudgementDetail,
    initialAnalysis,
    fetchAppletUrl,
} from 'src/api/docManage/judgeRisk';
export default {
    components: {
        VueMarkdown,
        ExpandableBox,
        DeepThink,
    },
    mixins: [hubbleWps],
    props: {
        docAnalysisId: {
            type: String,
            default: '',
        },
        rightRiskReady: {
            type: Boolean,
            default: false,
        },
        hasHistoryEvent: {
            type: Boolean,
            default: false,
        },
        contractId: {
            type: String,
            default: '',
        },
        receiverId: {
            type: String,
            default: '',
        },
        riskList: {
            type: Array,
            default: () => [],
        },
        agentType: {
            type: String,
            default: '',
        },
        productType: {
            type: Number,
            default: 15, // 15比对，16翻译
        },
        productConfig: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            isInit: false,
            agreementAnalysisType: '',
            historyAnalysisResults: [],
            analysisId: '',
            documentId: '',
            message: '',
            responseContent: '',
            currentReasoning: '',
            operateBtns: {},
            autoReply: false,
            needAnalysis: false,
            userName: '',
            inResponse: false,
            isThinking: false,
            inAnalysis: false,
            inWpsChange: false,
            inOriginal: false,
            inProposal: false,
            wpsChangeMsg: '',
            riskWpsBuffer: { originalContent: '', proposalContent: '' },
            wpsReplaced: false,
            inUserInput: false,
            tagBuffer: '',
            streamBuffer: '',
            reader: null,
            isElse: false,
            isCustom: false,
            markdownConfig: {
                html: true, // 允许在 Markdown 中使用 HTML 标签
                breaks: true, // 转换换行符为 <br> 标签
            },
            analysisInterval: null,
            analysisMsg: '',
            btnMsg: '',
            preventClick: false,
            timer: null,
        };
    },
    computed: {
        ...mapState({
            commonHeaderInfo: state => state.commonHeaderInfo,
        }),
        getCheckOrNot() {
            return ({ btn, num }) => {
                const type = {
                    [this.$t('agent.extractTitle')]: 'EXTRACT',
                    [this.$t('agent.riskTitle')]: 'RISK_JUDGEMENT',
                    [this.$t('agent.deepInference')]: 'RISK_JUDGEMENT_REASONING',
                }[btn];
                if (this.historyAnalysisResults[num + 1]) {
                    return this.historyAnalysisResults[num + 1].textContent === btn;
                } else if (type) {
                    return this.agreementAnalysisType === type;
                } else {
                    return false;
                }
            };
        },
        chooseTips() {
            if (this.agentType === 'EXTRACT') {
                return this.$t('agent.chooseExtract');
            } else {
                return this.$t('agent.chooseRisk');
            }
        },
    },
    watch: {
        needAnalysis(val) {
            const dotsArray = ['.', '..', '...'];
            let index = 0;
            if (val) {
                this.analysisInterval = setInterval(() => {
                    this.analysisMsg = `${this.$t('agent.analyzing')}${dotsArray[index]}`;
                    this.btnMsg = `${this.inWpsChange ? this.$t('agent.advice') : this.$t('agent.options')}${dotsArray[index]}`;
                    index = index === 2 ? 0 : index + 1;
                }, 500);
            } else {
                this.analysisMsg = '';
                clearInterval(this.analysisInterval);
            }
        },
    },
    methods: {
        handleHistoryData(analysisMessages) {
            return analysisMessages.map(msg => {
                if (msg.roleType === 'AI') {
                    msg.riskWpsBuffer = this.handleWpsData(msg.textContent);
                    msg.reasoning && (msg.reasoning = this.getReasoning(msg.reasoning));
                }
                return msg;
            });
        },
        getReasoning(reasoning) {
            const regex = /<hubbleThink>([\s\S]*?)<\/hubbleThink>/;
            const msg = reasoning.match(regex);
            if (msg) {
                return msg[1].replace(/^\n|\n$/g, '').trim().replace(/\n\s*/g, '\n');
            } else {
                return reasoning;
            }
        },
        formatString(str) {
            return str
                .replace(/```/g, '')
                .replace(/\\n/g, '\n')
                .trim()
                .split('\n')
                .map(line => line.trim())
                .join('\n');
        },
        initOrRecord(document) {
            if (this.analysisId) {
                return;
            }
            this.documentId = document.documentId;
            this.historyAnalysisResults = [];
            this.historyAnalysisResults.push({
                roleType: 'USER',
                textContent: document.fileName,
            });
            if (document.analysisInfos && document.analysisInfos.length) {
                const analysis = document.analysisInfos.find(item =>
                    item.analysisType === this.agentType,
                );
                if (analysis) {
                    this.analysisId = analysis.analysisId;
                    this.getAnalysisRecord(this.analysisId);
                } else {
                    this.analysisId = '';
                    this.init();
                }
            } else {
                this.analysisId = '';
                this.init();
            }
        },
        init() {
            initialAnalysis(this.contractId, this.receiverId, {
                documentId: this.documentId,
                paymentPlanType: null,
                approverAnnotationType: this.agentType,
            }).then(async res => {
                this.analysisId = res.data;
                this.needAnalysis = true;
                await this.sendMessage();
            });
        },
        initAnalyze() {
            this.isInit = true;
            this.historyAnalysisResults = [{
                messageId: '',
                textContent: this.$t('agent.selectFunc'),
                selectedOptions: [
                    this.$t('agent.riskTitle'),
                    this.$t('agent.deepInference'),
                    this.$t('agent.extractTitle'),
                ],
                reasoning: '',
                roleType: 'AI',
                riskWpsBuffer: { originalContent: '', proposalContent: '' },
            }];
        },
        getAnalysisRecord(analysisId) {
            this.needAnalysis = false;
            if (!this.analysisId) {
                this.analysisId = analysisId;
            }
            hubbleJudgementDetail(analysisId).then(res => {
                if (res.data) {
                    // this.$store.state.hubble.allowEdit = res.data.allowEdit;
                    // this.$store.state.hubble.multiVersionFileId = res.data.editModeExtensionInfo?.multiVersionFileId;
                    this.agreementAnalysisType = res.data.agreementAnalysisType;
                    if (!res.data.agreementAnalysisType) {
                        return this.initAnalyze();
                    }
                    // this.$store.state.hubble.documentType = this.agreementAnalysisType === 'RISK_JUDGEMENT' && this.allowEdit ? 'wps' : 'doc';
                    const analysisMessages = this.handleHistoryData(res.data.analysisMessages);
                    const lastOne = analysisMessages[analysisMessages.length - 1];
                    lastOne && (this.riskWpsBuffer = (lastOne.riskWpsBuffer || { originalContent: '', proposalContent: '' }));
                    this.historyAnalysisResults = this.historyAnalysisResults.concat(res.data.analysisMessages);
                    if (res.data.retry?.ifRetry) {
                        this.needAnalysis = true;
                        this.message = res.data.retry.retryUserMessage || '';
                        this.sendMessage();
                        return;
                    }
                }
            }).finally(() => {
                this.scrollToEnd();
            });
        },
        handleStreamResponse(totalStream) {
            let totalResponse = totalStream;
            const parser = new DOMParser();
            let doc = parser.parseFromString(totalResponse, 'text/html');
            const hasClosingTag = (tag) => totalResponse.includes(`</${tag}>`);
            const hubbleThink = doc.querySelector('hubbleThink');
            if (hubbleThink) {
                this.isThinking = true;
                this.currentReasoning = this.formatString(hubbleThink.textContent);
                if (hasClosingTag('hubbleThink')) {
                    this.isThinking = false;
                    totalResponse = totalResponse.replace(/<hubbleThink>[\s\S]*?<\/hubbleThink>/, '');
                    doc = parser.parseFromString(totalResponse, 'text/html');
                } else {
                    return;
                }
            }

            const response = doc.querySelector('response');
            response && (this.inResponse = true);

            const needPay = doc.querySelector('needPay');
            if (needPay && hasClosingTag('needPay')) {
                setTimeout(() => {
                    this.$emit('showCharge');
                }, 1000);
                return;
            }

            const analysis = doc.querySelector('analysis');
            if (analysis) {
                this.inAnalysis = true;
                this.responseContent = this.formatString(analysis.textContent);
                if (hasClosingTag('analysis')) {
                    this.inAnalysis = false;
                }
            }

            const revisionContents = doc.querySelector('revisionContents');
            if (revisionContents) {
                this.inWpsChange = true;
                const original = doc.querySelector('original');
                if (original) {
                    this.inOriginal = true;
                    this.riskWpsBuffer.originalContent = this.formatString(original.textContent);
                    if (hasClosingTag('original')) {
                        this.inOriginal = false;
                    }
                }
                const proposal = doc.querySelector('proposal');
                if (proposal) {
                    this.inProposal = true;
                    this.riskWpsBuffer.proposalContent = this.formatString(proposal.textContent);
                    if (hasClosingTag('proposal')) {
                        this.inProposal = false;
                    }
                }
                if (hasClosingTag('revisionContents')) {
                    // if (this.isWpsMode && !this.wpsReplaced) {
                    //     this.replaceText();
                    //     this.wpsReplaced = true;
                    // }
                    this.inWpsChange = false;
                }
            }
            const btnTag = doc.querySelector('userInputHelper');
            if (btnTag) {
                this.inUserInput = true;
                if (hasClosingTag('userInputHelper')) {
                    this.inUserInput = false;
                    const userInputHelper = JSON.parse(this.formatString(btnTag.textContent));
                    const choosenKey = userInputHelper.contractTypes ||
                        userInputHelper.stakeholders ||
                        userInputHelper.terminologyProvidedToUsers ||
                        userInputHelper.askUserSatisfied ||
                        userInputHelper.optionsProvidedToUsers ||
                        [];
                    this.operateBtns = Object.assign(
                        {},
                        userInputHelper,
                        { choosen: choosenKey },
                    );
                }
            }
        },
        async sendMessage() {
            const data = {
                userMessage: `${this.message}${this.wpsChangeMsg}`,
                autoReply: this.autoReply,
            };
            this.responseContent = '';
            this.currentReasoning = '';
            this.operateBtns = {};
            this.preventClick = true;
            this.riskWpsBuffer = { originalContent: '', proposalContent: '' };
            this.wpsReplaced = false;
            await fetch(`/web/hubble/agreement-analysis/event-stream/${this.analysisId}/submit-message`, {
                method: 'POST',
                headers: {
                    'authorization': 'bearer ' + this.$cookie.get('access_token'),
                    'content-type': 'application/json',
                },
                body: JSON.stringify(data),
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP 错误，状态码: ${response.status}`);
                }
                this.reader = response.body.getReader();
                const that = this;
                let totalResponse = '';
                return new ReadableStream({
                    start(controller) {
                        function push() {
                            that.reader.read().then(({ done, value }) => {
                                if (done) {
                                    controller.close();
                                    const historyLength = JSON.parse(JSON.stringify(that.historyAnalysisResults)).length;
                                    that.preventClick = false;
                                    if (that.autoReply && Object.keys(that.operateBtns).length) {
                                        if (that.operateBtns.userInputHelperType === 'stakeholdersJudgement') {
                                            that.autoReply = false;
                                        } else if (that.operateBtns.userInputHelperType === 'scannedCopyRecognized') {
                                            that.submitChoose('');
                                        } else if (that.operateBtns.userInputHelperType === 'needPay') {
                                            return;
                                        } else if (!that.operateBtns.choosen.length && that.operateBtns.userInputHelperType === 'contractInterpretation') {
                                            that.submitChoose('');
                                        } else {
                                            const msg = that.operateBtns.choosen[0];
                                            that.submitChoose(msg);
                                        }
                                    } else if (Object.keys(that.operateBtns).length &&
                                        that.operateBtns.userInputHelperType === 'scannedCopyRecognized'
                                    ) {
                                        that.submitChoose('');
                                    } else if (Object.keys(that.operateBtns).length &&
                                        that.operateBtns.userInputHelperType === 'needPay'
                                    ) {
                                        return;
                                    } else if (that.operateBtns.userInputHelperType === 'stakeholdersJudgement') {
                                        that.autoReply = false;
                                    } else if (!Object.keys(that.operateBtns).length && historyLength <= 1) {
                                        that.submitChoose('');
                                    }
                                    return;
                                }
                                const text = new TextDecoder().decode(value);
                                const lines = text.split('\n');
                                lines.forEach(line => {
                                    if (line.trim() !== '') {
                                        let result = line;
                                        if (line.indexOf('data:') !== -1) {
                                            result = line.slice(5);
                                        }
                                        totalResponse += result;
                                    }
                                });
                                that.handleStreamResponse(totalResponse);
                                that.scrollToEnd();
                                push();
                            });
                        }
                        push();
                    },
                });
            }).catch(err => {
                console.log('Fetch Error: ', err);
            }).finally(() => {
                this.message = '';
            });
        },
        openWxMiniProgram(from) {
            const currentAccount = this.commonHeaderInfo?.platformUser?.account;
            fetchAppletUrl(101, 'views/riskJudge/index',
                           `account=${currentAccount}&isFromH5Home=1&accessFrom=${from}`, 'release')
                .then((res) => {
                    window.location.href = res.data?.url;
                });
        },
        scrollToEnd() {
            this.$nextTick(() => {
                const agentBody = document.querySelector('.risk-judge-popup__agent-body-box');
                if (agentBody) {
                    agentBody.scrollIntoView({
                        behavior: 'instant',
                        block: 'end',
                    });
                }
            });
        },
        submitMessage(val) {
            if (val.trim() !== '') {
                this.submitChoose(val);
            } else {
                this.$MessageToast.warning(this.$t('agent.inputTips'));
            }
        },
        chooseAnalysisType(val) {
            const type = {
                [this.$t('agent.extractTitle')]: 'EXTRACT',
                [this.$t('agent.riskTitle')]: 'RISK_JUDGEMENT',
                [this.$t('agent.deepInference')]: 'RISK_JUDGEMENT_REASONING',
            }[val];
            confirmOperateType(this.analysisId, type).then(() => {
                this.agreementAnalysisType = type;
                this.needAnalysis = true;
                this.isInit = false;
                if (this.hasHistoryEvent) {
                    this.$emit('updateHistory');
                }
                this.sendMessage();
            }).catch((err) => {
                this.$MessageToast.error(err.response?.data?.message);
                setTimeout(() => {
                    this.$emit('showCharge');
                }, 1000);
            });
        },
        submitChoose(val, index, fromClick) {
            if (this.isInit) {
                return this.chooseAnalysisType(val);
            }
            if (
                [
                    this.$t('agent.riskTitle'),
                    this.$t('agent.deepInference'),
                    this.$t('agent.extractTitle'),
                ].includes(val)
            ) {
                return;
            }
            if (val === this.$t('filter.yes')) {
                this.isCustom = true;
                return;
            }
            if (val === '去小程序查看') {
                !fromClick && this.scrollToEnd();
                fromClick && this.openWxMiniProgram('analysisBottom');
                return;
            }
            if (!this.autoReply && this.preventClick) {
                return;
            }
            this.isCustom = false;
            this.isElse = false;
            if (index && index === this.historyAnalysisResults.length || !index) {
                this.message = val;
                if (this.responseContent) {
                    this.historyAnalysisResults.push({
                        roleType: 'AI',
                        textContent: this.responseContent,
                        selectedOptions: this.operateBtns.choosen,
                        riskWpsBuffer: this.riskWpsBuffer,
                        reasoning: this.currentReasoning,
                    });
                }
                if (val) {
                    this.historyAnalysisResults.push({
                        roleType: 'USER',
                        textContent: val,
                    });
                }
                this.$nextTick(() => {
                    this.sendMessage();
                    const agentBody = document.querySelector('.risk-judge-popup__agent-body-box');
                    if (agentBody) {
                        agentBody.scrollIntoView({
                            behavior: 'instant',
                            block: 'end',
                        });
                    }
                });
                this.needAnalysis = true;
            } else {
                return;
            }
        },
        getContent(content) {
            const regex = /<analysis>([\s\S]*?)<\/analysis>/;
            const msg = content.match(regex);
            if (msg) {
                return msg[1].replace(/^\n|\n$/g, '').trim().replace(/\n\s*/g, '\n').replace(/```/g, '\n');
            } else {
                return (content);
            }
        },
        showInput() {
            this.isElse = true;
            this.scrollToEnd();
        },
        getPayResult() {
            this.historyAnalysisResults = [];
            if (this.riskList.length > 1) {
                const file = this.riskList.find(file => file.documentId === this.documentId);
                this.historyAnalysisResults.push({
                    roleType: 'USER',
                    textContent: file.fileName,
                });
            }
            this.getAnalysisRecord(this.analysisId);
        },
        reset() {
            this.historyAnalysisResults = [];
            this.operateBtns = {};
            this.needAnalysis = false;
            this.preventClick = false;
            this.analysisId = '';
            this.agreementAnalysisType = '';
            this.isCustom = false;
            this.inResponse = false;
            this.isThinking = false;
            this.inAnalysis = false;
            this.inUserInput = false;
            if (this.reader) {
                this.reader.cancel();
                this.reader = null;
            }
        },
        rejudge() {
            this.$confirm(this.$t('agent.tipsContent'), this.$t('uploadFile.tip'), {
                confirmButtonText: this.$t('agent.confirm'),
                showCancelButton: true,
            }).then(() => {
                let fileInfo = {};
                if (this.historyAnalysisResults.length > 0 && this.riskList.length > 1) {
                    fileInfo = this.historyAnalysisResults[0];
                }
                this.reset();
                if (this.riskList.length > 1) {
                    this.historyAnalysisResults.push(fileInfo);
                }
                this.init();
            }).catch(() => {
                return;
            });
        },
    },
    mounted() {
        if (this.docAnalysisId) {
            this.analysisId = this.docAnalysisId;
            return;
        }
        let analysis = null;
        if (this.agentType === 'EXTRACT') {
            this.autoReply = false;
        }
        if (this.riskList.length === 1) {
            analysis = this.riskList[0].analysisInfos.find(item =>
                item.analysisType === this.agentType && item.analysisId,
            );
            this.documentId = this.riskList[0].documentId;
        }
        if (this.riskList.length === 1 && analysis) {
            this.analysisId = analysis.analysisId;
            this.getAnalysisRecord(this.analysisId);
        } else if (this.riskList.length === 1 && !analysis) {
            this.init();
        }
    },
    created() {},
    beforeDestroy() {
        if (this.reader) {
            this.reader.cancel();
            this.reader = null;
        }
    },
};
</script>

<style lang="scss" scoped>
.risk-judge-popup {
    * {
        box-sizing: border-box;
    }
    &__agent {
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        background-color: #FFF;
        &-body {
            flex-grow: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 10px 15px 0;
            .loading {
                text-align: center;
            }
        }
        .content-mask {
            position: absolute;
            width: 100%;
            height: calc(100% - 70px);
            background-color: rgba(255,255,255, 0);
            left: 0;
            top: 0;
        }
        &-body-role{
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            .content-container {
                flex-grow: 1;
                line-height: 20px;
                padding: 5px 10px;
                border-radius: 3px;
                user-select: text;
                .location {
                    font-size: 12px;
                    padding: 4px;
                    border-radius: 5px;
                    margin-left: 100%;
                    transform: translateX(-100%);
                    [dir="rtl"] & {
                        margin-left: 0;
                        transform: translateX(0);
                    }
                    span {
                        display: flex;
                        align-items: center;
                    }
                    .location-index {
                        display: inline-block;
                        margin-left: 4px;
                        border-radius: 50%;
                        font-size: 10px;
                        line-height: 13px;
                        width: 13px;
                        height: 13px;
                        transform: translateY(-1px);
                        background-color: #fff;
                        color: #098AEE;
                    }
                }
            }
            .ai {
                background-color: #E6F4FF;
            }
            .user {
                background-color: #F5F5F5;
            }
            &::v-deep ul, &::v-deep ol{
                padding-left: 18px;
                margin: 0;
                font-size: 0;
                [dir="rtl"] & {
                    padding-left: 0px;
                    padding-right: 18px;
                }
                li {
                    font-size: 14px;
                }
            }
            &::v-deep ol{
                li {
                    list-style: decimal;
                    li {
                        list-style: disc;
                        li {
                            list-style: circle;
                        }
                    }
                }
            }
            &::v-deep ul {
                li {
                    list-style: disc;
                    li {
                        list-style: circle;
                    }
                }
            }
            &::v-deep code {
                white-space: pre-line;
                word-break: break-all;
            }
            &-icon {
                width: 30px;
                height: 30px;
                font-size: 15px;
                padding: 7.5px;
                margin-right: 10px;
                border-radius: 3px;
                flex-shrink: 0;
                box-sizing: border-box;
                &.robot {
                    background-color: #098AEE;
                }
                &.human {
                    background-color: #E5E5E5;
                    color: #fff;
                }
                [dir="rtl"] & {
                    margin-right: 0px;
                    margin-left: 10px;
                }
                .hubble {
                    height: 16px;
                    width: 15px;
                    background-image: url("~img/hubble_icon.png");
                    background-size: 100%;
                }
            }
            &-content {
                flex-grow: 1;
                .dialog-input::v-deep .el-input-group__append {
                    background-color: #098AEE;
                    color: #fff;
                }
            }
            .user-operate {
                width: 100%;
                margin-top: 10px;
                display: flex;
                flex-wrap: wrap;
                &-checkbox {
                    padding: 4px 10px;
                    border-radius: 3px;
                    border: 1px solid #666;
                    line-height: 24px;
                    margin-bottom: 4px;
                    margin-right: 4px;
                    position: relative;
                    background: #fff;
                    color: #666;
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    .satisfy-symbol {
                        display: inline-block;
                        font-size: 14px;
                        margin-right: 5px;
                        [dir="rtl"] & {
                            margin-left: 5px;
                            margin-right: 0px;
                        }
                    }
                    .symbol {
                        position: absolute;
                        right: 0;
                        bottom: 0;
                        background: #098AEE;
                        color: #fff;
                        border-radius: 5px 0px 2px 0px;
                        display: flex;
                        align-items: center;
                        font-size: 4px;
                        width: 8px;
                        height: 8px;
                        padding-left: 1px;
                        i {
                            display: block;
                            margin: auto;
                        }
                    }
                    &.checked {
                        background: #fff;
                        border: 1px solid #098AEE;
                        color: #098AEE;
                    }
                    &.unchecked {
                        background: #E5E5E5;
                        border: 1px solid #E5E5E5;
                        color: #A6A6A6;
                    }
                }
                &-custom-input::v-deep {
                    position: relative;
                    border: 1px solid #e5e5e5;
                    border-radius: 5px;
                    padding: 10px 0 35px;
                    overflow: hidden;
                    .el-button {
                        position: absolute;
                        right: 5px;
                        bottom: 5px;
                        border-radius: 20px;
                    }
                    .el-textarea .el-textarea__inner{
                        border: none;
                        outline: none;
                        &:hover, &:focus, &:active{
                            border: none;
                            outline: none;
                            box-shadow: none;
                        }
                    }
                }
            }
        }
        &-footer {
            height: 70px;
            background-color: #fff;
            border-top: 1px solid #EEE;
            padding: 12px;
            flex-shrink: 0;
            box-sizing: border-box;
            .footer-operate {
                display: flex;
                align-items: center;
                justify-content: space-between;
                .el-button {
                    padding: 8px 14px;
                    font-size: 12px;
                }
            }
            p {
                font-size: 12px;
                font-weight: 400;
                line-height: 28px;
                color: #C9C7C7;
            }
        }
    }
}
</style>
